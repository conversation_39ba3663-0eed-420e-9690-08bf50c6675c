-- Deploy cm_tenant_db_RebaseSchema:2025-01-13-accounts_table_add_ie_staff to pg

BEGIN;
    INSERT INTO account (firstname, lastname, email, is_admin, about)
    VALUES ('<PERSON>', '<PERSON><PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON>', '<PERSON><PERSON><PERSON>', 'gem<PERSON>@imagineeverything.com', true, 'Imagine Everything Team'),
           ('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'se<PERSON><PERSON>@imagineeverything.com', true, 'Imagine Everything Team'),
           ('<PERSON><PERSON><PERSON>', '<PERSON>shmor<PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON>leb', 'Mo<PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON><PERSON>', '<PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON><PERSON>', '<PERSON><PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('<PERSON>', 'Situ', '<PERSON><PERSON>@imagineeverything.com', true, 'Imagine Everything Team'),
           ('<PERSON>', '<PERSON>', '<EMAIL>', true, 'Imagine Everything Team'),
           ('Richard', 'Keller', '<EMAIL>', true, 'Imagine Everything Team'),
           ('Jordan', 'Blazevich', '<EMAIL>', true, 'Imagine Everything Team'),
           ('Erin', 'Pickles', '<EMAIL>', true, 'Imagine Everything Team'),
           ('Don', 'Burks', '<EMAIL>', true, 'Imagine Everything Team')
    ON CONFLICT (email) DO UPDATE
        SET is_admin = true;
COMMIT;
