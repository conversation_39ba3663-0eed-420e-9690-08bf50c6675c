-- Deploy cm_tenant_db_DeploySchema:2025-02-13-content_add_tag_ids to pg

BEGIN;

-- disable triggers
ALTER TABLE public.content DISABLE TRIGGER ALL;

-- alter content
-- ALTER TABLE public.content DROP COLUMN IF EXISTS structure;
-- ALTER TABLE public.content DROP COLUMN IF EXISTS published;
-- ALTER TABLE public.content DROP COLUMN IF EXISTS approved;
-- ALTER TABLE public.content RENAME COLUMN created TO created_at;
-- ALTER TABLE public.content RENAME COLUMN updated TO updated_at;
-- ALTER TABLE public.content RENAME COLUMN owner TO created_by;
-- ALTER TABLE public.content RENAME COLUMN publisher TO updated_by;

ALTER TABLE public.content ADD COLUMN IF NOT EXISTS tags UUID[] DEFAULT '{}'::uuid[];
CREATE INDEX IF NOT EXISTS content_tags_idx ON public.content USING gin(tags);

UPDATE public.content c
SET tags = COALESCE(
        (SELECT array_agg(tag_id)
         FROM content_tag ct
         WHERE ct.content_id = c.id
         GROUP BY ct.content_id),
        '{}'::uuid[]
);

-- alter content_history
-- ALTER TABLE public.content_history DROP COLUMN IF EXISTS structure;
-- ALTER TABLE public.content_history DROP COLUMN IF EXISTS published;
-- ALTER TABLE public.content_history DROP COLUMN IF EXISTS approved;
-- ALTER TABLE public.content_history RENAME COLUMN created TO created_at;
-- ALTER TABLE public.content_history RENAME COLUMN updated TO updated_at;
-- ALTER TABLE public.content_history RENAME COLUMN owner TO created_by;
-- ALTER TABLE public.content_history RENAME COLUMN publisher TO updated_by;

ALTER TABLE public.content_history ADD COLUMN IF NOT EXISTS tags UUID[] DEFAULT '{}'::uuid[];

UPDATE public.content_history c
SET tags = COALESCE(
        (SELECT array_agg(tag_id)
         FROM content_tag ct
         WHERE ct.content_id = c.id
         GROUP BY ct.content_id),
        '{}'::uuid[]
           );

-- enable triggers
ALTER TABLE public.content ENABLE TRIGGER ALL;

COMMIT;
