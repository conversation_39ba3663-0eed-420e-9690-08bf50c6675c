-- Deploy cm_tenant_db_DeploySchema:2024-12-04-Suggestions to pg

BEGIN;

CREATE TABLE IF NOT EXISTS search_suggestions
(
    id            uuid         NOT NULL    DEFAULT uuid_generate_v4()
        constraint pk_search_suggestions primary key,

    name          text NOT NULL,
    items         text[]       NOT NULL    DEFAULT ARRAY []::text[],

    keywords       text[]       NOT NULL    DEFAULT ARRAY []::text[],
    exact_match     boolean      NOT NULL    DEFAULT false,
    row_tsvector tsvector GENERATED ALWAYS AS (
        setweight(to_tsvector('english', coalesce(array_to_string_immutable(keywords, ' '), '')), 'A')
        ) STORED,

    active        boolean      NOT NULL,

    publish_at    TIMESTAMP WITH TIME ZONE,
    expire_at     TIMESTAMP WITH TIME ZONE,

    created_at    TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by    uuid         NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by    uuid         NOT NULL
);

CREATE UNIQUE INDEX IF NOT EXISTS ux_search_suggestions_name ON search_suggestions (lower(name));
CREATE INDEX IF NOT EXISTS idx_search_suggestions_updated_at ON search_suggestions (updated_at);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_admin_search
    ON search_suggestions USING gin ((search_suggestions.id || ' '
                                          || search_suggestions.name || ' '
                                          || coalesce(array_to_string_immutable(search_suggestions.keywords, ' '), ' ') || ' '
                                          || coalesce(array_to_string_immutable(search_suggestions.items, ' '), ' ') || ' '
        ) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_search_suggestions_tsvector
    ON search_suggestions USING gin(row_tsvector)
    WHERE exact_match = false;
CREATE INDEX idx_search_suggestions_dates
    ON search_suggestions (publish_at, expire_at)
    WHERE active = true;

GRANT ALL ON TABLE search_suggestions to contentmanager_application_user;

COMMIT;
