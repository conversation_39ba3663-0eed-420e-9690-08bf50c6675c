-- Deploy cm_tenant_db_DeploySchema:ThirdPartyContentTable to pg

BEGIN;
CREATE TABLE third_party_content
(
    site_id uuid NOT NULL,
    service text NOT NULL,
    data jsonb DEFAULT '{}' NOT NULL,
    last_modified TIMESTAMP WITH TIME ZONE DEFAULT now(),
    CONSTRAINT pk_third_party_content PRIMARY KEY(site_id, service)
)WITH (
     OIDS=FALSE
     );

GRANT ALL ON TABLE third_party_content to contentmanager_application_user;

COMMIT;
