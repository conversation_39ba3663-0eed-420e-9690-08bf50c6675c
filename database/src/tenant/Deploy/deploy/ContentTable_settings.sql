-- Deploy cm_tenant_db_DeploySchema:ContentTable_settings to pg

BEGIN;

ALTER TABLE ONLY content ADD COLUMN IF NOT EXISTS settings jsonb DEFAULT '{}' NOT NULL;

ALTER TABLE ONLY content ADD CONSTRAINT ck_settings_priority check(
    CASE WHEN (settings->'priority') IS NOT NULL
        THEN (settings->>'priority') ~ '^(-)?[0-9]+\.?([0-9]+)?$'
    ELSE
        TRUE
    END
);

CREATE OR REPLACE FUNCTION check_content_content_type()
RETURNS TRIGGER AS
$$
BEGIN
    IF string_to_array(replace(replace(trim(both '[]' from (NEW.settings->>'classification')), '"', ''), ' ', ''), ',')::content_type[] IS NOT NULL THEN
        RETURN NEW;
    ELSE
        RETURN NEW;
    END IF;

    EXCEPTION WHEN INVALID_CHARACTER_VALUE_FOR_CAST THEN
        RAISE EXCEPTION 'failed to cast classification';
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER tr_check_content_content_type
    BEFORE INSERT OR UPDATE on content
    FOR EACH ROW EXECUTE PROCEDURE check_content_content_type();

CREATE OR REPLACE FUNCTION check_content_timestamp()
RETURNS TRIGGER AS
$$
BEGIN
    IF (NEW.settings->>'startdate')::timestamptz IS NOT NULL AND (NEW.settings->>'enddate')::timestamptz IS NOT NULL THEN
        RETURN NEW;
    ELSE
        RETURN NEW;
    END IF;

    EXCEPTION WHEN INVALID_CHARACTER_VALUE_FOR_CAST THEN
        RAISE EXCEPTION 'failed to cast to timestamp';
END;
$$
LANGUAGE plpgsql;

CREATE TRIGGER tr_check_content_timestamp
    BEFORE INSERT OR UPDATE on content
    FOR EACH ROW EXECUTE PROCEDURE check_content_timestamp();

UPDATE content
SET settings =
 jsonb_build_object(
    'startdate', content.startdate,
    'enddate', content.enddate
) where type = 'event';

UPDATE content
SET settings =
 jsonb_build_object(
    'priority', content.priority
) where type = 'news';

UPDATE content
SET settings =
 jsonb_build_object(
    'navigation', true,
    'classification', array_append(ARRAY[]::text[], 'template')
) where type = 'template';

ALTER TABLE ONLY content DROP COLUMN IF EXISTS startdate;
ALTER TABLE ONLY content DROP COLUMN IF EXISTS enddate;
ALTER TABLE ONLY content DROP COLUMN IF EXISTS priority;


COMMIT;
