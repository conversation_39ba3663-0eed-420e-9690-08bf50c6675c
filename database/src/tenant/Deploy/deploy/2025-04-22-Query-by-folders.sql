-- Deploy cm_tenant_db_DeploySchema:2025-04-22-Query-by-folders to pg

BEGIN;
    ALTER TABLE document DISABLE TRIGGER ALL;

    ALTER TABLE document
        ADD COLUMN IF NOT EXISTS parent_path ltree GENERATED ALWAYS AS (
            CASE
                -- handle NULL or empty path
                WHEN path IS NULL OR path = ''::ltree THEN NULL
                -- if the last label equals the id (without dashes)
                WHEN subpath(path, nlevel(path)-1, 1)::text = translate(id::text, '-', '')
                    THEN CASE
                    -- if there is more than one label, remove the last label
                             WHEN nlevel(path) > 1 THEN subpath(path, 0, nlevel(path)-1)
                    -- if it’s a single-label path built by appending the id, return NULL
                             ELSE NULL
                    END
                -- if the id wasn’t appended, leave the path unchanged
                ELSE path
            END
        ) STORED;

    ALTER TABLE document
        ADD COLUMN IF NOT EXISTS parent uuid GENERATED ALWAYS AS (
            CASE
                -- handle NULL or empty path
                WHEN path IS NULL OR path = ''::ltree OR nlevel(path) = 1 THEN NULL
                -- if the last label equals the id (without dashes)
                WHEN subpath(path, nlevel(path)-1, 1)::text = translate(id::text, '-', '')
                    THEN CASE
                    -- if there is more than one label, remove the last label
                             WHEN nlevel(path) > 1 THEN uuid(subpath(subpath(path, 0, nlevel(path)-1), nlevel(subpath(path, 0, nlevel(path)-1))-1, 1)::text)
                    -- if it’s a single-label path built by appending the id, return NULL
                             ELSE NULL
                    END
                -- if the id wasn’t appended, leave the path unchanged
                ELSE uuid(subpath(path, nlevel(path)-1, 1)::text)
                END
            ) STORED;

    CREATE INDEX IF NOT EXISTS ix_document_parent_path ON document USING GIST (parent_path);
    CREATE INDEX IF NOT EXISTS ix_document_parent ON document (parent);

    ALTER TABLE document_history
        ADD COLUMN IF NOT EXISTS parent_path ltree;
    ALTER TABLE document_history
        ADD COLUMN IF NOT EXISTS parent uuid;

    ALTER TABLE document ENABLE TRIGGER ALL;

COMMIT;
