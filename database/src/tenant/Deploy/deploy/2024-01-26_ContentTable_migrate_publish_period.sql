
-- NEWS expirationDate
update content
set expire_at = (settings->>'expirationDate')::timestamptz
where (settings->>'expirationDate')::timestamptz is not null
  and type = 'news';

-- NEWS releaseDate
update content
set publish_at = (settings->>'releaseDate')::timestamptz
where (settings->>'releaseDate')::timestamptz is not null
  and published
  and type = 'news';

-- ALERTS enddate
update content
set expire_at = (settings->>'enddate')::timestamptz
where (settings->>'enddate')::timestamptz is not null
  and type = 'alert';

-- ALERTS startdate
update content
set publish_at = (settings->>'startdate')::timestamptz
where (settings->>'startdate')::timestamptz is not null
  and published
  and type = 'alert';


-- The rest
update content
set publish_at = COALESCE(updated, created, now())
where published and publish_at is null;
