-- the migration is irreversible. To perform it audit_event type must contain all possible values

BEGIN;
ALTER TABLE audit_record ALTER COLUMN event TYPE VARCHAR(155);
COMMIT ;

BEGIN ;

DROP INDEX IF EXISTS ix_audit_record_search;
CREATE INDEX ix_audit_record_search
    ON audit_record
        USING gin
        ((id || ' ' || event || ' ' || coalesce(message, ' ') || ' '
              || coalesce(topic_id::text, ' ') || ' '
              || coalesce(subscriber_id::text, ' ') || ' '
            || coalesce(issue_id::text, ' ')) gin_trgm_ops);
CREATE INDEX IF NOT EXISTS idx_audit_record_event ON audit_record (event);


COMMIT ;
