-- Deploy cm_tenant_db_DeploySchema:FullTextSearchIndex to pg

BEGIN;
drop index if exists ix_content_tsvector_index_col;
drop index if exists ix_document_tsvector_index_col;

ALTER TABLE content
    DROP COLUMN IF EXISTS tsvector_index_col;

ALTER TABLE document
    DROP COLUMN IF EXISTS tsvector_index_col;

ALTER TABLE content
    ADD COLUMN if not exists tsvector_index_col tsvector
        GENERATED ALWAYS AS (
                        setweight(to_tsvector('english', coalesce(title, '')), 'A')
                        || setweight(to_tsvector('english', coalesce(regexp_replace(replace(regexp_replace(content.content, '<[^>]*>', ' ', 'g'), '&nbsp;', ' '), '[^\s:/_-]{30,}', ' ', 'g'), '')), 'C')
                    || setweight(to_tsvector('english', coalesce(regexp_replace(replace( replace(replace(content.data::text, '.', ' '), '-', ' '),'_', ' '), '[^\s:/_-]{30,}', ' ', 'g'), '')), 'C')
                || setweight(to_tsvector('english', coalesce(regexp_replace(replace( replace(replace(content.route, '.', ' '), '-', ' '),'_', ' '), '[^\s:/_-]{30,}', ' ', 'g'), '')), 'D')
            )
            STORED;
CREATE INDEX if not exists ix_content_tsvector_index_col ON content USING GIN (tsvector_index_col);

ALTER TABLE document
    ADD COLUMN if not exists tsvector_index_col tsvector
        GENERATED ALWAYS AS (
            setweight(to_tsvector('english', regexp_replace(replace( replace(replace(filename, '.', ' '), '-', ' '),'_', ' '), '[^\s:/_-]{30,}', ' ', 'g')), 'A')
            )
            STORED;
CREATE INDEX if not exists ix_document_tsvector_index_col ON document USING GIN (tsvector_index_col);

COMMIT;
