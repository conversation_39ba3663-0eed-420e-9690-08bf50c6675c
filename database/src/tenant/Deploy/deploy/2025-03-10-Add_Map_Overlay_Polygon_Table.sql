-- Deploy cm_tenant_db_DeploySchema:2024-12-09-Add_Map_Overlay_Polygon_Table to pg

BEGIN;

CREATE TABLE IF NOT EXISTS map_overlay_polygons
(
    id uuid NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_map_overlay_polygons
        primary key,
    polygon polygon not null,
    document_id uuid
        constraint fk_map_overlay_polygons_document_id
        references document(id)
        ON DELETE CASCADE,
    data jsonb
    );

CREATE INDEX IF NOT EXISTS idx_map_overlay_polygons_polygon ON map_overlay_polygons USING GIST (polygon);
CREATE INDEX IF NOT EXISTS idx_map_overlay_polygons_document_id ON map_overlay_polygons (document_id);

GRANT ALL ON TABLE map_overlay_polygons to contentmanager_application_user;

COMMIT;
