-- Deploy cm_tenant_db_DeploySchema:2023-11-21-StructureTable_content_structure_id to pg

BEGIN;
    -- Structure table (Template)
    CREATE TABLE IF NOT EXISTS structure
    (
        id                          uuid NOT NULL DEFAULT uuid_generate_v4()
            constraint pk_structure primary key,
        name                        VARCHAR(150) NOT NULL,
        template                    text NOT NULL,
        form_structure              jsonb,
        active                      boolean NOT NULL,
        created_at                  TIMESTAMP WITH TIME ZONE DEFAULT now(),
        created_by                  uuid NOT NULL,
        updated_at                  TIMESTAMP WITH TIME ZONE DEFAULT now(),
        updated_by                  uuid NOT NULL
    );
    CREATE UNIQUE INDEX IF NOT EXISTS idx_structure_name ON structure (lower(name));
    GRANT ALL ON TABLE structure to contentmanager_application_user;

    -- Content table
    ALTER TABLE content ADD COLUMN structure_id uuid
            constraint fk_content_structure_id references structure(id);

    ALTER TYPE content_type ADD VALUE IF NOT EXISTS 'fragment' AFTER 'distributed_page';

COMMIT;

