-- Deploy cm_tenant_db_DeploySchema:RoleTable_alter_access to pg

BEGIN;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS tag;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS content;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS site;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS media;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS domain;
ALTER TABLE ONLY role ADD COLUMN mod_core INT NOT NULL DEFAULT 0;
ALTER TABLE ONLY role ADD COLUMN mod_pages INT NOT NULL DEFAULT 0;
ALTER TABLE ONLY role ADD COLUMN mod_news INT NOT NULL DEFAULT 0;
ALTER TABLE ONLY role ADD COLUMN mod_events INT NOT NULL DEFAULT 0;
ALTER TABLE ONLY role ADD COLUMN mod_media INT NOT NULL DEFAULT 0;
ALTER TABLE ONLY role ADD COLUMN mod_transportation INT NOT NULL DEFAULT 0;
COMMIT;
