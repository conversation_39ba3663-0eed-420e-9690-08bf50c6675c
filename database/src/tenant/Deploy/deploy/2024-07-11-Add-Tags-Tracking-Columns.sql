-- Deploy cm_tenant_db_DeploySchema:2024-07-11-Add-Tags-Tracking-Columns to pg

BEGIN;

ALTER TABLE tag
    ADD COLUMN IF NOT EXISTS created_at    TIMESTAMP WITH TIME ZONE not null DEFAULT now(),
    ADD COLUMN IF NOT EXISTS created_by    uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085',
    ADD COLUMN IF NOT EXISTS updated_at    TIMESTAMP WITH TIME ZONE not null DEFAULT now(),
    ADD COLUMN IF NOT EXISTS updated_by    uuid NOT NULL DEFAULT '45f06f48-a93c-414e-b9a0-7582e0abc085'
;

COMMIT;
