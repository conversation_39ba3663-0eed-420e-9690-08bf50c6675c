-- Deploy cm_tenant_db_DeploySchema:2025-06-11_Reservations to pg

BEGIN;
    ALTER TABLE content DISABLE TRIGGER ALL;

    ALTER TABLE content DROP COLUMN IF EXISTS extended_lock;
    ALTER TABLE content DROP COLUMN IF EXISTS current_editor;
    ALTER TABLE content DROP COLUMN IF EXISTS editing_session;
    ALTER TABLE content_history DROP COLUMN IF EXISTS extended_lock;
    ALTER TABLE content_history DROP COLUMN IF EXISTS current_editor;
    ALTER TABLE content_history DROP COLUMN IF EXISTS editing_session;

    ALTER TABLE content ENABLE TRIGGER ALL;

    CREATE TABLE IF NOT EXISTS reservations
    (
        key varchar(150) NOT NULL CHECK (key <> ''),
        current_editor uuid,
        editing_session timestamptz,
        extended_lock timestamptz,
        CONSTRAINT pk_reservations PRIMARY KEY(key)
    );
    CREATE INDEX IF NOT EXISTS idx_reservations_current_editor_editing_session ON reservations (current_editor, editing_session);

    GRANT ALL ON TABLE reservations to contentmanager_application_user;

COMMIT;
