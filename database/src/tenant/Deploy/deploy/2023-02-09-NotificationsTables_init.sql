-- Deploy cm_tenant_db_DeploySchema:2023-02-09-NotificationsTables_init to pg

BEGIN;
CREATE TYPE topic_type as ENUM (
    'alert'
    , 'bus_alert'
    , 'news'
    , 'events'
    , 'whats_new'
    , 'board_agenda'
    );
CREATE TYPE creation_type as <PERSON>NUM (
    'cron'
    , 'cm_admin'
    , 'manual'
    );
CREATE TYPE delivery_type as <PERSON>NU<PERSON> (
    'email'
    , 'sms'
    );
CREATE TYPE relay_type as <PERSON>NU<PERSON> (
    'plain_smtp'
    , 'sendgrid'
    );
CREATE TYPE issue_status as ENUM (
    'ready'
    , 'sending'
    , 'done'
    , 'stopped'
    );
CREATE TYPE audit_event as ENUM (
    'cant_send_issue_to_subscriber'
    , 'send_issue_started'
    , 'send_issue_stopped'
    , 'send_issue_stopped_error'
    );

ALTER TABLE ONLY bus_status ADD COLUMN IF NOT EXISTS has_email_notification boolean DEFAULT false NOT NULL;

CREATE TABLE IF NOT EXISTS schedule
(
    id          uuid                   NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_schedule primary key,
    cron        character varying(256) NOT NULL,
    description character varying(256) NOT NULL,

    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
GRANT ALL ON TABLE schedule to contentmanager_application_user;

CREATE TABLE IF NOT EXISTS relay
(
    id       uuid                     NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_relay primary key,
    type     relay_type               NOT NULL,
    x_data   jsonb      DEFAULT '{}'  NOT NULL
);
GRANT ALL ON TABLE relay to contentmanager_application_user;

CREATE TABLE IF NOT EXISTS subscriber
(
    id                         uuid NOT NULL            DEFAULT uuid_generate_v4()
        constraint pk_subscriber primary key,

    first_name                 character varying(256),
    last_name                  character varying(256),

    manage_code                uuid unique,

    email                      character varying(256)  NOT NULL,
    email_confirmed_at         TIMESTAMP WITH TIME ZONE,
    email_confirmation_sent_at TIMESTAMP WITH TIME ZONE,

    phone                      character varying(256) UNIQUE,
    phone_confirmed_at         TIMESTAMP WITH TIME ZONE,
    phone_confirmation_sent_at TIMESTAMP WITH TIME ZONE,
    phone_confirmation_code    character varying(10) UNIQUE,

    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
CREATE EXTENSION if not exists pg_trgm;
CREATE INDEX ix_subscriber_search ON subscriber USING gin ((id || ' ' || email || ' ' || coalesce(phone, ' ') || ' ' || coalesce(first_name, ' ') || ' ' || coalesce(last_name, ' ')) gin_trgm_ops);
CREATE UNIQUE INDEX ux_subscriber_email ON subscriber (lower(email));
-- won't help: CREATE INDEX ix_subscriber_id ON subscriber (id);
-- won't help: ALTER TABLE subscriber CLUSTER ON pk_subscriber;
GRANT ALL ON TABLE subscriber to contentmanager_application_user;

CREATE TABLE IF NOT EXISTS topic
(
    id                 uuid       NOT NULL      DEFAULT uuid_generate_v4()
        constraint pk_topics primary key,

    schedule_id        uuid
        CONSTRAINT fk_topics_schedule_id
            REFERENCES schedule (id) ON DELETE SET NULL,
    relay_id        uuid
        CONSTRAINT fk_topics_relay_id
            REFERENCES relay (id) ON DELETE SET NULL,
    site_id            uuid,
    bus_route_id       uuid
        CONSTRAINT fk_topics_bus_route_id
            REFERENCES bus_route (id) ON DELETE SET NULL,

    topic_type         topic_type NOT NULL,
    delivery_type      delivery_type NOT NULL,
    active             boolean    NOT NULL,

    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
GRANT ALL ON TABLE topic to contentmanager_application_user;


CREATE TABLE IF NOT EXISTS subscription
(
    subscriber_id        uuid                     NOT NULL
        CONSTRAINT fk_subscriptions_subscriber_id
            REFERENCES subscriber (id) ON DELETE CASCADE,
    topic_id             uuid                     NOT NULL
        CONSTRAINT fk_subscriptions_topic_id
            REFERENCES topic (id) ON DELETE CASCADE,
    CONSTRAINT pk_subscriber_topic PRIMARY KEY (subscriber_id, topic_id),

    subscribed           boolean                  NOT NULL,

    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
-- won't help: ALTER TABLE subscription CLUSTER ON pk_subscriber_topic;
GRANT ALL ON TABLE subscription to contentmanager_application_user;

CREATE TABLE IF NOT EXISTS issue
(
    id                   uuid                     NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_issues primary key,

    topic_id             uuid                     NOT NULL
        CONSTRAINT fk_issues_topic_id
            REFERENCES topic (id) ON DELETE CASCADE,
    included_content_ids uuid[],

    starts_at            TIMESTAMP WITH TIME ZONE NOT NULL,
    total_sent           integer                  NOT NULL DEFAULT 0,
    min_subscriber_id    uuid,
    status               issue_status             NOT NULL,
    creation_type        creation_type            NOT NULL,
    version              bigint                   NOT NULL DEFAULT 0,
    sending_process_id   uuid,

    domain               character varying(256),
    subject              character varying(256),
    html_content         text,
    plain_text_content   text,
    sms_content          text,    

    created_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    created_by                 uuid NOT NULL,
    updated_at                 TIMESTAMP WITH TIME ZONE DEFAULT now(),
    updated_by                 uuid NOT NULL
);
GRANT ALL ON TABLE issue to contentmanager_application_user;
create or replace function immutable_array_to_string(arr anyarray, sep text) returns text
    immutable
    language sql
as
$$
      SELECT array_to_string(arr, sep);
$$;

CREATE INDEX ix_issue_search
    ON issue
        USING gin
        ((id || ' ' || topic_id || ' ' || coalesce(subject, ' ') || ' '
              || coalesce(domain, ' ') || ' '
              || coalesce(immutable_array_to_string(included_content_ids, ' '), ' ')) gin_trgm_ops);
			  

CREATE TABLE IF NOT EXISTS audit_record
(
    id                   uuid                     NOT NULL DEFAULT uuid_generate_v4()
        constraint pk_audit_record primary key,
    created_at           TIMESTAMP WITH TIME ZONE          DEFAULT now(),

    subscriber_id        uuid
        CONSTRAINT fk_audit_record_subscriber_id
            REFERENCES subscriber (id) ON DELETE CASCADE,
    topic_id             uuid
        CONSTRAINT fk_issues_topic_id
            REFERENCES topic (id) ON DELETE CASCADE,
    issue_id             uuid
        CONSTRAINT fk_audit_record_issue_id
            REFERENCES issue (id) ON DELETE CASCADE,

    event              audit_event                NOT NULL,
    message            text,
    x_data   jsonb
);
GRANT ALL ON TABLE audit_record to contentmanager_application_user;
CREATE INDEX ix_audit_record_search
    ON audit_record
        USING gin
        ((id || ' ' || coalesce(message, ' ') || ' '
              || coalesce(topic_id::text, ' ') || ' '
              || coalesce(subscriber_id::text, ' ') || ' '
              || coalesce(issue_id::text, ' ')) gin_trgm_ops);

COMMIT;