-- Deploy cm_tenant_db_DeploySchema:2025-02-10-Resources to pg

BEGIN;

    CREATE TABLE resource_refs (
       entity_id   UUID NOT NULL,
       entity_type VARCHAR(255) NOT NULL CHECK (resource <> ''),
       resource    VARCHAR(255) NOT NULL CHECK (resource <> ''),
       PRIMARY KEY (entity_id, entity_type, resource)
    );

    CREATE INDEX idx_resource ON resource_refs (resource);

GRANT ALL ON TABLE resource_refs to contentmanager_application_user;

COMMIT;
