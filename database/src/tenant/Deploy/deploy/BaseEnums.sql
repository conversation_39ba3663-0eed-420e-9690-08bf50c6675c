-- Deploy contentmanagerapp_DeploySchema:BaseEnums to pg

BEGIN;

	CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA pg_catalog;
	CREATE EXTENSION IF NOT EXISTS hstore WITH SCHEMA pg_catalog;
	CREATE EXTENSION IF NOT EXISTS ltree WITH SCHEMA pg_catalog;
	CREATE EXTENSION IF NOT EXISTS dblink;
    CREATE TYPE page_style as ENUM ('DCT', 'WYSIWYG', 'HTML');
    CREATE TYPE content_type as ENUM ('template', 'page', 'css', 'js', 'news', 'event');
    CREATE TYPE media_type as ENUM ('folder', 'image', 'video');

COMMIT;

DO $$
BEGIN

-- -----------------------------------------------------
-- New Index Type for uuid[] columns
--
-- Create a GIN inverted index type for UUID array
-- columns to enable quick comparisons
-- -----------------------------------------------------

CREATE OPERATOR CLASS _uuid_ops DEFAULT FOR TYPE _uuid USING gin AS
OPERATOR 1 &&(anyarray, anyarray),
OPERATOR 2 @>(anyarray, anyarray),
OPERATOR 3 <@(anyarray, anyarray),
OPERATOR 4 =(anyarray, anyarray),
FUNCTION 1 uuid_cmp(uuid, uuid),
FUNCTION 2 ginarrayextract(anyarray, internal, internal),
FUNCTION 3 ginqueryarrayextract(anyarray, internal, smallint, internal, internal, internal, internal),
FUNCTION 4 ginarrayconsistent(internal, smallint, anyarray, integer, internal, internal, internal, internal),
STORAGE uuid;

EXCEPTION
WHEN duplicate_object THEN
RAISE NOTICE 'error: %', SQLERRM;
END;
$$;
