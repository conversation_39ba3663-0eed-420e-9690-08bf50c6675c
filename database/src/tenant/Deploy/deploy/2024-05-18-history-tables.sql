-- Deploy cm_tenant_db_DeploySchema:2024-05-18-history-tables to pg

BEGIN;

DO $$
    DECLARE
        table_name text;
        table_names text[] := ARRAY['content', 'structure', 'navigation', 'media', 'document', 'redirect_rule', 'security_group', 'role'];
    BEGIN
        FOR i IN 1..array_length(table_names, 1) LOOP
                table_name := table_names[i];

                -- Add the sys_period column to each table
                EXECUTE 'ALTER TABLE ' || quote_ident(table_name) ||
                        ' ADD COLUMN sys_period tstzrange NOT NULL DEFAULT tstzrange(current_timestamp, null)';

                -- Create a history table for each table
                EXECUTE 'CREATE TABLE ' || quote_ident(table_name) || '_history (LIKE ' || quote_ident(table_name) || ')';

                -- Create a trigger for versioning on each table
                EXECUTE 'CREATE TRIGGER versioning_trigger_on_' || quote_ident(table_name) ||
                        ' BEFORE INSERT OR UPDATE OR DELETE ON ' || quote_ident(table_name) ||
                        ' FOR EACH ROW EXECUTE PROCEDURE versioning(''sys_period'', ''' || quote_ident(table_name) || '_history'', true)';

                -- Grant permissions on the history table
                EXECUTE 'GRANT ALL ON TABLE ' || quote_ident(table_name) || '_history TO contentmanager_application_user';
            END LOOP;
    END $$;

COMMIT;
