-- Deploy cm_tenant_db_DeploySchema:AccountSecurityGroupTable to pg

BEGIN;

    CREATE TABLE account_security_group(
        id uuid NOT NULL DEFAULT uuid_generate_v4(),
        account_id uuid NOT NULL,
        security_group_id uuid NOT NULL,
        CONSTRAINT pk_account_security_group_id PRIMARY KEY(id),
        CONSTRAINT fk_account_security_group_account_id FOREIGN KEY (account_id) REFERENCES account(id) ON DELETE CASCADE,
        CONSTRAINT fk_account_security_group_sg_id FOREIGN KEY (security_group_id) REFERENCES security_group(id) ON DELETE CASCADE
    )WITH (
        OIDS=FALSE
    );

    GRANT ALL ON TABLE account_security_group to contentmanager_application_user;

COMMIT;
