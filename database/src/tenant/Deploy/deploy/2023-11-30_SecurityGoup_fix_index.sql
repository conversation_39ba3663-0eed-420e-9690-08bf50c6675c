BEGIN ;
CREATE OR <PERSON><PERSON>LACE FUNCTION immutable_concat_security_group_cols(p_sg public.security_group)
    RETURNS text AS $$
BEGIN
    RETURN p_sg.id || ' '
               || p_sg.name || ' '
               || p_sg.description || ' '
               || p_sg.role_id || ' '
               || COALESCE(p_sg.site_id::text, ' ') || ' '
               || array_to_string(p_sg.external_id_list, ' ') || ' '
               || array_to_string(p_sg.audience, ' ') || ' '
               || array_to_string(p_sg.available_groups::text[], ' ') || ' '
    ;
END;
$$ LANGUAGE plpgsql IMMUTABLE;


DROP INDEX IF EXISTS idx_composite_security_group_trgm;
CREATE INDEX IF NOT EXISTS idx_composite_security_group_trgm ON public.security_group USING gin(immutable_concat_security_group_cols(security_group) gin_trgm_ops);

END ;
