-- Deploy cm_tenant_db_DeploySchema:2025-02-07-Image-Renditions to pg

BEGIN;

CREATE TABLE IF NOT EXISTS image_crop_size (
    id uuid UNIQUE NOT NULL DEFAULT uuid_generate_v4(),
    name text NOT NULL UNIQUE,
    width int NOT NULL,
    height int NOT NULL,
    active boolean NOT NULL DEFAULT true,

    created_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    created_by    uuid         NOT NULL,
    updated_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
    updated_by    uuid         NOT NULL
    );

ALTER TABLE media DISABLE TRIGGER ALL;

ALTER TABLE media ADD COLUMN IF NOT EXISTS image_crop_size_ids uuid[] DEFAULT NULL;
ALTER TABLE media ADD COLUMN IF NOT EXISTS dirty_image_crop_size_ids uuid[] DEFAULT NULL;
ALTER TABLE media_history ADD COLUMN IF NOT EXISTS image_crop_size_ids uuid[] DEFAULT NULL;
ALTER TABLE media_history ADD COLUMN IF NOT EXISTS dirty_image_crop_size_ids uuid[] DEFAULT NULL;

ALTER TABLE media ENABLE TRIGGER ALL;

CREATE INDEX IF NOT EXISTS idx_image_crop_size_ids_gin  ON media USING GIN(image_crop_size_ids);

GRANT ALL ON TABLE image_crop_size to contentmanager_application_user;

COMMIT;
