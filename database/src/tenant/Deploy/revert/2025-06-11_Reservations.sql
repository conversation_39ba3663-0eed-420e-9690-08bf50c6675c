-- Revert cm_tenant_db_DeploySchema:2025-06-11_Reservations from pg

BEGIN;
    ALTER TABLE content DISABLE TRIGGER ALL;

    ALTER TABLE content ADD COLUMN IF NOT EXISTS current_editor uuid
        CONSTRAINT fk_content_current_editor_account_id
            REFERENCES account (id) ON DELETE SET NULL;
    ALTER TABLE content ADD COLUMN IF NOT EXISTS editing_session
        timestamptz;
    ALTER TABLE content ADD COLUMN IF NOT EXISTS extended_lock timestamptz;

    CREATE INDEX IF NOT EXISTS idx_content_current_editor ON content (current_editor);

    ALTER TABLE content_history ADD COLUMN IF NOT EXISTS current_editor uuid;
    ALTER TABLE content_history ADD COLUMN IF NOT EXISTS editing_session timestamptz;
    ALTER TABLE content_history ADD COLUMN IF NOT EXISTS extended_lock timestamptz;

    ALTER TABLE content ENABLE TRIGGER ALL;

    -- Drop the reservations table
    DROP TABLE IF EXISTS reservations;
COMMIT;
