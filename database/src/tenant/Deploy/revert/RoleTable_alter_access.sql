-- Revert cm_tenant_db_DeploySchema:RoleTable_alter_access from pg

BEGIN;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_core;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_pages;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_news;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_events;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_media;
ALTER TABLE ONLY role DROP COLUMN IF EXISTS mod_transportation;
ALTER TABLE ONLY role ADD COLUMN site access_level NOT NULL DEFAULT 'No Access'::access_level;
ALTER TABLE ONLY role ADD COLUMN domain access_level NOT NULL DEFAULT 'No Access'::access_level;
ALTER TABLE ONLY role ADD COLUMN content access_level NOT NULL DEFAULT 'No Access'::access_level;
ALTER TABLE ONLY role ADD COLUMN media access_level NOT NULL DEFAULT 'No Access'::access_level;
<PERSON>TE<PERSON> TABLE ONLY role ADD COLUMN tag access_level NOT NULL DEFAULT 'No Access'::access_level;






COMMIT;
