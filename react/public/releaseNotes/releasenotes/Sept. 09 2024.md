# Release Notes (Sept. 09 2024)

### Bug fixes and improvements

Optimized performance for public sites.

Resolved issues with distributed pages not displaying properly

Added additional content details in the Content Editor > Revision History dropdown

Lexical Editor:

-   Resolved Undo/Redo feature not always working
-   Improved useability and resolve some issues for Embedded HTML nodes by using a dialog editor as opposed to inline.
-   Resolved several bugs regarding tables and lists
