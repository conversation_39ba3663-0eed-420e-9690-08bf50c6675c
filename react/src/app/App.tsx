import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom'
import {
    AllFolderContextProvider,
    DocumentContextProvider,
    FolderContextProvider,
    FullAccessFoldersContextProvider
} from '../pkgs/media/document/context'
import { DisabledContextProvider } from '../common/DisabledContext'
import {
    Alert,
    Button,
    Checkbox, FormControl, InputLabel,
    ListItemText,
    MenuItem,
    Select,
    StyledEngineProvider,
    TextField,
    ThemeProvider
} from '@mui/material'
import { primaryTheme } from './theme'
import '../styles/shortcuts.css'
import './App.css'
import { ToastContainer } from 'react-toastify'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { ApplicationWrapper } from './ApplicationWrapper'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { AdapterMoment } from '@mui/x-date-pickers/AdapterMoment'
import { LoadingButton } from '@mui/lab'
import { useEditingSession } from '@/pkgs/reservation/useEditingSession'

const queryClient = new QueryClient()

export function App() {
    useEditingSession()

    if (window.location.hostname === 'contentmanager.imagineeverything.com' ||
        window.location.hostname === 'contentmanager.imagineeverything.ca' ||
        window.location.hostname === 'contentmanager.imagineeverything.ca.localhost') {
        return <TransitionTenantToUniqueURL />
    }
    // Custom Menu / Dialogs
    // Global function
    // Provide it a message, available actions and parameters for actions
    // notify("message", "type")
    // use Toast as an example
    return (
        <LocalizationProvider dateAdapter={AdapterMoment}>
            <QueryClientProvider client={queryClient}>
                <BrowserRouter>
                    <StyledEngineProvider injectFirst>
                        <ThemeProvider theme={primaryTheme}>

                            <DisabledContextProvider>
                                <DocumentContextProvider>
                                    <FullAccessFoldersContextProvider>
                                        <FolderContextProvider>
                                            <AllFolderContextProvider>
                                                <ToastContainer limit={10} />
                                                {
                                                    <ApplicationWrapper />
                                                }
                                            </AllFolderContextProvider>
                                        </FolderContextProvider>
                                    </FullAccessFoldersContextProvider>
                                </DocumentContextProvider>
                            </DisabledContextProvider>

                        </ThemeProvider>
                    </StyledEngineProvider>
                </BrowserRouter>
            </QueryClientProvider>
        </LocalizationProvider>
    )
}


function TransitionTenantToUniqueURL() {
    const [to, setTo] = React.useState('')
    const suffix = window.location.hostname
    const options = [
        {
            name: 'Regina Catholic School Division',
            to: 'rcsd.' + suffix
        },
        {
            name: 'Peel District School Board',
            to: 'peelschools.' + suffix
        },
        {
            name: 'Nechako Lakes School District',
            to: 'sd91.' + suffix
        },
        {
            name: 'Living Sky School Division',
            to: 'livingsky.' + suffix
        },
        {
            name: 'Fort MacMurray Catholic Schools',
            to: 'fmcsd.' + suffix
        },
        {
            name: 'Grasslands School Division',
            to: 'grasslands.' + suffix
        },
        {
            name: 'Central Okanagan School District',
            to: 'sd23.' + suffix
        },
        {
            name: 'Yellowknife Catholic Schools',
            to: 'ycs.' + suffix
        },
        {
            name: 'Cariboo - Chilcotin School District',
            to: 'sd27.' + suffix
        },
        {
            name: 'Black Gold School Division',
            to: 'blackgold.' + suffix
        },
        {
            name: 'Vernon School District',
            to: 'sd22.' + suffix
        },
        {
            name: 'Christ the Redeemer',
            to: 'redeemer.' + suffix
        },
        {
            name: 'Calgary Catholic School District',
            to: 'cssd.' + suffix
        },
        {
            name: 'Edmonton Catholic School District',
            to: 'ecsd.' + suffix
        },
        {
            name: 'Calgary Board of Education',
            to: 'cbe.' + suffix
        },
        {
            name: 'Medicine Hat Public',
            to: 'mhpsd.' + suffix
        },
        {
            name: 'High Prairie School District',
            to: 'hpsd.' + suffix
        },
        {
            name: 'Christ The Teacher School Division',
            to: 'ctt.' + suffix
        },
        {
            name: 'Prairie Rose School Division',
            to: 'prairierose.' + suffix
        },
        {
            name: 'Holy Family Catholic Regional Division',
            to: 'hfcrd.' + suffix
        },
        {
            name: 'Holy Spirit Catholic School Division',
            to: 'hscsd.' + suffix
        },
        {
            name: 'Limestone District School Board',
            to: 'limestone.' + suffix
        },
        {
            name: 'Northwest School Division',
            to: 'nwsd.' + suffix
        },
        {
            name: 'East Central Catholic Schools',
            to: 'ecacs.' + suffix
        }

    ].sort(((a, b) => a.name.localeCompare(b.name)))
    return (
        <div className="splash-centered">
            <div className="background-logo" style={{ marginBottom: '3rem' }} />
            <div className="flex-column-center">
                <div className={'flex-row-align-center'}>
                    <FormControl variant={'outlined'} style={{ width: '20rem' }}
                    >
                        <InputLabel>Choose Your District</InputLabel>
                        <Select
                            placeholder={'Choose Your District'}
                            label={'Choose Your District'}
                            value={to}
                            onChange={(v) => {
                                //@ts-ignore
                                setTo(v.target.value || '')
                            }}
                        >
                            {options.map(({ name, to }) => {
                                return <MenuItem key={name} value={to}>
                                    <ListItemText primary={name} />
                                </MenuItem>
                            })}
                        </Select>
                    </FormControl>

                    <Button
                        variant={'outlined'}
                        style={{ marginLeft: '8px' }}
                        onClick={() => {
                            // @ts-ignore
                            window.location = ('https://' + to)
                        }}
                        disabled={!to?.length}
                    >
                        Go
                    </Button>
                </div>
                <Alert severity={'info'} sx={{ my: '8px', width: '50rem' }}>
                    To improve security, Content Manager is now using unique application URLs. To access your site,
                    simply select your district from the dropdown menu and hit go! As a time-saver, we would also
                    recommend
                    bookmarking the new URL.
                </Alert>
            </div>

        </div>
    )
}
