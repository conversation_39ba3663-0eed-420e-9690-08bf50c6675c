import { FormControl<PERSON><PERSON><PERSON>, Grid, Switch } from '@mui/material'
import { EditorsSelector } from './selectors/EditorsSelector'
import Button from '@mui/material/Button'
import React, { useEffect, useState } from 'react'
import { storage } from '../../common/storage.service'
import { DepartmentsAutocomplete } from './selectors/DepartmentsAutocomplete'
import { StatusSelector } from '../content/BaseForm'
import SearchBar from '../../common/components/SearchBar'
import { DelayedSearchTimeout } from '../../common/constants'
import { ContentPublishedTypes } from '../content/types'
import { useAppContext } from '@/pkgs/auth/atoms'
import { TagType } from '@/pkgs/system/tags/types'
import { TagsSelector } from '../system/tags/TagsSelector'

export const ManagerFilters = ({ paginationState, setPaginationState, contentType, resetFormCallback }) => {
    const appContext = useAppContext()
    const pageTypeKey = `${appContext.tenantID}-${contentType}-page-type`
    const inactiveKey = `${appContext.tenantID}-${contentType}-inactive`

    useEffect(() => {
        const pageType = storage.getItem(pageTypeKey)
        if (pageType) {
            handleChangePublishedView(pageType)
        }

        const inactive = storage.getItem(inactiveKey)
        if (inactive) {
            setPaginationState((prev) => ({ ...prev, deleted: inactive }))
        }
    }, [])

    const handleChangePublishedView = (pageType) => {
        if (Object.entries(ContentPublishedTypes).some((x) => x[1] === pageType)) {
            storage.setItem(pageTypeKey, pageType)
            setPaginationState((prev) => ({
                ...prev,
                publishedState: pageType,
                currentPage: 0
            }))
        }
    }

    const reset = () => {
        resetFormCallback()
    }

    return (
        <Grid container spacing={2} marginBottom='1em'>
            <Grid item xs={3}>
                <SearchBar
                    value={paginationState.searchTerm}
                    onChange={(val) => setPaginationState((p) => ({ ...p, searchTerm: val }))}
                    helperText='Search by title'
                />
            </Grid>
            <Grid item xs={3}>
                <TagsSelector
                    selected={paginationState?.tags || []}
                    onChange={(tags) => setPaginationState((p) => ({ ...p, tags }))}
                    tagTypes={[contentType]}
                />
            </Grid>
            <Grid item xs={3}>
                <EditorsSelector state={paginationState} setState={setPaginationState} contentType={contentType} />
            </Grid>
            <Grid item xs={3}>
                <DepartmentsAutocomplete
                    state={paginationState}
                    setState={setPaginationState}
                    contentType={contentType}
                />
            </Grid>
            <Grid item xs={3}>
                <StatusSelector
                    value={paginationState.status || ''}
                    onChange={(v) => {
                        setPaginationState((prev) => ({
                            ...prev,
                            status: v,
                            currentPage: 0
                        }))
                    }}
                />
            </Grid>
            <Grid item xs={2}>
                <FormControlLabel
                    disabled={appContext.isCurrentSiteDepartment()}
                    value='start'
                    control={
                        <Switch
                            checked={paginationState.siteOnly || false}
                            onChange={(e) =>
                                setPaginationState((prev) => {
                                    const inactive = e.target.checked
                                    storage.setItem(inactiveKey, inactive)
                                    return {
                                        ...prev,
                                        currentPage: 0,
                                        siteOnly: inactive
                                    }
                                })
                            }
                            color='secondary'
                        />
                    }
                    label='Site Only'
                    labelPlacement='start'
                />
            </Grid>
            <Grid item xs={2}>
                <FormControlLabel
                    value='start'
                    control={
                        <Switch
                            checked={paginationState.inactive || false}
                            onChange={(e) =>
                                setPaginationState((prev) => {
                                    const inactive = e.target.checked
                                    storage.setItem(inactiveKey, inactive)
                                    return {
                                        ...prev,
                                        currentPage: 0,
                                        inactive: inactive
                                    }
                                })
                            }
                            color='secondary'
                        />
                    }
                    label='Deleted'
                    labelPlacement='start'
                />
            </Grid>
            <Grid item xs={5} display='flex' justifyContent='flex-end'>
                <Button onClick={reset} color={'primary'} style={{ marginLeft: 'auto' }}>
                    Reset Filters
                </Button>
            </Grid>
        </Grid>
    )
}
