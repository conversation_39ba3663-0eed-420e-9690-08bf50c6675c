import React, { Dispatch, SetStateAction, useEffect, useState } from 'react'
import { $ElementProps } from '../notifications/TypeHelpers'
import {
    DataGrid,
    GridColumnVisibilityModel,
    GridSortModel,
    GridToolbarColumnsButton,
    GridToolbarContainer
} from '@mui/x-data-grid'
import { clientStorage } from '../../common/client'
import { Paged, PagingQuery, SortingQuery, defaultSortingQuery } from '../../common/react-query'
import { colours } from '@/common/colours'
import Alert from '@mui/material/Alert'

export const DataGridCustomToolbar = () => {
    return (
        <GridToolbarContainer>
            <GridToolbarColumnsButton style={{ marginLeft: 'auto' }} />
        </GridToolbarContainer>
    )
}

export const DataGridBase = <T extends PagingQuery & SortingQuery>({
    state,
    setQuery,
    hideToolbar,
    hidePagination,
    getRowId = (row) => row.ID || row.id,
    ...params
}: {
    state: Paged<any> | undefined
    setQuery: Dispatch<SetStateAction<T>>
    hideToolbar?: boolean
    hidePagination?: boolean
    getRowId?: (row: any) => string
} & Omit<$ElementProps<typeof DataGrid>, 'rows'>) => {
    const cacheKey = 'v3-' + window.location.pathname

    const [visibilityModel, setVisibilityModel] = useState(
        clientStorage.getItem<GridColumnVisibilityModel>(`${cacheKey}.columnVisibilityModel`) || {}
    )
    const [sortModel, setSortModel] = useState(clientStorage.getItem<GridSortModel>(`${cacheKey}.gridSortModel`) || [])

    useEffect(() => {
        if (sortModel.length > 0) {
            setQuery((prev) => ({ ...prev, ...mapSorting(sortModel) }))
        }
    }, [])

    const mapSorting = (sortModel: GridSortModel): SortingQuery => {
        if (sortModel.length > 0) {
            const sorting = sortModel[0]
            return {
                sortingField: sorting.field,
                sortingDirection: sorting.sort
            }
        }
        return defaultSortingQuery
    }

    return (
        <>
            {(state?.PageSize || 0) > 100 && <Alert severity='warning'>Only the first 100 rows are displayed</Alert>}
            <DataGrid
                {...params}
                sx={{
                    '& .disabled-row': {
                        backgroundColor: colours.disabled_row,
                        '&:hover': {
                            backgroundColor: colours.disabled_row_hover
                        }
                    }
                }}
                paginationMode={'server'}
                sortingMode={'server'}
                filterMode={params.filterMode || 'server'}
                pageSizeOptions={[10, 20, 50, 100]}
                onColumnVisibilityModelChange={(model) => {
                    clientStorage.setItem(`${cacheKey}.columnVisibilityModel`, model)
                    setVisibilityModel(model)
                }}
                columnVisibilityModel={visibilityModel}
                rows={(state?.Rows || []).slice(0, 100)}
                rowCount={state?.TotalRecords || 0}
                paginationModel={
                    state
                        ? {
                              pageSize: state.PageSize > 100 ? 100 : state.PageSize,
                              page: state.Page - 1
                          }
                        : undefined
                }
                onPaginationModelChange={
                    state
                        ? (model) => {
                              if (model.page !== state.Page - 1 || model.pageSize !== state.PageSize) {
                                  const page = model.pageSize !== state.PageSize ? 1 : model.page + 1
                                  setQuery((prev) => ({ ...prev, page: page, pageSize: model.pageSize }))
                              }
                          }
                        : undefined
                }
                onSortModelChange={(model) => {
                    clientStorage.setItem(`${cacheKey}.gridSortModel`, model)
                    setSortModel(model)
                    setQuery((prev) => ({ ...prev, ...mapSorting(model) }))
                }}
                sortModel={sortModel}
                disableRowSelectionOnClick={true}
                getRowId={getRowId}
                autoHeight={true}
                slots={{
                    ...(hideToolbar ? {} : { toolbar: DataGridCustomToolbar }),
                    ...(hidePagination ? { pagination: null } : {})
                }}
            />
        </>
    )
}
