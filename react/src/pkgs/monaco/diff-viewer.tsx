import { DiffEditor, DiffEditorProps, Monaco, MonacoDiffEditor } from '@monaco-editor/react'
import { useEffect, useState } from 'react'

function setEditorHeight(editor: MonacoDiffEditor, maxHeight: number) {
    const containerDomNode = editor.getOriginalEditor().getContainerDomNode()
    const contentHeight = Math.min(maxHeight, editor.getOriginalEditor().getContentHeight())
    const modifiedContentHeight = Math.min(maxHeight, editor.getModifiedEditor().getContentHeight())
    const height = Math.max(contentHeight, modifiedContentHeight)

    containerDomNode.setAttribute('height', `${height}px`)
    containerDomNode.setAttribute('position', 'relative')
    editor.getOriginalEditor().layout({
        width: containerDomNode.clientWidth,
        height
    })
    editor.getModifiedEditor().layout({
        width: containerDomNode.clientWidth,
        height
    })
}

type DiffViewerProps = {
    original: any
    modified: any
    maxHeight?: number
} & DiffEditorProps
export const DiffViewer = ({ original, modified, maxHeight = 500, ...diffViewerProps }: DiffViewerProps) => {
    const originalString = typeof original === 'string' ? original : JSON.stringify(original, null, 4)
    const modifiedString = typeof modified === 'string' ? modified : JSON.stringify(modified, null, 4)
    const language = getLanguage(original) || getLanguage(modified) || 'plaintext'
    const [showLines, setShowLines] = useState(true)
    const [editorRefState, setEditorRefState] = useState<MonacoDiffEditor | null>(null)

    function editorOnMountHandler(editor: MonacoDiffEditor, monaco: Monaco) {
        setEditorRefState(editor)
        if (!diffViewerProps?.height) {
            setEditorHeight(editor, maxHeight)
        }
    }

    useEffect(() => {
        if (editorRefState) {
            if (!diffViewerProps?.height) {
                setEditorHeight(editorRefState, maxHeight)
            }

            const originalEditorHeight = editorRefState.getOriginalEditor().getContentHeight()
            const modifiedEditorHeight = editorRefState.getModifiedEditor().getContentHeight()

            if (originalEditorHeight && modifiedEditorHeight <= 30) {
                setShowLines(false)
            } else {
                setShowLines(true)
            }
        }
    }, [editorRefState])

    return (
        <DiffEditor
            onMount={editorOnMountHandler}
            language={language}
            original={originalString}
            modified={modifiedString}
            options={{
                automaticLayout: true,
                readOnly: true,
                lineNumbers: showLines === true ? 'on' : 'off',
                scrollBeyondLastLine: false
            }}
            height={diffViewerProps?.height}
            width={'calc(100% - 30px)'}
        />
    )
}

function getLanguage(value: any): string | undefined {
    if (typeof value === 'string') {
        if (value.includes('</')) return 'html'
        return 'plaintext'
    }

    return !!value ? 'json' : undefined
}
