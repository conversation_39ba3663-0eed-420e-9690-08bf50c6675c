.upload-drag {
    width: 100%;
    height: 4vh;
    background-color: grey;
}
.media-container {
    max-height: 60vh;
}
.document-container {
    max-height: 60vh;
    background-color: #f0f0f0;
}
.document-gallery-container {
    max-height: 85vh;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.sub-container {
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 16px rgba(0, 0, 0, 0.2);
    padding: 1vh;
    margin-bottom: 1vh;
}

.media-dialog-container {
    padding: 1vw;
    max-height: 85vh;
}

.media-gallery {
    /*justify-content: space-between;*/
    margin: 1vw;
    /*overflow: auto;*/
    overflow-y: scroll;
    flex-wrap: wrap;
}

.document-gallery {
    margin-top: 10px;
    /*background-color: #f0f0f0;*/
    margin-bottom: 10px;
    flex-wrap: wrap;
}
.document-gallery > .document-row {
    border-bottom: 0.5px solid rgba(128, 128, 128, 0.2);
    display: flex;
    flex-direction: row;
    align-items: center;
}
.document-gallery:nth-child(1) {
    border-top: 0.5px solid rgba(128, 128, 128, 0.2);
}

/* Test: Issue turns out to be regarding the height of the container - absolute bypassed this - implemented max-height on mediaGallery to fix */
.media-modal-gallery {
    flex-wrap: wrap;
    overflow: auto;
    position: absolute;
    top: 20vh;
    bottom: 0;
    text-align: center;
}

.pdf-wrapper {
    grid-column-start: 3;
    grid-column-end: 9;
    grid-row-start: 1;
    grid-row-end: 9;
    margin-top: 1vh;
    justify-self: center;
}

.pdf-navigation-button {
    height: 60%;
    grid-column-start: 5;
    grid-column-end: span 2;
    justify-self: center;
    grid-row-start: 10;
    z-index: 10000000;
}
.document-container {
    width: 90vw;
    margin: auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}
.right-document-column {
    width: 70vw;
    display: flex;
    flex-direction: column;
}
.document-header-row {
    width: 90vw;
    margin: auto;
    padding-left: 8px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
}

.xl-container {
}

@media only screen and (min-width: 110em) {
    .xl-container {
        width: 105rem !important;
    }
}
@media only screen and (min-width: 120em) {
    .xl-container {
        width: 115rem !important;
    }
}
