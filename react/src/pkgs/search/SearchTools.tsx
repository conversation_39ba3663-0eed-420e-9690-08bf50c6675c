import {
    Box,
    Button,
    <PERSON>alog,
    <PERSON>alogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormGroup,
    Switch,
    Typography
} from '@mui/material'
import AppAccordion from '@/common/components/AppAccordion'
import React, { useEffect } from 'react'
import { ContentIndexingConfig } from '@/pkgs/search/types'
import { httpGet, httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { z } from 'zod'
import { useAppContext } from '@/pkgs/auth/atoms'

type SearchToolsProps = {
    contentID: string
    value?: ContentIndexingConfig
    onChange: (config: ContentIndexingConfig) => void
    isDistributed?: boolean
}

export function SearchTools({
    contentID,
    value = {
        ExcludeFromIndex: false,
        IndexFullPage: false,
        IndexForAllSites: false,
        IndexAllPrivacyLevels: false
    },
    onChange,
    isDistributed = false
}: SearchToolsProps) {
    if (value.ExcludeFromIndex) {
        value.IndexFullPage = false
        value.IndexForAllSites = false
        value.IndexAllPrivacyLevels = false
    } else if (isDistributed) {
        value.IndexForAllSites = true
    }

    const appContext = useAppContext()

    const [loading, setLoading] = React.useState(false)
    const [asJson, setAsJson] = React.useState<any[] | undefined>(undefined)

    useEffect(() => {
        if (isDistributed && !value.IndexForAllSites && !value.ExcludeFromIndex) {
            onChange({ ...value, IndexForAllSites: true })
        }
    }, [isDistributed])

    const handleChange = (key: keyof ContentIndexingConfig) => {
        const newValue = !value[key]
        onChange({ ...value, [key]: newValue })
    }

    const handlePreview = async () => {
        setLoading(true)
        try {
            const res = await httpGet(`${BASE}/api/v1/index/content/${contentID}`, null, z.array(z.any()))
            setAsJson(res)
        } catch (err) {
            notify(guessErrorMessage(err), 'error')
        } finally {
            setLoading(false)
        }
    }

    const handleReindex = async () => {
        setLoading(true)
        try {
            await httpPut(`${BASE}/api/v1/index/content/${contentID}`, null)
        } catch (err) {
            notify(guessErrorMessage(err), 'error')
        } finally {
            setLoading(false)
        }
    }

    return (
        <>
            <AppAccordion
                unmountOnExit
                defaultExpanded={false}
                withoutPadding
                summary={'Search Tools'}
                details={
                    <>
                        <Box sx={{ padding: 1 }}>
                            <FormControl component={'fieldset'} fullWidth>
                                <FormGroup sx={{}}>
                                    <FormControlLabel
                                        sx={{ justifyContent: 'space-between' }}
                                        control={<Switch />}
                                        label={'Exclude From Index'}
                                        labelPlacement={'start'}
                                        onChange={() => handleChange('ExcludeFromIndex')}
                                        checked={value.ExcludeFromIndex}
                                        disabled={loading}
                                    />

                                    {value.ExcludeFromIndex ? null : (
                                        <>
                                            <FormControlLabel
                                                sx={{ justifyContent: 'space-between' }}
                                                control={<Switch />}
                                                label={'Index For All Sites'}
                                                labelPlacement={'start'}
                                                onChange={() => handleChange('IndexForAllSites')}
                                                checked={value.IndexForAllSites}
                                                disabled={!!isDistributed || loading}
                                            />
                                            <FormControlLabel
                                                sx={{ justifyContent: 'space-between' }}
                                                control={<Switch />}
                                                label={'Index Full Page'}
                                                labelPlacement={'start'}
                                                onChange={() => handleChange('IndexFullPage')}
                                                checked={value.IndexFullPage}
                                                disabled={loading}
                                            />
                                            <FormControlLabel
                                                sx={{ justifyContent: 'space-between' }}
                                                control={<Switch />}
                                                label={'Index All Privacy Levels'}
                                                labelPlacement={'start'}
                                                onChange={() => handleChange('IndexAllPrivacyLevels')}
                                                checked={value.IndexAllPrivacyLevels}
                                                disabled={loading}
                                            />

                                            <Button
                                                disabled={loading}
                                                variant={'outlined'}
                                                fullWidth
                                                sx={{ mt: 1 }}
                                                onClick={handlePreview}
                                            >
                                                Preview index
                                            </Button>
                                            {/*<Button*/}
                                            {/*    disabled={loading}*/}
                                            {/*    variant={'contained'}*/}
                                            {/*    fullWidth*/}
                                            {/*    sx={{ mt: 1 }}*/}
                                            {/*    onClick={handleReindex}*/}
                                            {/*>*/}
                                            {/*    Reindex*/}
                                            {/*</Button>*/}
                                        </>
                                    )}
                                </FormGroup>
                            </FormControl>
                        </Box>

                        {asJson && (
                            <Dialog
                                open={Boolean(asJson)}
                                onClose={() => setAsJson(undefined)}
                                fullWidth
                                maxWidth={'xl'}
                            >
                                <DialogTitle>{`Index Data (${asJson.length} version(s))`}</DialogTitle>
                                <DialogContent>
                                    {Array.isArray(asJson) ? (
                                        asJson.map((item, idx) => {
                                            const content = item.Content

                                            const sites = item.Sites.map((id) => appContext.getSiteName(id)).join(', ')

                                            const forJson = { ...item }
                                            delete forJson.Content
                                            delete forJson.Sites

                                            return (
                                                <Box
                                                    key={idx}
                                                    sx={{ mb: 3, border: 'lightgray 1px solid', padding: '0 5px' }}
                                                >
                                                    <Typography variant={'subtitle1'}>
                                                        Index for site(s): {sites}
                                                    </Typography>
                                                    <pre style={{ whiteSpace: 'break-spaces' }}>{content}</pre>
                                                    <pre style={{ maxHeight: '300px' }}>
                                                        {JSON.stringify(forJson, null, 4)}
                                                    </pre>
                                                </Box>
                                            )
                                        })
                                    ) : (
                                        <pre>{JSON.stringify(asJson, null, 4)}</pre>
                                    )}
                                </DialogContent>
                                <DialogActions>
                                    <Button
                                        onClick={() => {
                                            setAsJson(undefined)
                                        }}
                                    >
                                        Close
                                    </Button>
                                </DialogActions>
                            </Dialog>
                        )}
                    </>
                }
            />
        </>
    )
}
