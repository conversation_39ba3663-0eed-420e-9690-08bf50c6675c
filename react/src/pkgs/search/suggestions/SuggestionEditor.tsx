import { suggestion, SuggestionDTO, suggestionDtoValidate } from '@/pkgs/search/suggestions/types'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    Card,
    DialogActions,
    DialogContent,
    Grid,
    IconButton,
    Stack,
    TextField,
    Typography
} from '@mui/material'
import { PublishPeriodControl } from '@/pkgs/content/editor/components/PublishPeriodControl'
import React, { useEffect, useState } from 'react'
import { KeywordsInput } from '@/pkgs/search/promotions/KeywordsInput'
import { useQuery } from '@tanstack/react-query'
import { baseQueryConfig } from '@/common/react-query'
import { httpGet, httpPost, httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import DeleteIcon from '@mui/icons-material/Delete'
import CMDialog from '@/common/components/CMDialog'

interface SuggestionEditorProps {
    id: string | null
    onClose: () => void
    open: boolean
}

const defaultSuggestion = {
    Name: '',
    PublishAt: new Date(),
    ExpireAt: null,
    Keywords: [],
    ExactMatch: true,
    Items: ['']
}

export function SuggestionEditor({ id, onClose, open }: SuggestionEditorProps) {
    const [pid, setPId] = useState<string | null>(id)
    const [state, setState] = useState<SuggestionDTO | undefined>(id ? undefined : defaultSuggestion)
    const [errors, setErrors] = useState<Partial<Record<keyof SuggestionDTO, string>>>({})
    const [dirty, setDirty] = useState(false)
    const [saving, setSaving] = useState(false)
    const [duplicates, setDuplicates] = useState<number[]>([])

    const result = useQuery({
        ...baseQueryConfig,
        enabled: !!pid,
        queryKey: ['suggestion', pid],
        queryFn: async () => httpGet(`${BASE}/api/v1/suggestions/${pid}`, {}, suggestion)
    })
    useEffect(() => {
        if (dirty) {
            validate()
        }
    }, [state])

    useEffect(() => {
        setDuplicates(findDuplicateIndexes(state?.Items || []))
        if (!state) {
            return
        }
        if (state.Items[state.Items.length - 1] !== '') {
            setState({ ...state, Items: [...state.Items, ''] })
        }
    }, [state?.Items])

    const validate = () => {
        setDirty(true)
        try {
            suggestionDtoValidate.parse(state)
            setErrors({})
            return true
        } catch (e) {
            const zError = e as z.ZodError
            const errors = zError.errors.reduce(
                (acc, curr) => {
                    acc[curr.path[0]] = curr.message
                    return acc
                },
                {} as Partial<Record<keyof SuggestionDTO, string>>
            )
            setErrors(errors)
            return false
        }
    }

    const save = async () => {
        if (!validate() || !state) {
            return
        }

        state.Items = state.Items.filter((str) => str.trim() !== '')
        try {
            setSaving(true)
            if (pid) {
                const res = await httpPut(`${BASE}/api/v1/suggestions/${pid}`, state)
            } else {
                const res = await httpPost(`${BASE}/api/v1/suggestions`, state, z.string())
                res && setPId(res)
            }
        } catch (e) {
            notify(guessErrorMessage(e), 'error')
        } finally {
            setSaving(false)
        }
    }

    useEffect(() => {
        if (result.data) {
            setPId(result.data.ID)
            if (result.data.Items[result.data.Items.length - 1] !== '') {
                setState({ ...result.data, Items: [...result.data.Items, ''] })
            } else {
                setState(result.data)
            }
        }
    }, [result.data])

    return (
        <div>
            <CMDialog
                open={open}
                onClose={onClose}
                showCloseButton={true}
                title={pid ? 'Edit Suggestion' : 'Add Suggestion'}
                fullWidth
                maxWidth={'xl'}
            >
                <DialogContent>
                    {state && (
                        <Grid container spacing={2}>
                            <Grid item xs={4}>
                                <PublishPeriodControl
                                    value={state}
                                    onChange={(v) =>
                                        setState({
                                            ...state,
                                            ExpireAt: v.ExpireAt,
                                            PublishAt: v.PublishAt
                                        })
                                    }
                                    errors={errors}
                                />
                            </Grid>
                            <Grid container item xs={8} spacing={2}>
                                <Grid item xs={12}>
                                    <TextField
                                        sx={{ my: 1 }}
                                        label='Title'
                                        value={state.Name}
                                        onChange={(e) => setState({ ...state, Name: e.target.value })}
                                        error={!!errors.Name}
                                        helperText={errors.Name}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <KeywordsInput
                                        value={state}
                                        onChange={(v) =>
                                            setState({
                                                ...state,
                                                Keywords: v.Keywords,
                                                ExactMatch: v.ExactMatch
                                            })
                                        }
                                        error={errors.Keywords}
                                    />
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Typography variant={'h6'} sx={{ m: 2 }}>
                                    Suggestions
                                </Typography>
                                {duplicates.length > 0 && (
                                    <Alert severity={'warning'} sx={{ m: 2 }}>
                                        Duplicate phrases found at positions: {duplicates.map((i) => i + 1).join(', ')}
                                    </Alert>
                                )}

                                {state.Items.map((item, i) => (
                                    <Stack direction={'row'} key={i} sx={{ p: 1, my: 0 }}>
                                        <TextField
                                            error={duplicates.includes(i)}
                                            size={'small'}
                                            label={`Phrase ${i + 1}`}
                                            value={item}
                                            onChange={(e) => {
                                                const newItems = [...state.Items]
                                                if (e.target.value) {
                                                    newItems[i] = e.target.value
                                                } else {
                                                    newItems.splice(i, 1)
                                                }
                                                setState({ ...state, Items: newItems })
                                            }}
                                        />
                                        <IconButton
                                            sx={{ minWidth: '40px', ml: 1 }}
                                            size={'small'}
                                            onClick={() => {
                                                const newItems = [...state.Items]
                                                newItems.splice(i, 1)
                                                setState({ ...state, Items: newItems })
                                            }}
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                        <Card />
                                    </Stack>
                                ))}
                            </Grid>
                        </Grid>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={onClose}>Close</Button>
                    <Button
                        disabled={saving}
                        variant={'contained'}
                        onClick={async () => {
                            if (!validate()) {
                                return
                            }

                            await save()
                            onClose()
                        }}
                    >
                        Save and Close
                    </Button>
                    <Button disabled={saving} variant={'contained'} onClick={save}>
                        Save
                    </Button>
                </DialogActions>
            </CMDialog>
        </div>
    )
}

function findDuplicateIndexes(arr: string[]): number[] {
    const indexMap = new Map<string, number[]>()

    // First pass: collect all indexes for each string
    arr.forEach((str, index) => {
        if (!indexMap.has(str)) {
            indexMap.set(str, [index])
        } else {
            indexMap.get(str)!.push(index)
        }
    })

    // Second pass: filter to only keep strings with multiple occurrences
    const result: number[] = []
    indexMap.forEach((indexes, str) => {
        if (indexes.length > 1) {
            result.push(...indexes)
        }
    })

    return result
}
