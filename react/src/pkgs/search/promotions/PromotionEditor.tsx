import { promotion, PromotionDTO, promotionDtoValidate } from '@/pkgs/search/promotions/types'
import { <PERSON>ton, Card, DialogActions, DialogContent, Grid, Stack, TextField } from '@mui/material'
import { PublishPeriodControl } from '@/pkgs/content/editor/components/PublishPeriodControl'
import React, { useEffect, useState } from 'react'
import { KeywordsInput } from '@/pkgs/search/promotions/KeywordsInput'
import { useQuery } from '@tanstack/react-query'
import { baseQueryConfig } from '@/common/react-query'
import { httpGet, httpPost, httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { ItemEditor } from '@/pkgs/search/promotions/ItemEditor'
import { AddButton } from '@/common/components'
import { ItemRow } from '@/pkgs/search/promotions/ItemRow'
import CMDialog from '@/common/components/CMDialog'

interface PromotionEditorProps {
    id: string | null
    onClose: () => void
    open: boolean
}

const defaultPromotion = {
    Name: '',
    PublishAt: new Date(),
    ExpireAt: null,
    Keywords: [],
    ExactMatch: true,
    Items: []
}

export function PromotionEditor({ id, onClose, open }: PromotionEditorProps) {
    const [pid, setPId] = useState<string | null>(id)
    const [state, setState] = useState<PromotionDTO | undefined>(id ? undefined : defaultPromotion)
    const [errors, setErrors] = useState<Partial<Record<keyof PromotionDTO, string>>>({})
    const [dirty, setDirty] = useState(false)
    const [saving, setSaving] = useState(false)
    const [editItemIndex, setEditItemIndex] = useState<number | null>(null)

    const result = useQuery({
        ...baseQueryConfig,
        enabled: !!pid,
        queryKey: ['promotion', pid],
        queryFn: async () => httpGet(`${BASE}/api/v1/promotions/${pid}`, {}, promotion)
    })
    useEffect(() => {
        if (dirty) {
            validate()
        }
    }, [state])

    const validate = () => {
        setDirty(true)
        try {
            promotionDtoValidate.parse(state)
            setErrors({})
            return true
        } catch (e) {
            const zError = e as z.ZodError
            const errors = zError.errors.reduce(
                (acc, curr) => {
                    acc[curr.path[0]] = curr.message
                    return acc
                },
                {} as Partial<Record<keyof PromotionDTO, string>>
            )
            setErrors(errors)
            return false
        }
    }

    const save = async () => {
        if (!validate()) {
            return
        }

        try {
            setSaving(true)
            if (pid) {
                const res = await httpPut(`${BASE}/api/v1/promotions/${pid}`, state)
            } else {
                const res = await httpPost(`${BASE}/api/v1/promotions`, state, z.string())
                res && setPId(res)
            }
        } catch (e) {
            notify(guessErrorMessage(e), 'error')
        } finally {
            setSaving(false)
        }
    }

    useEffect(() => {
        if (result.data) {
            setState(result.data)
            setPId(result.data.ID)
        }
    }, [result.data])

    return (
        <div>
            <CMDialog
                open={open}
                onClose={onClose}
                showCloseButton={true}
                fullWidth
                maxWidth={'xl'}
                title={pid ? 'Edit Promotion' : 'Add Promotion'}
            >
                <DialogContent>
                    {state && (
                        <Grid container spacing={2}>
                            <Grid item xs={4}>
                                <PublishPeriodControl
                                    value={state}
                                    onChange={(v) =>
                                        setState({
                                            ...state,
                                            ExpireAt: v.ExpireAt,
                                            PublishAt: v.PublishAt
                                        })
                                    }
                                    errors={errors}
                                />
                            </Grid>
                            <Grid container item xs={8} spacing={2}>
                                <Grid item xs={12}>
                                    <TextField
                                        sx={{ my: 1 }}
                                        label='Title'
                                        value={state.Name}
                                        onChange={(e) => setState({ ...state, Name: e.target.value })}
                                        error={!!errors.Name}
                                        helperText={errors.Name}
                                    />
                                </Grid>
                                <Grid item xs={12}>
                                    <KeywordsInput
                                        value={state}
                                        onChange={(v) =>
                                            setState({
                                                ...state,
                                                Keywords: v.Keywords,
                                                ExactMatch: v.ExactMatch
                                            })
                                        }
                                        error={errors.Keywords}
                                    />
                                </Grid>
                            </Grid>
                            <Grid item xs={12}>
                                <Stack direction='row' spacing={2} justifyContent='flex-end'>
                                    <AddButton func={() => setEditItemIndex(-1)} title={'Add Item'} />
                                </Stack>

                                {state.Items.sort((a, b) => a.DisplayOrder - b.DisplayOrder).map((item, i) => (
                                    <Card key={i} sx={{ p: 1, my: 2 }}>
                                        <ItemRow
                                            item={item}
                                            index={i}
                                            onDelete={(i) => {
                                                if (!confirm(`Are you sure you want to delete ${item.Title}?`)) {
                                                    return
                                                }
                                                state &&
                                                    setState({
                                                        ...state,
                                                        Items: state.Items.filter((_, idx) => idx !== i)
                                                    })
                                            }}
                                            onEdit={(i) => setEditItemIndex(i)}
                                        />
                                    </Card>
                                ))}
                            </Grid>
                        </Grid>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={onClose}>Close</Button>
                    <Button
                        disabled={saving}
                        variant={'contained'}
                        onClick={async () => {
                            if (!validate()) {
                                return
                            }

                            await save()
                            onClose()
                        }}
                    >
                        Save and Close
                    </Button>
                    <Button disabled={saving} variant={'contained'} onClick={save}>
                        Save
                    </Button>
                </DialogActions>
            </CMDialog>

            {state && editItemIndex !== null && (
                <ItemEditor
                    isOpen={true}
                    onClose={() => setEditItemIndex(null)}
                    value={
                        state.Items[editItemIndex]
                            ? state.Items[editItemIndex]
                            : {
                                  Title: '',
                                  Description: '',
                                  Link: '',
                                  ExtID: null,
                                  Type: 'none',
                                  DisplayOrder: state.Items.length
                              }
                    }
                    onSave={(item) => {
                        if (!!state.Items[editItemIndex]) {
                            setState({
                                ...state,
                                Items: state.Items.map((i, idx) => (idx === editItemIndex ? item : i))
                            })
                        } else {
                            setState({ ...state, Items: [...state.Items, item] })
                            setEditItemIndex(null)
                        }
                    }}
                />
            )}
        </div>
    )
}
