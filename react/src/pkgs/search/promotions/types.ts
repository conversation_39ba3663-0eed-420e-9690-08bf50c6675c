import { z } from 'zod'
import { entity, paged, PagingQuery, SortingQuery, trackable, uuidSchema } from '@/common/react-query'
import { publishPeriod } from '@/pkgs/content/types'

export type PromotionsQuery = {
    Search?: string
    Inactive?: boolean
} & PagingQuery &
    SortingQuery

export const keywordSearch = z.object({
    Keywords: z.array(z.string()),
    ExactMatch: z.boolean()
})

export const item = z
    .object({
        ExtID: uuidSchema.nullish(),
        Type: z.string(),
        DisplayOrder: z.number(),
        Title: z.string(),
        Description: z.string(),
        Link: z.string().nullish()
    })
    .superRefine((data, ctx) => {
        // If ExtID is null/undefined, check Title and Link
        if (!data.ExtID) {
            if (!data.Title || data.Title.trim() === '') {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Title is required when ExtID is not provided',
                    path: ['Title']
                })
            }

            if (!data.Link || data.Link.trim() === '') {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Link is required when ExtID is not provided',
                    path: ['Link']
                })
            }
        }

        if (data.Link) {
            const urlPattern = new RegExp(
                '^(https?:\\/\\/)?' + // protocol
                    '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name
                    '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
                    '(\\:\\d+)?(\\/[-a-z\\d%_.~+]*)*' + // port and path
                    '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
                    '(\\#[-a-z\\d_]*)?$', // fragment
                'i'
            )
            if (!urlPattern.test(data.Link)) {
                ctx.addIssue({
                    code: z.ZodIssueCode.custom,
                    message: 'Link must be a valid URL',
                    path: ['Link']
                })
            }
        }
    })

export const promotionDto = z.object({
    ...keywordSearch.shape,
    ...publishPeriod.shape,
    Name: z.string(),
    Items: z.array(item)
})

export const promotion = z.object({
    ...promotionDto.shape,
    ...entity.shape,
    ...trackable.shape,
    Active: z.boolean()
})

export const searchPromotionResult = paged.extend({
    Rows: z.array(promotion)
})

export const promotionDtoValidate = promotionDto.extend({
    Keywords: z.array(z.string()).nonempty('Keywords must not be empty'),
    Name: z.string().min(3, 'Name must be at least 3 characters')
})

export type Promotion = z.infer<typeof promotion>
export type PromotionDTO = z.infer<typeof promotionDto>
export type Item = z.infer<typeof item>
export type KeywordSearch = z.infer<typeof keywordSearch>
