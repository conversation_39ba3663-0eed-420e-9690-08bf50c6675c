import CenteredSpinner from '@/common/components/CenteredSpinner'
import { Alert, Chip, Link } from '@mui/material'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { Item } from '@/pkgs/search/promotions/types'
import { usePreview } from '@/pkgs/search/promotions/usePreview'
import { getColorByType } from '@/pkgs/search/types'
import React from 'react'

interface ContentSnippetProps {
    item: Item
}

export function ItemSnippet({ item }: ContentSnippetProps) {
    const result = usePreview(item)

    if (result.isLoading) {
        return <CenteredSpinner />
    }
    if (result.isError) {
        return <Alert severity='error'>{guessErrorMessage(result.error)}</Alert>
    }

    const content = result.data
    if (!content) {
        return <Alert severity='error'>Content not found</Alert>
    }
    return (
        <div>
            <Link href={content.PreviewLink} target={content.Item.ExtID || '_blank'}>
                {content.Title}
            </Link>
            <div>
                <Chip
                    size={'small'}
                    color={getColorByType(content.Item.Type)}
                    label={content.Item.Type}
                    sx={{ my: 1 }}
                />
            </div>
            {content.PrivacyLevel > 0 && <Alert severity={'warning'}>The content is only available for staff. </Alert>}
        </div>
    )
}
