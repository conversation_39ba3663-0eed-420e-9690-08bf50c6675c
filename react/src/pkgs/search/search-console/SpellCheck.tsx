import { useEffect, useState } from 'react'
import { Al<PERSON>, <PERSON> } from '@mui/material'
import { httpGet } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

type SpellCheckProps = {
    value: string
    onChange: (value: string) => void
}

export function SpellCheck({ value, onChange }: SpellCheckProps) {
    const [suggestion, setSuggestion] = useState('')
    const [error, setError] = useState<any>(undefined)

    useEffect(() => {
        setError(undefined)
        if (!value) {
            return
        }
        const fetchSuggestion = async () => {
            try {
                const response = await httpGet(`${BASE}/api/v1/index/spellcheck`, { SearchText: value }, z.string())
                setSuggestion(response)
            } catch (error) {
                setError(error)
            }
        }

        fetchSuggestion()
    }, [value])

    if (!suggestion) {
        return null
    }

    if (error) {
        return <Alert severity='error'>{guessErrorMessage(error)}</Alert>
    }

    return (
        <div>
            <strong>Did you mean:</strong> &nbsp;
            <Link
                href='#'
                onClick={() => {
                    onChange(suggestion)
                    setSuggestion('')
                }}
            >
                {suggestion}
            </Link>
            ?
        </div>
    )
}
