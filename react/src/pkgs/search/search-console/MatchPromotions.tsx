import { useAppContext } from '@/pkgs/auth/atoms'
import { useQuery } from '@tanstack/react-query'
import { baseQueryConfig } from '@/common/react-query'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { Al<PERSON>, Box, Card, IconButton, Link, Typography } from '@mui/material'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { httpGet } from '@/common/client'
import { BASE } from '@/common/constants'
import { z } from 'zod'
import { PromotionEditor } from '@/pkgs/search/promotions/PromotionEditor'
import React, { useState } from 'react'
import { EditOutlined } from '@mui/icons-material'

type Query = {
    SearchText: string
    AllSites: boolean
}

interface MatchPromotionsParams {
    query: Query
}

export function MatchPromotions({ query }: MatchPromotionsParams) {
    const appContext = useAppContext()
    const [pid, setPId] = useState<string | undefined>(undefined)
    const result = useQuery({
        ...baseQueryConfig,
        enabled: query.SearchText.length > 0,
        queryKey: ['match-promotions', query],
        queryFn: async () => await httpGet(`${BASE}/api/v1/search/match-promotions`, query, promotionsSchema)
    })

    if (result.isLoading) {
        return <CenteredSpinner />
    }

    if (result.isError) {
        return (
            <Alert sx={{ p: 1, m: 1 }} severity='error'>
                {guessErrorMessage(result.error)}
            </Alert>
        )
    }

    if (result.data.length === 0) {
        return (
            <Alert sx={{ p: 1, m: 1 }} severity='info'>
                No promotions found
            </Alert>
        )
    }

    const hasPermission = appContext.entityScopeAny('cm.full_text_search')
    return (
        <>
            {result.data.map((promotion, idx) => (
                <Card key={idx} sx={{ p: 1, m: 1 }}>
                    <Typography variant={'subtitle2'}>
                        <Link href={promotion.Link} target='_blank'>
                            {promotion.Title}
                        </Link>
                    </Typography>
                    <p>{promotion.Description}</p>

                    {hasPermission && (
                        <Box display='flex' justifyContent='flex-end'>
                            <IconButton
                                title={'Edit related Promotion'}
                                size='small'
                                onClick={() => setPId(promotion.PID)}
                            >
                                <EditOutlined />
                            </IconButton>
                        </Box>
                    )}
                </Card>
            ))}

            {hasPermission && pid !== undefined && (
                <PromotionEditor
                    id={pid}
                    onClose={() => {
                        setPId(undefined)
                        result.refetch()
                    }}
                    open={true}
                />
            )}
        </>
    )
}

const promotionsSchema = z.array(
    z.object({
        Title: z.string(),
        Description: z.string(),
        Link: z.string(),
        PID: z.string()
    })
)
