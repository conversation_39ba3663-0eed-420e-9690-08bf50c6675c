import { UseQueryResult } from '@tanstack/react-query'
import { z } from 'zod'
import { getColorByType, SearchResult, searchResults } from '@/pkgs/search/types'
import CenteredSpinner from '@/common/components/CenteredSpinner'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { Alert, Chip, IconButton, Link, Stack } from '@mui/material'
import React from 'react'
import { useAppContext } from '@/pkgs/auth/atoms'
import { Bar<PERSON><PERSON>, EditOutlined } from '@mui/icons-material'
import DocumentEditor from '@/pkgs/storage/DocumentEditor'

export function SearchResults({ results }: { results: UseQueryResult<z.infer<typeof searchResults>> }) {
    const appContext = useAppContext()
    const [documentId, setDocumentId] = React.useState<string | undefined>(undefined)

    if (results.isLoading) {
        return <CenteredSpinner />
    }
    if (results.isError) {
        return <Alert severity='error'>{guessErrorMessage(results.error)}</Alert>
    }

    const getUrl = (result: SearchResult) => {
        let domain = appContext.currentSite()?.PrimaryDomain
        if (!result.Sites.some((s) => s === appContext.currentSiteID)) {
            domain = appContext.getSite(result.Sites[0])?.PrimaryDomain
        }

        if (!domain) {
            return '#'
        }

        if (window?.location?.host.includes('localhost') && !domain.includes('localhost')) {
            domain += '.localhost'
        }

        return result.Type === 'document'
            ? `https://${domain}/documents/${result.ExtID}/${result.Route}`
            : `https://${domain}${result.Route}`
    }

    return (
        <div>
            {results.data.Rows.map((r, index) => {
                const editLink = r.Type === 'document' ? `` : `/content-editor/${r.ExtID}`
                return (
                    <div key={r.ExtID} style={{ borderBottom: '1px solid lightgray', padding: '10px 0' }}>
                        <span>{index + 1 + results.data.Offset}</span>.&nbsp;
                        <Link
                            typography={{ fontSize: '1.2rem' }}
                            href={getUrl(r)}
                            target='_blank'
                            rel='noreferrer'
                            dangerouslySetInnerHTML={{
                                __html: `${r.Title}`
                            }}
                        />
                        <p dangerouslySetInnerHTML={{ __html: r.Snippet }} />
                        <Stack direction='row' spacing={1} alignItems='center' justifyContent='flex-end'>
                            {editLink && (
                                <IconButton
                                    size={'small'}
                                    onClick={() => window.open(editLink, r.ExtID)}
                                    title={`Edit related ${r.Type}`}
                                >
                                    <EditOutlined />
                                </IconButton>
                            )}
                            {r.Type === 'document' && (
                                <IconButton
                                    size={'small'}
                                    onClick={() => setDocumentId(r.ExtID)}
                                    title={`Edit related ${r.Type}`}
                                >
                                    <EditOutlined />
                                </IconButton>
                            )}
                            <Chip size={'small'} color={getColorByType(r.Type)} label={r.Type} /> &nbsp;
                            {r.Rank > 0 && <Chip size={'small'} icon={<BarChart />} label={r.Rank.toFixed(10)} />}
                        </Stack>
                    </div>
                )
            })}

            {documentId && (
                <DocumentEditor
                    documentId={documentId}
                    isOpen={!!documentId}
                    onClose={() => setDocumentId(undefined)}
                />
            )}
        </div>
    )
}
