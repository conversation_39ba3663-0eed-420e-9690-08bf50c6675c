import { <PERSON>, Button, <PERSON>, <PERSON>ack, Typography } from '@mui/material'
import { IDToNameCell } from '../grid/cells/GridCells'
import moment from 'moment'
import { useAppNavigation } from '../../app/useAppNavigation'
import { colours } from '../../common/colours'
import CenteredSpinner from '../../common/components/CenteredSpinner'
import { HistoryType, sanitizeSysPeriod, useHistoryQuery } from '../history/queries'
import { getPublishStatus, publishStatusColour } from './editor/ContentEditorSaveBar'
import { Content } from '@/pkgs/content/types'
import { ObjectAsJson } from '@/common/ObjectAsJson'
import React, { useState } from 'react'
import useContentDiff from './editor/diff/useContentDiff'
import HistoryIcon from '@mui/icons-material/History'
import { useSetAtom } from 'jotai'
import { contentEditorAtom } from '../auth/atoms'

// import CompareIcon from '@mui/icons-material/Compare'

function Dot({ styles }) {
    const size = '8px'
    return (
        <span
            style={{
                height: size,
                width: size,
                backgroundColor: colours.orange,
                borderRadius: '50%',
                display: 'inline-block',
                marginLeft: '5px',
                ...styles
            }}
        ></span>
    )
}

interface RevisionHistoryListProps {
    content: Content
    onRevertHandler: (newValue: Content) => void
    disabled?: boolean
}

export function RevisionHistoryList({ content, onRevertHandler, disabled }: RevisionHistoryListProps) {
    const { data, isLoading } = useHistoryQuery(content.ID, HistoryType.CONTENT)
    const { navigateTo } = useAppNavigation()
    const [asJson, setAsJson] = React.useState(undefined)
    const revisionHistoryExists = !!data?.Rows

    const [selectedRevisionHistory, setSelectedRevisionHistory] = useState<Content | null>(null)

    const { changed, renderContentDiffViewer, setContentDiffViewerIsOpen } = useContentDiff({
        serverValue: selectedRevisionHistory || undefined,
        value: content,
        hasExternalChanges: false,
        title: (
            <Stack direction='row' alignItems='center' gap='8px'>
                <HistoryIcon />
                <Typography variant='subtitle1'>
                    {moment(selectedRevisionHistory?.Updated).format('YYYY-MM-DD, h:mm:ss a')}
                </Typography>
            </Stack>
        ),
        onRevert: (newValue, callback) => {
            onRevertHandler(newValue)
        },
        disabled
    })

    if (isLoading) {
        return <CenteredSpinner />
    }

    return (
        <Box maxHeight={'500px'} overflow={'auto'}>
            <Box
                sx={{
                    padding: '12px',
                    '&:hover': {
                        backgroundColor: colours.off_white
                    }
                }}
            >
                <Typography variant={'subtitle1'}>Current version:</Typography>
                <Box display={'flex'} gap={'8px'} alignItems={'center'} flexWrap={'wrap'}>
                    <Typography variant={'subtitle2'}>Created:</Typography>
                    {moment(content.Created).format('MM/DD/YYYY, h:mm a')}
                    <Typography variant={'subtitle2'}>Updated:</Typography>
                    {moment(content.Updated).format('MM/DD/YYYY, h:mm a')}
                </Box>
                {content.Owner === content.Publisher ? (
                    <Box>
                        <Typography variant={'subtitle2'}>Owner, Publisher:</Typography>
                        <IDToNameCell tableName={'account'} ID={content.Owner} />
                    </Box>
                ) : (
                    <>
                        <Box>
                            <Typography variant={'subtitle2'}>Owner:</Typography>
                            <IDToNameCell tableName={'account'} ID={content.Owner} />
                        </Box>
                        <Box>
                            <Typography variant={'subtitle2'}>Publisher:</Typography>
                            <IDToNameCell tableName={'account'} ID={content.Publisher} />
                        </Box>
                    </>
                )}
                <Button variant={'outlined'} onClick={() => setAsJson(content.Settings)}>
                    Settings column
                </Button>
            </Box>

            {revisionHistoryExists &&
                data.Rows.map((row, idx) => {
                    const publishStatus = getPublishStatus(row.PublishAt, row.ExpireAt)
                    const dotColour = publishStatusColour[publishStatus]

                    return (
                        <Box
                            key={row.SysPeriod}
                            sx={{
                                padding: '12px',
                                '&:hover': {
                                    backgroundColor: colours.off_white,
                                    cursor: 'pointer'
                                }
                            }}
                            onClick={() => {
                                setSelectedRevisionHistory(row)
                                setContentDiffViewerIsOpen(true)
                            }}
                        >
                            <Stack direction='row' spacing='8px' sx={{ alignItems: 'center' }}>
                                <Dot styles={{ backgroundColor: dotColour }} />
                                <Typography variant='subtitle1'>
                                    {moment(sanitizeSysPeriod(row.SysPeriod)).format('MM/DD/YYYY, h:mm a')}
                                </Typography>
                            </Stack>
                            <IDToNameCell tableName={'account'} ID={row.Publisher} />
                            <Button
                                size={'small'}
                                onClick={(e) => {
                                    e.stopPropagation()
                                    navigateTo(`/user-management/accounts/${row.Publisher}`, undefined, true)
                                }}
                            >
                                publisher
                            </Button>
                        </Box>
                    )
                })}
            {renderContentDiffViewer()}

            {asJson && <ObjectAsJson obj={asJson} title={`Settings column`} onClose={() => setAsJson(undefined)} />}
        </Box>
    )
}
