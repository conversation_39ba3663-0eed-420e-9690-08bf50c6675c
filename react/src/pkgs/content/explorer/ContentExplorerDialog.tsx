import { DialogContent } from '@mui/material'
import { ContentExplorer, ContentExplorerProps } from './ContentExplorer'
import CMDialog from '@/common/components/CMDialog'

interface ContentExplorerDialogProps extends ContentExplorerProps {
    isOpen: boolean
    onClose: () => void
}

export function ContentExplorerDialog({ isOpen, onClose, ...contentExplorerProps }: ContentExplorerDialogProps) {
    return (
        <CMDialog title='Content Explorer' showCloseButton open={isOpen} onClose={onClose} fullWidth maxWidth={'xl'}>
            <DialogContent style={{ paddingTop: '8px' }}>
                <ContentExplorer {...contentExplorerProps} />
            </DialogContent>
        </CMDialog>
    )
}
