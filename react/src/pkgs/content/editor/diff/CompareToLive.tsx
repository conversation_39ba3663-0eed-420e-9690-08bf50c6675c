import { Content } from '@/pkgs/content/types'
import { useContentQueries } from '@/pkgs/content/queries'
import useContentDiff from '@/pkgs/content/editor/diff/useContentDiff'
import { useEffect } from 'react'
import CMDialog from '@/common/components/CMDialog'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import DialogContent from '@mui/material/DialogContent'
import { Alert } from '@mui/material'

interface CompareToLiveProps<T extends Content> {
    value: T
    onRevert?: (newValue: T, callback?: () => void) => void
    open: boolean
    onClose: () => void
}

export function CompareToLive<T extends Content>({ value, onRevert, open, onClose }: CompareToLiveProps<T>) {
    const { fetcher } = useContentQueries({ id: value.ID, workspace: 'live' })
    const { renderContentDiffViewer, setContentDiffViewerIsOpen, contentDiffViewerIsOpen } = useContentDiff({
        serverValue: fetcher.data,
        value: value,
        hasExternalChanges: false,
        leftTitle: value.Workspace,
        title: 'Live',
        onRevert: (newValue, callback) => {
            onRevert?.(newValue as T, callback)
        }
    })

    useEffect(() => {
        setContentDiffViewerIsOpen(open)
    }, [open])

    useEffect(() => {
        if (!contentDiffViewerIsOpen) {
            onClose()
        }
    }, [contentDiffViewerIsOpen])

    if (open && fetcher.error) {
        return (
            <CMDialog open={open} onClose={() => setContentDiffViewerIsOpen(false)} title={'Error'}>
                <DialogContent>
                    <Alert severity='error'>Error loading live content: {guessErrorMessage(fetcher.error)}</Alert>
                </DialogContent>
            </CMDialog>
        )
    }

    return renderContentDiffViewer()
}
