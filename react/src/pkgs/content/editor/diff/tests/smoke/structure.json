{"ID": "1ad21cb3-2f7e-4011-beca-06efe90d0dc3", "Active": true, "CreatedAt": "2024-12-09T12:46:36.91627-08:00", "CreatedBy": "11d38fd1-6871-4cc5-b463-eeb5772decaa", "UpdatedAt": "2025-04-23T09:02:08.094854-07:00", "UpdatedBy": "d2014f44-b779-4f10-8490-91a329d0889d", "Name": "Job Posting", "Template": "\n<div class=\"job-info\">\n    {{#jsonExists Dct \"jobInfo.term\"}}\n    <div>\n        <span>Term:</span>\n        <span>{{DctMap.jobInfo.term}}</span>\n    </div>\n    <div>\n        <span>Location:</span>\n        <span>{{DctMap.jobInfo.location}}</span>\n    </div>\n    <div>\n        <span>Contract #:</span>\n        <span>{{DctMap.jobInfo.contract}}</span>\n    </div>\n    <div>\n        <span>FTE:</span>\n        <span>{{DctMap.jobInfo.fte}}</span>\n    </div>\n    {{/jsonExists}}\n    {{#if CurrentContent.ExpireAt}}\n    <div>\n        <span>Closing Date:</span>\n        <span class=\"format-date\">{{CurrentContent.ExpireAt}}</span>\n    </div>\n    {{/if}}\n</div>\n\n{{#jsonExists Dct \"mainContent.lexical.html\"}}\n<div class=\"editor\">{{{DctMap.mainContent.lexical.html}}}</div>\n{{/jsonExists}}", "FormStructure": [{"name": "jobInfo", "title": "Job Info", "components": [{"key": true, "name": "image1", "type": "image", "label": "Image", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "term", "type": "text", "label": "Term", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "image2", "type": "image", "label": "Image", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "location", "type": "text", "label": "Location", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "contract", "type": "text", "label": "Contract #", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "fte", "type": "text", "label": "FTE", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "lexical", "type": "rich-text", "label": "Content", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "document", "type": "document", "label": "Document", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "date", "type": "date", "label": "Date", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "text<PERSON>e", "type": "textarea", "label": "textarea", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "cfl", "type": "contact-form-link", "label": "cfl", "required": false, "validate": "", "maximumLength": ""}]}, {"name": "mainContent", "title": "Main Content", "components": [{"key": true, "name": "lexical", "type": "rich-text", "label": "Content", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "derp image", "type": "image", "label": "Image", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "example checkbox", "type": "checkbox", "label": "Example checkbox", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "example select", "type": "select", "label": "Example select", "options": ["option 1", "option2", "option3"], "required": false, "validate": "", "maximumLength": ""}], "description": "Use the styles dropdown to change text from the 'Normal' style to a heading or list to make your content easier to scan, or use the Insert function to include images in your main content area.", "allowMultiple": false}, {"name": "ALLOWMULTIPLETEST", "title": "Main Content ALLOW MULTIPLE TEST", "components": [{"key": true, "name": "lexical", "type": "rich-text", "label": "Content", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "derp image", "type": "image", "label": "Image", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "example checkbox", "type": "checkbox", "label": "Example checkbox", "required": false, "validate": "", "maximumLength": ""}, {"key": true, "name": "example select", "type": "select", "label": "Example select", "options": ["option 1", "option2", "option3"], "required": false, "validate": "", "maximumLength": ""}], "description": "Use the styles dropdown to change text from the 'Normal' style to a heading or list to make your content easier to scan, or use the Insert function to include images in your main content area.", "allowMultiple": true}]}