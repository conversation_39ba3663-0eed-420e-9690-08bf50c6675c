import { readFile } from 'fs/promises'
import { getContentChanges } from '../getContentChanges'
import { expect, it } from 'vitest'
import { Structure } from '@/pkgs/structure/types'
import { Content } from '@/pkgs/content/types'

describe('getContentChanges works', async () => {
    it('smoke', async () => {
        const testStructure = JSON.parse(await readFile(`${__dirname}/smoke/structure.json`, 'utf-8')) as Structure

        const currentContent = JSON.parse(await readFile(`${__dirname}/smoke/currentContent.json`, 'utf-8')) as Content
        const historyContent = JSON.parse(await readFile(`${__dirname}/smoke/historyContent.json`, 'utf-8')) as Content

        const contentChanges = getContentChanges(historyContent, currentContent, testStructure)
        expect(contentChanges).toMatchFileSnapshot(`./smoke/smoke.snap.html`)
    })
})
