.testRemoveTreeLine .rst__lineBlock {
    display: none;
    width: 0;
    left: 10px;
}
.testRemoveTreeLine .rst__placeholder {
    display: none;
}
.testRemoveTreeLine .rst__tree {
}
.testRemoveTreeLine .rst__nodeContent {
    width: 100%;
}
.testRemoveTreeLine .rst__rowWrapper {
    margin: 5px 5px 5px 0;
    padding: 5px 5px 5px 0;
    width: 100%;
}
.rst__rowLabel {
    /*max-width: 15rem;*/
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.Navigation-TypeDepartment > .rst__rowContents {
    /*background-color: mediumpurple;;*/
}
.rst__rowContents {
    cursor: pointer;
    background-color: #f7f7f7;
    /*border-radius: 0;*/
}
.selected-tree-node {
    /*background-color: #e6e6e6;*/
    border: solid black 1px;
}
.rst__rowSearchMatch {
    outline: solid 3px #0080ff61;
}
.rst__rowSearchFocus {
    outline: solid 3px #2196f3;
}
.btn-group .button:first-child {
    border-radius: 6px 0 0 0px;
}
.non-interactive-node .rst__rowContents {
    background-color: #e8e8e8;
    border: 1px solid #c9c9c9;
    cursor: default;
}
.navigation-options {
    width: 95%;
    padding: 1.5vh;
    margin-bottom: 1vh;
    margin-top: 1vh;
}
.navigation-options-margin {
    margin-top: 1vh;
    margin-bottom: 1vh;
}
.navigation-options-auto {
    width: 98%;
    margin-top: 1vh !important;
    margin-bottom: 1vh !important;
}
.non-dialog-container {
    height: 4.5vh;
    display: grid;
    grid-template-columns: 25% 50% 24%;
    grid-template-rows: 4% 4% 42% 42%;
    min-height: 90vh;
}

@media only screen and (max-width: 1368px) {
    .non-dialog-container {
        height: 4.5vh;
        display: grid;
        grid-template-columns: 32% 43% 24%;
        grid-template-rows: 4% 4% 42% 42%;
        min-height: 90vh;
    }
}

.explorer {
    grid-column-start: 2;
    grid-column-end: 3;
    grid-row-start: 2;
    grid-row-end: span 4;
}
#navigation-tip-overlay {
    grid-column-start: 3;
    grid-row-start: 2;
}
#navigation-save {
    grid-column-start: 3;
    grid-row-start: 4;
    grid-row-end: span 2;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    margin-bottom: 0;
}
.rst__tree {
    max-height: 75vh;
    min-height: 10vh;
}

.rst__external-node {
    height: 32px;
    margin: 0.15rem 0px 0.15rem 0px;
    /*margin: 5px 0px 5px 0px;*/
    min-width: 25px;
}

.rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTExIDE4YzAgMS4xLS45IDItMiAycy0yLS45LTItMiAuOS0yIDItMiAyIC45IDIgMnptLTItOGMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAtNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTYgNGMxLjEgMCAyLS45IDItMnMtLjktMi0yLTItMiAuOS0yIDIgLjkgMiAyIDJ6bTAgMmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAgNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6Ii8+PC9zdmc+')
        no-repeat center;
    border-radius: 0;
    width: 35px;
    min-width: 35px;
}
.Navigation-TypeDepartment .rst__moveHandle {
    background: #333333
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDI0IDI0IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0cHgiIGZpbGw9IiMwMDAwMDAiPjxyZWN0IGZpbGw9Im5vbmUiIGhlaWdodD0iMjQiIHdpZHRoPSIyNCIvPjxwYXRoIGQ9Ik0xMiw3VjNIMnYxOGgyMFY3SDEyeiBNMTAsMTlINHYtMmg2VjE5eiBNMTAsMTVINHYtMmg2VjE1eiBNMTAsMTFINFY5aDZWMTF6IE0xMCw3SDRWNWg2Vjd6IE0yMCwxOWgtOFY5aDhWMTl6IE0xOCwxMWgtNHYyIGg0VjExeiBNMTgsMTVoLTR2Mmg0VjE1eiIvPjwvc3ZnPg==')
        no-repeat center;
    border-radius: 0;
    background-color: #b4a5d0;
    background-size: 1.15rem;
}
.Navigation-TypePage .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iNDgiIHdpZHRoPSI0OCI+PHBhdGggZD0iTTE1Ljk1IDM1LjVoMTYuMXYtM2gtMTYuMVptMC04LjVoMTYuMXYtM2gtMTYuMVpNMTEgNDRxLTEuMiAwLTIuMS0uOVE4IDQyLjIgOCA0MVY3cTAtMS4yLjktMi4xUTkuOCA0IDExIDRoMTguMDVMNDAgMTQuOTVWNDFxMCAxLjItLjkgMi4xLS45LjktMi4xLjlabTE2LjU1LTI3LjdWN0gxMXYzNGgyNlYxNi4zWk0xMSA3djkuM1Y3djM0VjdaIi8+PC9zdmc+')
        no-repeat center;
    border-radius: 0;
    background-size: 20px;
}
.Navigation-TypeExternal .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iNDgiIHdpZHRoPSI0OCI+PHBhdGggZD0iTTIyLjUgMzRIMTRxLTQuMjUgMC03LjEyNS0yLjg3NVQ0IDI0cTAtNC4yNSAyLjg3NS03LjEyNVQxNCAxNGg4LjV2M0gxNHEtMyAwLTUgMnQtMiA1cTAgMyAyIDV0NSAyaDguNVptLTYuMjUtOC41di0zaDE1LjV2M1pNMjUuNSAzNHYtM0gzNHEzIDAgNS0ydDItNXEwLTMtMi01dC01LTJoLTguNXYtM0gzNHE0LjI1IDAgNy4xMjUgMi44NzVUNDQgMjRxMCA0LjI1LTIuODc1IDcuMTI1VDM0IDM0WiIvPjwvc3ZnPg==')
        no-repeat center;
    border-radius: 0;
    background-color: #c9d7ed;
    background-size: 20px;
}
.Navigation-Disabled .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iNDgiIHdpZHRoPSI0OCI+PHBhdGggZD0iTTExIDQ0cS0xLjI1IDAtMi4xMjUtLjg3NVQ4IDQxVjE5LjNxMC0xLjI1Ljg3NS0yLjEyNVQxMSAxNi4zaDMuNXYtNC44cTAtMy45NSAyLjc3NS02LjcyNVEyMC4wNSAyIDI0IDJxMy45NSAwIDYuNzI1IDIuNzc1UTMzLjUgNy41NSAzMy41IDExLjV2NC44SDM3cTEuMjUgMCAyLjEyNS44NzVUNDAgMTkuM1Y0MXEwIDEuMjUtLjg3NSAyLjEyNVQzNyA0NFptNi41LTI3LjdoMTN2LTQuOHEwLTIuNy0xLjktNC42UTI2LjcgNSAyNCA1cS0yLjcgMC00LjYgMS45LTEuOSAxLjktMS45IDQuNlpNMTEgNDFoMjZWMTkuM0gxMVY0MVptMTMtN3ExLjYgMCAyLjcyNS0xLjF0MS4xMjUtMi42NXEwLTEuNS0xLjEyNS0yLjcyNVQyNCAyNi4zcS0xLjYgMC0yLjcyNSAxLjIyNVQyMC4xNSAzMC4yNXEwIDEuNTUgMS4xMjUgMi42NVEyMi40IDM0IDI0IDM0Wm0wLTMuODVaIi8+PC9zdmc+')
        no-repeat center;
    border-radius: 0;
    height: 32px;
    background-size: 1.4rem;
    width: 35px;
    cursor: not-allowed;
}
.rst__missingPermission {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iNDgiIHdpZHRoPSI0OCI+PHBhdGggZD0iTTExIDQ0cS0xLjI1IDAtMi4xMjUtLjg3NVQ4IDQxVjE5LjNxMC0xLjI1Ljg3NS0yLjEyNVQxMSAxNi4zaDMuNXYtNC44cTAtMy45NSAyLjc3NS02LjcyNVEyMC4wNSAyIDI0IDJxMy45NSAwIDYuNzI1IDIuNzc1UTMzLjUgNy41NSAzMy41IDExLjV2NC44SDM3cTEuMjUgMCAyLjEyNS44NzVUNDAgMTkuM1Y0MXEwIDEuMjUtLjg3NSAyLjEyNVQzNyA0NFptNi41LTI3LjdoMTN2LTQuOHEwLTIuNy0xLjktNC42UTI2LjcgNSAyNCA1cS0yLjcgMC00LjYgMS45LTEuOSAxLjktMS45IDQuNlpNMTEgNDFoMjZWMTkuM0gxMVY0MVptMTMtN3ExLjYgMCAyLjcyNS0xLjF0MS4xMjUtMi42NXEwLTEuNS0xLjEyNS0yLjcyNVQyNCAyNi4zcS0xLjYgMC0yLjcyNSAxLjIyNVQyMC4xNSAzMC4yNXEwIDEuNTUgMS4xMjUgMi42NVEyMi40IDM0IDI0IDM0Wm0wLTMuODVaIi8+PC9zdmc+')
        no-repeat center;
    border-radius: 0;
    height: 32px;
    background-size: 1.4rem;
    width: 45px;
    cursor: not-allowed;
}
.canMoveSVG {
    background: white
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTExIDE4YzAgMS4xLS45IDItMiAycy0yLS45LTItMiAuOS0yIDItMiAyIC45IDIgMnptLTItOGMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAtNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTYgNGMxLjEgMCAyLS45IDItMnMtLjktMi0yLTItMiAuOS0yIDIgLjkgMiAyIDJ6bTAgMmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAgNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6Ii8+PC9zdmc+')
        no-repeat center;
}
.cantMoveSVG {
    background: white
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDI0IDI0IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0cHgiIGZpbGw9IiMwMDAwMDAiPjxnPjxyZWN0IGZpbGw9Im5vbmUiIGhlaWdodD0iMjQiIHdpZHRoPSIyNCIvPjwvZz48Zz48Zz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iMTAiLz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iNiIvPjxyZWN0IGhlaWdodD0iMiIgd2lkdGg9IjciIHg9IjMiIHk9IjE0Ii8+PHBvbHlnb24gcG9pbnRzPSIyMC41OSwxMS45MyAxNi4zNCwxNi4xNyAxNC4yMiwxNC4wNSAxMi44MSwxNS40NiAxNi4zNCwxOSAyMiwxMy4zNCIvPjwvZz48L2c+PC9zdmc+')
        no-repeat center;
}

/* Page Search Control - When node is present in the tree */
.Navigation-PageSearchList .Navigation-ExistsInTree .rst__moveHandle {
    /*background:#d9d9d9 url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTExIDE4YzAgMS4xLS45IDItMiAycy0yLS45LTItMiAuOS0yIDItMiAyIC45IDIgMnptLTItOGMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAtNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTYgNGMxLjEgMCAyLS45IDItMnMtLjktMi0yLTItMiAuOS0yIDIgLjkgMiAyIDJ6bTAgMmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAgNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6Ii8+PC9zdmc+") no-repeat center;*/
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDI0IDI0IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0cHgiIGZpbGw9IiMwMDAwMDAiPjxnPjxyZWN0IGZpbGw9Im5vbmUiIGhlaWdodD0iMjQiIHdpZHRoPSIyNCIvPjwvZz48Zz48Zz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iMTAiLz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iNiIvPjxyZWN0IGhlaWdodD0iMiIgd2lkdGg9IjciIHg9IjMiIHk9IjE0Ii8+PHBvbHlnb24gcG9pbnRzPSIyMC41OSwxMS45MyAxNi4zNCwxNi4xNyAxNC4yMiwxNC4wNSAxMi44MSwxNS40NiAxNi4zNCwxOSAyMiwxMy4zNCIvPjwvZz48L2c+PC9zdmc+')
        no-repeat center !important;
    pointer-events: none;
    cursor: none;
}
.Navigation-PageSearchList .Navigation-ExistsInTree .rst__rowContents {
    pointer-events: none;
    cursor: none;
}

/* Page Search Control - When node is NOT present in the tree */
.Navigation-PageSearchList .Navigation-MissingFromTree .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTExIDE4YzAgMS4xLS45IDItMiAycy0yLS45LTItMiAuOS0yIDItMiAyIC45IDIgMnptLTItOGMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAtNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTYgNGMxLjEgMCAyLS45IDItMnMtLjktMi0yLTItMiAuOS0yIDIgLjkgMiAyIDJ6bTAgMmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAgNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6Ii8+PC9zdmc+')
        no-repeat center;
}
.Navigation-PageSearchList .Navigation-Disabled .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAtOTYwIDk2MCA5NjAiIHdpZHRoPSIyNHB4IiBmaWxsPSIjMDAwMDAwIj48cGF0aCBkPSJNMjQwLTgwcS0zMyAwLTU2LjUtMjMuNVQxNjAtMTYwdi00MDBxMC0zMyAyMy41LTU2LjVUMjQwLTY0MGg0MHYtODBxMC04MyA1OC41LTE0MS41VDQ4MC05MjBxODMgMCAxNDEuNSA1OC41VDY4MC03MjB2ODBoNDBxMzMgMCA1Ni41IDIzLjVUODAwLTU2MHY0MDBxMCAzMy0yMy41IDU2LjVUNzIwLTgwSDI0MFptMC04MGg0ODB2LTQwMEgyNDB2NDAwWm0yNDAtMTIwcTMzIDAgNTYuNS0yMy41VDU2MC0zNjBxMC0zMy0yMy41LTU2LjVUNDgwLTQ0MHEtMzMgMC01Ni41IDIzLjVUNDAwLTM2MHEwIDMzIDIzLjUgNTYuNVQ0ODAtMjgwWk0zNjAtNjQwaDI0MHYtODBxMC01MC0zNS04NXQtODUtMzVxLTUwIDAtODUgMzV0LTM1IDg1djgwWk0yNDAtMTYwdi00MDAgNDAwWiIvPjwvc3ZnPg==')
        no-repeat center;
}


.Navigation-Searched .rst__moveHandle {
    background: #d9d9d9
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDI0IDI0IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0cHgiIGZpbGw9IiMwMDAwMDAiPjxnPjxyZWN0IGZpbGw9Im5vbmUiIGhlaWdodD0iMjQiIHdpZHRoPSIyNCIvPjwvZz48Zz48Zz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iMTAiLz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iNiIvPjxyZWN0IGhlaWdodD0iMiIgd2lkdGg9IjciIHg9IjMiIHk9IjE0Ii8+PHBvbHlnb24gcG9pbnRzPSIyMC41OSwxMS45MyAxNi4zNCwxNi4xNyAxNC4yMiwxNC4wNSAxMi44MSwxNS40NiAxNi4zNCwxOSAyMiwxMy4zNCIvPjwvZz48L2c+PC9zdmc+')
        no-repeat center !important;
    pointer-events: none;
    cursor: none;
}
.Navigation-Searched.Navigation-ExistsInTree .rst__moveHandle {
    background: white
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGhlaWdodD0iMjRweCIgdmlld0JveD0iMCAwIDI0IDI0IiB3aWR0aD0iMjRweCIgZmlsbD0iIzAwMDAwMCI+PHBhdGggZD0iTTAgMGgyNHYyNEgwVjB6IiBmaWxsPSJub25lIi8+PHBhdGggZD0iTTExIDE4YzAgMS4xLS45IDItMiAycy0yLS45LTItMiAuOS0yIDItMiAyIC45IDIgMnptLTItOGMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAtNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTYgNGMxLjEgMCAyLS45IDItMnMtLjktMi0yLTItMiAuOS0yIDIgLjkgMiAyIDJ6bTAgMmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6bTAgNmMtMS4xIDAtMiAuOS0yIDJzLjkgMiAyIDIgMi0uOSAyLTItLjktMi0yLTJ6Ii8+PC9zdmc+')
        no-repeat center;
    pointer-events: none;
    cursor: none;
}
.Navigation-Searched .rst__rowContents {
    pointer-events: none;
    cursor: none;
}
.rst__cantMoveHandle {
    background: white
        url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIGVuYWJsZS1iYWNrZ3JvdW5kPSJuZXcgMCAwIDI0IDI0IiBoZWlnaHQ9IjI0cHgiIHZpZXdCb3g9IjAgMCAyNCAyNCIgd2lkdGg9IjI0cHgiIGZpbGw9IiMwMDAwMDAiPjxnPjxyZWN0IGZpbGw9Im5vbmUiIGhlaWdodD0iMjQiIHdpZHRoPSIyNCIvPjwvZz48Zz48Zz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iMTAiLz48cmVjdCBoZWlnaHQ9IjIiIHdpZHRoPSIxMSIgeD0iMyIgeT0iNiIvPjxyZWN0IGhlaWdodD0iMiIgd2lkdGg9IjciIHg9IjMiIHk9IjE0Ii8+PHBvbHlnb24gcG9pbnRzPSIyMC41OSwxMS45MyAxNi4zNCwxNi4xNyAxNC4yMiwxNC4wNSAxMi44MSwxNS40NiAxNi4zNCwxOSAyMiwxMy4zNCIvPjwvZz48L2c+PC9zdmc+')
        no-repeat center !important;
    border-radius: 0;
    height: 32px;
    width: 48px;
    border: solid #bbb 1px;
    border-right: none;
    z-index: 1;
}
.Navigation-Disabled .rst__rowContents {
    cursor: not-allowed;
    background-color: #f0f0f0;
    /*padding: 0;*/
    justify-content: normal;
}
/*.Navigation-Disabled .rst__rowContents::before {*/
/*    font-family: "Material Icons";*/
/*    content: "\e897";*/
/*    height: 100%;*/
/*    background-color: #d9d9d9;*/
/*    width: 33px;*/
/*    border: solid #aaa 1px;*/
/*    -webkit-box-shadow: 0 2px 2px -2px;*/
/*    box-shadow: 0 2px 2px -2px;*/
/*    border-radius: 1px;*/
/*    z-index: 1;*/
/*    display: flex;*/
/*    align-items: center;*/
/*    justify-content: center;*/
/*}*/
.Navigation-DisabledTree .rst__rowContents::before {
    border-left: 0;
}
.Navigation-Disabled .rst__rowToolbar {
    margin-left: auto;
}
.Navigation-Primary .rst__rowLabel::before {
    font-family: 'Material Icons';
    content: '\f10d';
    vertical-align: top;
    font-size: 18px;
    /*margin-right: 5px;*/
    color: #f6c94c;
}
/*.Navigation-InactiveContent .rst__rowLabel::before {*/
/*    font-family: 'Material Icons';*/
/*    content: "\e92b";*/
/*    vertical-align: top;*/
/*    font-size: 18px;*/
/*    margin-right: 5px;*/
/*    color: #f6c94c*/
/*}*/
.Navigation-InactiveContent .rst__rowToolbar::before {
    font-family: 'Material Icons';
    align-self: center;
    content: '\e92b';
    vertical-align: top;
    font-size: 18px;
    /*margin-right: 5px;*/
    color: #f6c94c;
}
.Navigation-MissingSiteOverlap .rst__rowToolbar::before {
    font-family: 'Material Icons';
    align-self: center;
    content: '\f1c2';
    vertical-align: top;
    font-size: 18px;
    /*margin-right: 5px;*/
    color: #ff8800;
}
.Navigation-MissingSiteOverlapIcon::before {
    font-family: 'Material Icons';
    content: '\f1c2';
    font-size: 30px;
    height: 35px;
    width: 35px;
    margin: 2px;
    margin-right: 15px;
    margin-left: 4px;
    color: #ff8800;
}
.Navigation-Disabled.Navigation-Primary .rst__rowLabel::before {
    margin-left: 5px;
}

/*.Navigation-DraftContent .rst__rowLabel::after {*/
/*    font-family: 'Material Icons';*/
/*    content: "\e745";*/
/*    vertical-align: top;*/
/*    font-size: 18px;*/
/*    margin-left: 5px;*/
/*    color: #5F6368;*/
/*}*/
.Navigation-DraftContent .rst__rowToolbar::before {
    font-family: 'Material Icons';
    align-self: center;
    content: '\e745';
    vertical-align: top;
    font-size: 18px;
    /*margin-left: 5px;*/
    color: #5f6368;
}
.Navigation-DraftContentIcon::before {
    font-family: 'Material Icons';
    content: '\e745';
    font-size: 30px;
    height: 35px;
    width: 35px;
    margin: 2px;
    margin-right: 15px;
    margin-left: 4px;
}
.Navigation-PrimaryIcon::before {
    font-family: 'Material Icons';
    content: '\f10d';
    font-size: 30px;
}
.Navigation-PrimaryIconSmall::before {
    font-family: 'Material Icons';
    content: '\f10d';
    font-size: 20px;
    color: #f6c94c;
}

.external-node-list {
    /*background-color:#F0F0F0;*/
    max-height: 15vh;
    padding: 5px;
    /*overflow-x: hidden;*/
    overflow-y: scroll;
}

.node-option-container:hover svg {
    opacity: 0.9 !important;
    pointer-events: auto !important;
    cursor: pointer !important;
}
.node-option-container .rst__rowContents {
    padding: 0.15rem 0 0.15rem 0.35rem;
    border-radius: 0 5px 5px 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    max-width: 90%;
}
.node-option-container .rst__moveHandle {
    padding: 0.15rem 0 0.15rem 0;
    border-radius: 5px 0 0 5px;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.node-option {
    opacity: 0;
    transition: opacity 0.2s linear !important;
    pointer-events: none;
}

.tree-example img {
    width: 100%;
    height: auto;
}

.tree-example .show-on-hover {
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    transition: opacity 0.2s linear;
}

.tree-example:hover .show-on-hover {
    opacity: 1;
}
.tree-example {
    position: relative;
    height: 90%;
    width: 90%;
    margin-bottom: 15px;
}
.nav-card-title {
    padding: 5%;
    padding-bottom: 0;
    font-size: 1.35em;
}
.tooltip-notch-none::after {
    display: none;
}
/*.m5 {*/
/*    margin:5px;*/
/*}*/
/*.m10 {*/
/*    margin:10px;*/
/*}*/
/*.m15 {*/
/*    margin:15px*/
/*}*/
/*.m20 {*/
/*    margin:20px;*/
/*}*/
/*.mt1rem {*/
/*    margin-top:1rem;*/
/*}*/
/*.mt10 {*/
/*    margin-top:10px;*/
/*}*/
/*.mt5p {*/
/*    margin-top:5%*/
/*}*/
/*.mb10 {*/
/*    margin-bottom:10px;*/
/*}*/
/*.mb5p {*/
/*    margin-bottom:5%*/
/*}*/
/*.m5p {*/
/*    margin:5%;*/
/*}*/
/*.p5p {*/
/*    padding:5%;*/
/*}*/
/*.pt5p {*/
/*    padding-top:5%;*/
/*}*/
/*.pt5 {*/
/*    padding-top:5px;*/
/*}*/
/*.pb5 {*/
/*    padding-bottom: 5px;*/
/*}*/
/*.pt10 {*/
/*    padding-top:10px;*/
/*}*/
/*.pb10 {*/
/*    padding-bottom: 10px;*/
/*}*/
/*.ml5 {*/
/*    margin-left:5px;*/
/*}*/
/*.ml5p {*/
/*    margin-left:5%*/
/*}*/
/*.mr5 {*/
/*    margin-right:5px;*/
/*}*/
/*.mr5p {*/
/*    margin-right: 5%*/
/*}*/
/*.p10 {*/
/*    padding: 10px;*/
/*}*/
/*.p20 {*/
/*    padding: 20px;*/
/*}*/
/*.max-height-and-width {*/
/*    width: 100%;*/
/*    height:100%;*/
/*}*/
/*.max-width {*/
/*    width: 100%;*/
/*}*/
/*.border-box {*/
/*    box-sizing: border-box;*/
/*}*/
/*.half-width {*/
/*    width:50%;*/
/*}*/
/*.half-height {*/
/*    height:50%;*/
/*}*/
/*.max-height {*/
/*    height:100%;*/
/*}*/
/*.flex-column-center {*/
/*    display:flex;*/
/*    flex-direction: column;*/
/*    align-items: center;*/
/*}*/

/* {*/
/*    content: "";*/
/*    position: absolute;*/
/*    top: 100%;*/
/*    left: 50%;*/
/*    margin-left: -5px;*/
/*    border-width: 5px;*/
/*    border-style: solid;*/
/*    border-color: #555 transparent transparent transparent;*/
/*}*/
