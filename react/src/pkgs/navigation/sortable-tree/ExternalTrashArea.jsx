import React from 'react'
import { DropTarget } from 'react-dnd'
import { ExternalNodeType } from './ExternalNode_v2'

/**
 *
 *  This DND Component is unused
 *
 * */

const trashAreaSpec = {
    // The endDrag handler on the tree source will use some of the properties of
    // the source, like node, treeIndex, and path to determine where it was before.
    // The treeId must be changed, or it interprets it as dropping within itself.
    drop: (props, monitor) => ({ ...monitor.getItem(), treeId: 'trash' })
}
const trashAreaCollect = (connect, monitor) => ({
    connectDropTarget: connect.dropTarget(),
    isOver: monitor.isOver({ shallow: true })
})

// The component will sit around the tree component and catch
// nodes dragged out
const TrashAreaBaseComponent = (props) => {
    const { connectDropTarget, children, isOver } = props

    return connectDropTarget(
        <div
            style={{
                height: '100vh',
                padding: 50,
                background: isOver ? 'pink' : 'transparent'
            }}
        >
            {children}
        </div>
    )
}

export const ExternalTrashArea = DropTarget(ExternalNodeType, trashAreaSpec, trashAreaCollect)(TrashAreaBaseComponent)
