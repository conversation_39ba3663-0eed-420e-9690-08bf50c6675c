import React, { useContext, useEffect } from 'react'
import { DragSource } from 'react-dnd'
import { Get } from '../../../helpers'
import { emittedEventContext } from '../useEmitter'

export const ExternalNodeType = 'orphan'
const externalNodeSpec = {
    // This needs to return an object with a property `node` in it.
    // Object rest spread is recommended to avoid side effects of
    // referencing the same object in different trees.
    beginDrag: (componentProps) => ({ node: { ...componentProps.node } })
}

// const externalNodeCollect = (connect /* , monitor */) => ({
const externalNodeCollect = (connect, monitor) => ({
    connectDragSource: connect.dragSource(),
    // Add props via react-dnd APIs to enable more visual
    // customization of your component
    // isDragging: monitor.isDragging(),s
    didDrop: monitor.didDrop(),
    getDropResult: monitor.getDropResult(),
    getItem: monitor.getItem(),
    isDragging: monitor.isDragging(),
    monitor,
    getItemType: monitor.getItemType()
    // isOver: monitor.isOver?.({ shallow: true }),
    // canDrop: monitor.canDrop(),
})

export function ExternalNodeElement({ Buttons, node, customClass: cc, disabled }) {
    const classname = ` flex-row ml5 mr5 
        ${cc ? cc : 'Navigation-External'} 
        ${disabled && 'Navigation-Disabled'}
        ${Get.isPrimary(node) && 'Navigation-Primary'}
    `
    return (
        <div className={classname}>
            <div className='rst__moveHandle' style={{ height: '32px' }} draggable={false} />
            <div className='rst__rowContents rst__external-node  node-option-container'>
                <div className='rst__rowLabel'>
                    <span className='rst__rowTitle'>{node?.content?.title || node?.title}</span>
                </div>
                {!disabled && <div className='rst__rowToolbar'>{Buttons && (Buttons?.() || <Buttons />)}</div>}
            </div>
        </div>
    )
}

const ExternalNodeDragBase = (props) => {
    const [, emitEvent] = useContext(emittedEventContext)
    const { connectDragSource, didDrop, getDropResult, getItemType, node, Buttons, customClass, disabled } = props

    useEffect(() => {
        if (didDrop && getItemType === ExternalNodeType && getDropResult?.dropEffect === 'move') {
            emitEvent({ message: 'TreeChange', id: getDropResult?.node?.id })
        }
    }, [didDrop])

    return connectDragSource(
        // ExternalNodeElement must be wrapped in div
        //  "Only native element nodes can now be passed to React DnD connectors."
        //  "You can either wrap the component into a <div>, or turn it into a drag source or a drop target itself."
        <div>
            <ExternalNodeElement {...{ node, Buttons, customClass, disabled }} />
        </div>,
        { dropEffect: 'move', draggable: false }
    )
}
export const ExternalNode = DragSource(ExternalNodeType, externalNodeSpec, externalNodeCollect)(ExternalNodeDragBase)
