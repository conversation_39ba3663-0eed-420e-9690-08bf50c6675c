import { List } from './types'
import { Base, isContentLikeContent } from '../content/types'
import {
    Checkbox,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    FormControl,
    FormControlLabel,
    FormGroup,
    FormHelperText,
    FormLabel,
    Grid,
    Switch
} from '@mui/material'
import { BaseForm } from '../content/BaseForm'
import { EntityScopeEnum } from '../auth/entityScope'
import TextField from '@mui/material/TextField'
import { StructureSelector } from '../structure/StructureSelector'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import Button from '@mui/material/Button'
import { httpPost, httpPut } from '@/common/client'
import { _isEqual, notify } from '../../helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { SchemaSectionsSelector } from '../structure/SchemaSectionsSelector'
import Alert from '@mui/material/Alert'
import { CodeEditor } from '../monaco/code-editor'
import { TagsSelector } from '@/pkgs/system/tags/TagsSelector'
import { ConfirmAction } from '../../common/components'
import { BASE } from '@/common/constants'
import { TagType } from '../system/tags/types'
import { useAppContext } from '@/pkgs/auth/atoms'
import CMDialog from '@/common/components/CMDialog'
import { colours } from '@/common/colours'

export const availableContentTypes = ['fragment', 'page', 'news', 'event'] as const

export type ListFormData = Base &
    Pick<
        List,
        | 'ID'
        | 'Name'
        | 'Description'
        | 'StructureID'
        | 'Template'
        | 'ContentTypes'
        | 'Structure'
        | 'OverrideSections'
        | 'Tags'
        | 'Distributed'
    >
type Payload = Omit<ListFormData, 'ID'>

interface ListFormProps {
    value: ListFormData | undefined
    open: boolean
    // We separate onClose and onSave to prevent users from accidentally closing the dialog and potentially erasing their unsaved changes
    onClose: () => void
    onSave: () => void
    editAs: 'individual' | 'group'
}

function getStateValidation(state: ListFormData): Partial<Record<keyof ListFormData, string>> {
    const result: Partial<Record<keyof ListFormData, string>> = {}

    if (!state.Name) {
        result.Name = 'Name is required'
    }
    if (!state.StructureID) {
        result.StructureID = 'Structure is required'
    }
    if (!state.ContentTypes.length) {
        result.ContentTypes = 'Content Types are required'
    }

    if (!state.Distributed) {
        if (!state.Sites.length) {
            result.Sites = 'Sites are required'
        }
    }

    return result
}

export const ListForm = ({ value, open, onClose, onSave, editAs }: ListFormProps) => {
    const mode = value ? 'edit' : 'create'
    const appContext = useAppContext()

    const defaultValue: ListFormData = {
        Description: '',
        ID: '',
        Name: '',
        Structure: null,
        StructureID: '',
        ContentTypes: [],
        Template: '{{ListItems}}',
        PublishAt: new Date(),
        OverrideSections: [],
        Sites: appContext.getDefaultSitesForSelectors(),
        DepartmentID: appContext.getCurrentSiteDepartmentID(),
        Tags: [],
        PrivacyLevel: 0,
        Distributed: false
    }
    const [state, setState] = useState<ListFormData>(value || defaultValue)
    const [stateErrors, setStateErrors] = useState<Record<string, string>>({})
    const [confirmCloseDialogIsOpen, setConfirmCloseDialogIsOpen] = useState(false)
    const [hiddenFields, setHiddenFields] = useState<Partial<Record<keyof Base, boolean>> | undefined>(undefined)

    const hasChanges = useMemo(
        () => (value == undefined ? !_isEqual(state, defaultValue, ['Sites']) : !_isEqual(state, value, ['Sites'])),
        [value, state]
    )

    const [loading, setLoading] = useState(false)

    useEffect(() => {
        if (value) {
            setState(value)
        }
    }, [value])

    useEffect(() => {
        if (state.Distributed) {
            setHiddenFields({ Sites: true })
            if (editAs === 'group') {
                setState({ ...state, Sites: [], DepartmentID: null })
            }
        } else {
            setHiddenFields(undefined)
        }
    }, [state.Distributed])

    const updateStateErrors = useCallback(
        (key, value) => {
            setStateErrors((p) => ({ ...p, [key]: value }))
        },
        [setStateErrors]
    )

    function handleDialogOnClose() {
        if (hasChanges) {
            setConfirmCloseDialogIsOpen(true)
        } else {
            onClose()
        }
    }

    const saveIndividual = (payload: Payload) => {
        const promise =
            mode === 'edit'
                ? httpPut(`${BASE}/api/v1/lists/${state.ID}`, payload)
                : httpPost(`${BASE}/api/v1/lists`, payload)

        promise
            .then(() => {
                notify('Saved!', 'info')
                setLoading(false)
                onSave()
                if (mode === 'create') {
                    onClose?.()
                }
            })
            .catch((e) => {
                setLoading(false)
                const msg = guessErrorMessage(e)
                if (msg.includes('Name')) {
                    setStateErrors({ ...stateErrors, Name: 'This name is already in use' })
                }
                notify(msg, 'error')
                if (msg.includes('name') && msg.includes('unique')) {
                    updateStateErrors('Name', 'This name is already in use')
                }
            })
    }

    const saveGroup = (payload: Payload) => {
        // @ts-ignore
        payload.Sites = null
        payload.DepartmentID = null

        const promise =
            mode === 'edit'
                ? httpPut(`${BASE}/api/v1/distributed-lists`, payload)
                : httpPost(`${BASE}/api/v1/distributed-lists`, payload)

        promise
            .then(() => {
                notify('Saved!', 'info')
                setLoading(false)
                onSave()
                if (mode === 'create') {
                    onClose?.()
                }
            })
            .catch((e) => {
                setLoading(false)
                const msg = guessErrorMessage(e)
                if (msg.includes('Name')) {
                    setStateErrors({ ...stateErrors, Name: 'This name is already in use' })
                }
                notify(msg, 'error')
                if (msg.includes('name') && msg.includes('unique')) {
                    updateStateErrors('Name', 'This name is already in use')
                }
            })
    }

    const handleSave = () => {
        if (!state) return

        const errors = getStateValidation(state)
        setStateErrors((p) => ({ ...p, ...errors }))
        if (Object.values(errors).find((v) => !!v)) {
            return
        }

        setLoading(true)
        const payload: Payload = {
            Name: state.Name,
            Description: state.Description,
            StructureID: state.StructureID,
            Template: state.Template,
            Tags: state.Tags,
            ContentTypes: state.ContentTypes,
            OverrideSections: state.OverrideSections,
            // Base
            Sites: state.Sites,
            DepartmentID: state.DepartmentID,
            PrivacyLevel: state.PrivacyLevel,
            PublishAt: state.PublishAt,
            ExpireAt: state.ExpireAt,
            Distributed: state.Distributed
        }

        const saveAsGroup = mode === 'create' ? payload.Distributed : editAs !== 'individual'

        if (saveAsGroup) {
            saveGroup(payload)
        } else {
            saveIndividual(payload)
        }
    }

    const disabledContentType = (contentType: (typeof availableContentTypes)[number]) => {
        if (mode === 'edit') return true
        if (contentType === 'fragment' && state && state.ContentTypes.some(isContentLikeContent)) return true
        if (contentType !== 'fragment' && state && state.ContentTypes.includes('fragment')) return true
        return false
    }

    return (
        <CMDialog
            open={open}
            onClose={() => {
                handleDialogOnClose()
            }}
            fullWidth
            maxWidth={'xl'}
            showCloseButton
            // @ts-ignore
            title={`${mode.toUpperCase()} ${state.Name || ''}`}
        >
            <DialogContent>
                <Grid container spacing={2}>
                    <Grid item xs={4} sx={{ marginTop: 3, paddingRight: 1 }}>
                        <FormControlLabel
                            control={<Switch disabled={mode === 'edit'} />}
                            label={'Distributed'}
                            labelPlacement={'end'}
                            onChange={() => setState((prev) => ({ ...prev, Distributed: !prev.Distributed }))}
                            checked={state.Distributed}
                            disabled={mode === 'edit'}
                        />

                        <BaseForm
                            config={{ moreSiteSelectorProps: { Required: true } }}
                            value={state}
                            onChange={(v) => v && setState(v)}
                            contentType={EntityScopeEnum.List}
                            errors={stateErrors}
                            disabledFields={
                                mode === 'create'
                                    ? state.Distributed
                                        ? { Sites: true }
                                        : false
                                    : state.Distributed
                                      ? editAs === 'group'
                                          ? { Sites: true }
                                          : {
                                                Sites: true,
                                                PrivacyLevel: true,
                                                PublishAt: true,
                                                ExpireAt: true
                                            }
                                      : false
                            }
                            hiddenFields={hiddenFields}
                        />

                        <FormControl fullWidth sx={{ my: 1 }}>
                            <TagsSelector
                                selected={state.Tags || []}
                                disabled={state.Distributed && mode === 'edit' && editAs === 'individual'}
                                tagTypes={[TagType.List]}
                                onChange={(tags) => tags && setState((prev) => ({ ...prev, Tags: tags }))}
                            />
                        </FormControl>
                    </Grid>

                    <Grid item container spacing={2} xs={8}>
                        <Grid item xs={12}>
                            <TextField
                                required
                                variant='standard'
                                error={!!stateErrors?.Name}
                                helperText={stateErrors?.Name}
                                label='Name'
                                style={{ width: '100%' }}
                                value={state.Name || ''}
                                onChange={(v) => {
                                    if (state) {
                                        setState({ ...state, Name: v.target.value })
                                    }
                                    updateStateErrors('Name', '')
                                }}
                                disabled={mode === 'edit' && state.Distributed}
                            />
                        </Grid>

                        <Grid item xs={12}>
                            <TextField
                                multiline
                                variant='standard'
                                label='Description'
                                value={state.Description || ''}
                                onChange={(v) => state && setState({ ...state, Description: v.target.value })}
                                disabled={mode === 'edit' && state.Distributed && editAs === 'group'}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <StructureSelector
                                error={stateErrors?.StructureID}
                                variant='standard'
                                required
                                disabled={mode === 'edit'}
                                onChange={(v) => {
                                    if (!state) return
                                    setState({ ...state, StructureID: v || '', OverrideSections: [] })
                                    updateStateErrors('StructureID', '')
                                }}
                                value={state.StructureID}
                            />
                        </Grid>
                        <Grid item xs={12}>
                            <FormLabel required error={!!stateErrors?.ContentTypes}>
                                Content Types
                            </FormLabel>
                            <FormGroup row>
                                {availableContentTypes.map((contentType) => {
                                    return (
                                        <FormControlLabel
                                            key={contentType}
                                            control={
                                                <Checkbox
                                                    disabled={disabledContentType(contentType)}
                                                    checked={state && state.ContentTypes.includes(contentType)}
                                                    onChange={(e) => {
                                                        if (!state) return

                                                        const checked = e.target.checked
                                                        const newContentTypes = checked
                                                            ? [...state.ContentTypes, contentType]
                                                            : state.ContentTypes.filter((ct) => ct !== contentType)
                                                        setState({
                                                            ...state,
                                                            ContentTypes: newContentTypes,
                                                            OverrideSections: []
                                                        })

                                                        if (stateErrors?.ContentTypes) {
                                                            updateStateErrors('ContentTypes', '')
                                                        }
                                                    }}
                                                />
                                            }
                                            sx={{ textTransform: 'capitalize' }}
                                            label={contentType}
                                        />
                                    )
                                })}
                            </FormGroup>
                            {!!stateErrors?.ContentTypes ? (
                                <FormHelperText error={true}>{stateErrors?.ContentTypes}</FormHelperText>
                            ) : undefined}
                        </Grid>
                        <Grid item xs={12}>
                            <FormLabel>Overridable Sections</FormLabel>
                            {!state.StructureID && <Alert severity='warning'>Select Structure</Alert>}
                            {!state.ContentTypes.length && <Alert severity='warning'>Select Content type</Alert>}
                            {state.StructureID && !!state.ContentTypes.length && (
                                <>
                                    <SchemaSectionsSelector
                                        id={state.StructureID}
                                        isContentLike={state.ContentTypes.some(isContentLikeContent)}
                                        selected={state.OverrideSections}
                                        disabled={mode === 'edit'}
                                        onSelect={(selected) => {
                                            if (!state) return
                                            setState({ ...state, OverrideSections: selected })
                                        }}
                                    />
                                </>
                            )}
                        </Grid>

                        <Grid item xs={12}>
                            <FormLabel>Template</FormLabel>
                            <CodeEditor
                                value={state.Template || ''}
                                language={'html'}
                                onChange={(v) => state && setState({ ...state, Template: v || '' })}
                            />
                        </Grid>
                    </Grid>
                </Grid>
                <ConfirmAction
                    open={confirmCloseDialogIsOpen}
                    handleAgree={() => {
                        setConfirmCloseDialogIsOpen(false)
                        onClose()
                    }}
                    handleClose={() => {
                        setConfirmCloseDialogIsOpen(false)
                        onClose()
                    }}
                    handleDisagree={() => {
                        setConfirmCloseDialogIsOpen(false)
                    }}
                    text={'Are you sure you want to exit?'}
                    title={`You have unsaved changes!`}
                />
            </DialogContent>
            <DialogActions sx={{ borderTop: `1px solid ${colours.off_white_but_darker}` }}>
                <Grid item container display='flex' justifyContent='flex-end'>
                    <Button disabled={loading} onClick={onClose} sx={{ marginRight: 2 }}>
                        Cancel
                    </Button>
                    <Button disabled={loading || !hasChanges} variant={'contained'} onClick={handleSave}>
                        {mode === 'edit' ? 'Save changes' : 'Create'}
                    </Button>
                </Grid>
            </DialogActions>
        </CMDialog>
    )
}
