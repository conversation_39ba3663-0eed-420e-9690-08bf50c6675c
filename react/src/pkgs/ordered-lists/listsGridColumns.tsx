import { GridColDef, GridHeader } from '@mui/x-data-grid'
import {
    CellLine,
    CellWrapper,
    IDToNameCell,
    MenuLightCell,
    NullableDateCell,
    PublishPeriodCell,
    TwoLinesCell
} from '@/pkgs/grid/cells/GridCells'
import DistributedListTooltip from './distributed-lists/DistributedListTooltip'

export function getListGridColumns(menuItems: any): GridColDef[] {
    return [
        {
            field: 'Distributed',
            headerName: '',
            headerAlign: 'center',
            maxWidth: 0,
            minWidth: 30,
            width: 0.1,
            cellClassName: 'distributed-cell',
            filterable: true,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>{params.row.Distributed ? <DistributedListTooltip /> : undefined}</CellWrapper>
            )
        },
        {
            field: 'Name',
            headerName: 'Name',
            flex: 1.4,
            filterable: false,
            sortable: true,
            renderCell: (params) => (
                <TwoLinesCell
                    l1={`${params.row.Name}`}
                    // l2={params.row.ID}
                    path={`/ordered-lists/${params.row.ID}/items`}
                />
            )
        },
        {
            field: 'StructureID',
            headerName: 'Structure',
            flex: 1.5,
            filterable: false,
            sortable: false,
            renderCell: (params) => <IDToNameCell tableName={'structure'} ID={params.row.StructureID} />
        },
        {
            field: 'ContentTypes',
            headerName: 'Content Types',
            flex: 1,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.ContentTypes?.join(', ')}</CellLine>
                </CellWrapper>
            )
        },
        {
            field: 'PublishPeriod',
            headerName: 'Status',
            flex: 1.3,
            sortable: false,
            filterable: false,
            renderCell: (params) => (
                <PublishPeriodCell publishAt={params.row.PublishAt} expireAt={params.row.ExpireAt} />
            )
        },
        {
            field: 'publish_at',
            headerName: 'Publish At',
            flex: 1,
            sortable: true,
            filterable: false,
            renderCell: (params) => <NullableDateCell row={params.row} fieldName={'PublishAt'} />
        },
        {
            field: 'expire_at',
            headerName: 'Expire At',
            flex: 1,
            sortable: true,
            filterable: false,
            renderCell: (params) => <NullableDateCell row={params.row} fieldName={'ExpireAt'} />
        },
        {
            field: 'Menu',
            headerName: '...',
            width: 80,
            sortable: false,
            filterable: false,
            renderCell: (params) => <MenuLightCell itemsFactory={menuItems(params.row)} />
        }
    ]
}
