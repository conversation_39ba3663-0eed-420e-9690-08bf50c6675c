import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { useIDToName } from '@/pkgs/grid/cells/GridCells'
import { useAppContext } from '@/pkgs/auth/atoms'

export function SessionError({ error }: { error: any }) {
    const errorMessage = guessErrorMessage(error)
    const editor = error?.response?.data?.ErrorData?.CurrentEditor
    const data = editor
        ? {
              CurrentEditor: editor as string,
              EditingSession: error?.response?.data?.ErrorData?.EditingSession as string,
              ExtendedLock: error?.response?.data?.ErrorData?.ExtendedLock as string,
              Key: error?.response?.data?.ErrorData?.Key as string
          }
        : null
    return <>{editor && data ? <SessionErrorDetails data={data} /> : <span>{errorMessage}</span>}</>
}

function SessionErrorDetails(props: {
    data: { CurrentEditor: string; EditingSession: string; ExtendedLock: string; Key: string }
}) {
    const appContext = useAppContext()
    const name = useIDToName({
        tableName: 'account',
        ID: props.data.CurrentEditor
    })

    const session = props.data.EditingSession ? new Date(props.data.EditingSession) : null
    const extendedLock = props.data.ExtendedLock ? new Date(props.data.ExtendedLock) : null
    const displayName = name || props.data.CurrentEditor

    if (props.data.CurrentEditor === appContext?.identity()?.ID) {
        return (
            <span>
                You are currently editing this item in another tab. Session ends at {session?.toLocaleString()}.
            </span>
        )
    }

    if (extendedLock && extendedLock > new Date()) {
        return (
            <span>
                This item is currently locked by <strong>{displayName}</strong> until {extendedLock.toLocaleString()}.
                You can still view it, but cannot edit it.
            </span>
        )
    }

    if (session && session > new Date()) {
        return (
            <span>
                This item is currently being edited by <strong>{displayName}</strong> until {session.toLocaleString()}.
                You can still view it, but cannot edit it.
            </span>
        )
    }

    return <span>You can't edit this item now. Please, try again later. </span>
}
