import React, { useEffect, useRef } from 'react'
import DialogTitle from '@mui/material/DialogTitle'
import DialogContent from '@mui/material/DialogContent'
import DialogContentText from '@mui/material/DialogContentText'
import DialogActions from '@mui/material/DialogActions'
import Button from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'

type SessionTimeoutProps = {
    isActive: boolean
    onTimeout: () => void
}

export function SessionTimeout({ isActive, onTimeout }: SessionTimeoutProps) {
    const timeoutRef = useRef<ReturnType<typeof setTimeout>>()

    const [open, setOpen] = React.useState(false)

    const reset = () => {
        clearTimeout(timeoutRef.current)
        setOpen(false)
    }

    useEffect(() => {
        clearTimeout(timeoutRef.current)
        setOpen(false)

        if (!isActive) {
            return
        }

        reset()

        return () => {
            clearTimeout(timeoutRef.current)
        }
    }, [isActive])

    return (
        <Dialog
            open={open}
            keepMounted
            aria-labelledby='alert-dialog-slide-title'
            aria-describedby='alert-dialog-slide-description'
        >
            <DialogTitle>Still editing?</DialogTitle>
            <DialogContent>
                <DialogContentText>Your editing session will soon expire, do you want to extend it?</DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button
                    onClick={() => {
                        onTimeout()
                        setOpen(false)
                    }}
                    color='primary'
                >
                    End Session
                </Button>
                <Button color='primary' onClick={() => setOpen(false)} data-testid='dialog-agree'>
                    Extend
                </Button>
            </DialogActions>
        </Dialog>
    )
}
