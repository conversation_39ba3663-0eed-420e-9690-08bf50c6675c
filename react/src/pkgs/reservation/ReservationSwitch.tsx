import { Box, Stack, Switch, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { useAppContext } from '@/pkgs/auth/atoms'
import { ConfirmAction } from '@/common/components'
import { Minute } from '@/pkgs/reservation/time'
import { primaryTheme } from '@/app/theme'
import { useReservationSwitch } from '@/pkgs/reservation/useReservationSwitch'

const EditingSessionDuration = 10 * Minute

interface ReservationSwitchProps {
    ID: string
    Workspace: string
    table: 'content' /* | future support */
    onChange?: (checked: boolean) => void
    disabled?: boolean
    hideExtendedLock?: boolean
}

export function ReservationSwitch({
    ID,
    Workspace,
    table,
    disabled,
    onChange,
    hideExtendedLock = false
}: ReservationSwitchProps) {
    const appContext = useAppContext()

    const { loading, value, start, end } = useReservationSwitch(`${table}::${ID}::${Workspace}`)

    useEffect(() => {
        onChange?.(value ?? false)
    }, [value])

    const [isInactivityWindowOpen, setIsInactivityWindowOpen] = useState(false)

    return (
        <>
            <Box>
                <Stack direction='row' component='label' alignItems='center' justifyContent='center'>
                    <Typography>Read</Typography>
                    <Switch
                        checked={value ?? false}
                        onChange={(e, checked) => (checked ? start() : end())}
                        // `Color` prop doesn't seem to support dynamic changes based on state/re-render.
                        style={{
                            color: value ? primaryTheme.palette.primary.main : primaryTheme.palette.warning.main
                        }}
                        disabled={loading}
                    />
                    <Typography>Edit</Typography>
                </Stack>
            </Box>
            <>
                {/*Inactivity Window*/}
                <ConfirmAction
                    open={isInactivityWindowOpen}
                    handleAgree={() => console.log('OK')}
                    handleClose={() => console.log('Must choose an option')}
                    handleDisagree={() => stop()}
                    text={'Your editing session will soon expire, do you want to extend it?'}
                    title={'Still editing?'}
                    disagreeLabel={'End Session'}
                    agreeLabel={'Extend'}
                />
            </>
        </>
    )
}
