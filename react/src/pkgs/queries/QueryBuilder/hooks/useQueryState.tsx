import { useState, useEffect } from 'react'
import { UpdateQueryDTO, CreateQueryDTO } from '@/pkgs/queries/types'
import { useParams } from 'react-router-dom'
import { useQueriesDetailsQuery } from '@/pkgs/queries/queries'
import { httpPut } from '@/common/client'
import { BASE } from '@/common/constants'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

export function useQueryState() {
    const { id } = useParams()
    const detailsResult = useQueriesDetailsQuery(id || '')

    const [state, setState] = useState<UpdateQueryDTO | CreateQueryDTO | undefined>(undefined)
    const [errors, setErrors] = useState<Partial<Record<keyof UpdateQueryDTO & keyof CreateQueryDTO, string>>>({})
    const [saving, setSaving] = useState(false)

    useEffect(() => {
        if (!detailsResult.data) {
            return
        }

        setState({
            Data: detailsResult.data.Data,
            Title: detailsResult.data.Title,
            Description: detailsResult.data.Description
        })
    }, [detailsResult.data])

    useEffect(() => {
        if (!state) {
            return
        }
        validate()
    }, [state?.Data.Filters])

    const validate = () => {
        if (!state) return false
        const newErrors: Partial<Record<keyof UpdateQueryDTO & keyof CreateQueryDTO, string>> = {}
        if (!state.Title) newErrors.Title = 'Title is required'

        if (state.Data.Filters.length > 0) {
            const hasNoValue = state.Data.Filters.some((f) => f.Value === undefined || f.Value === null)
            if (hasNoValue) newErrors.Data = 'Please fill in all filters'
        }

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }

    const save = async () => {
        if (!validate()) {
            return
        }
        setSaving(true)
        try {
            await httpPut(`${BASE}/api/v1/queries/${id}`, state)
            notify('Successfully updated query', 'info')
            await detailsResult.refetch()
        } catch (error) {
            notify(guessErrorMessage(error), 'error')
        } finally {
            setSaving(false)
        }
    }

    return {
        detailsResult,
        state,
        setState,
        errors,
        save,
        saving
    }
}
