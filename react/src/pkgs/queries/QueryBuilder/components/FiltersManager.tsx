import { Details, Field, isQueryableType, Operation, UpdateQueryDTO } from '@/pkgs/queries/types'
import React from 'react'
import { filterComponentsMapping } from '@/pkgs/queries/filterComponentsMapping'
import { ContentType } from '@/pkgs/content/types'
import { TagType } from '@/pkgs/system/tags/types'
import { FilterComponent } from '@/pkgs/queries/FilterComponent'
import { Button, MenuItem, MenuList } from '@mui/material'
import { AddCircleOutline, Circle } from '@mui/icons-material'
import { getQueryType } from '@/pkgs/queries/QueryBuilder/utils/getQueryType'
import { getFields } from '@/pkgs/queries/QueryBuilder/utils/fieldHelpers'
import { SplitButtonV2 } from '@/pkgs/queries/SplitButtonV2'
import Alert from '@mui/material/Alert'

type FiltersManagerProps = {
    state: UpdateQueryDTO | undefined
    setState: React.Dispatch<React.SetStateAction<UpdateQueryDTO | undefined>>
    query: Details
}

export const FiltersManager = ({ state, setState, query }: FiltersManagerProps) => {
    if (!state || !query) return null

    const queryType = getQueryType(query)
    const availableFields = getFields(queryType, query.Structure)

    const add = (field: Field) => {
        const firstFilterType = Object.keys(filterComponentsMapping[field.type])[0] as Operation
        if (!firstFilterType) {
            return
        }

        if (!isQueryableType(field.type)) {
            console.error(`Field ${field.name} is not queryable`)
            return
        }

        setState({
            ...state,
            Data: {
                ...state.Data,
                Filters: [
                    ...state.Data.Filters,
                    {
                        FieldName: field.name,
                        FieldType: field.type,
                        Operation: firstFilterType,
                        Value: null
                    }
                ]
            }
        })
    }

    const added = (name: string) => state.Data.Filters.some((field) => field.FieldName === name)
    const showWarning = queryType === 'event' && !state.Data.Filters.some((f) => f.FieldName === 'Event')

    return (
        <>
            {showWarning && (
                <Alert severity={'warning'} sx={{ mb: 2 }}>
                    For recurrent events work properly you need to add a time range filter.
                    <Button
                        size={'small'}
                        onClick={() =>
                            setState({
                                ...state,
                                Data: {
                                    ...state.Data,
                                    Filters: [
                                        {
                                            FieldName: 'Event',
                                            FieldType: 'schedule',
                                            Operation: 'timeRange',
                                            Inverted: false,
                                            Value: ['+0d', '+10d']
                                        },
                                        ...state.Data.Filters
                                    ]
                                }
                            })
                        }
                    >
                        Add time range
                    </Button>
                </Alert>
            )}
            {state.Data.Filters.map((data, index) => {
                const filter = availableFields.find((f: Field) => f.name === data.FieldName)
                if (!filter) {
                    return null
                }
                const options = filter?.type === 'select' ? filter.options : undefined
                const config: any = {}
                if (filter?.type === 'tags') {
                    if (query.ContentTypes.some((type) => type === ContentType.Document)) {
                        config.tagTypes = [TagType.Document]
                    } else if (query.ContentTypes.some((type) => type === ContentType.Image)) {
                        config.tagTypes = [TagType.Image]
                    } else {
                        config.tagTypes = []
                        for (const type of query.ContentTypes) {
                            config.tagTypes.push(type as TagType)
                        }
                    }
                }

                return (
                    <FilterComponent
                        key={index}
                        value={data}
                        label={filter.label}
                        onChange={(value) => {
                            const newState = [...state.Data.Filters]

                            if (value === null) {
                                newState.splice(index, 1)
                                setState({
                                    ...state,
                                    Data: { ...state.Data, Filters: newState }
                                })
                                return
                            }

                            newState[index] = value
                            setState({
                                ...state,
                                Data: { ...state.Data, Filters: newState }
                            })
                        }}
                        options={options}
                        config={config}
                    />
                )
            })}

            <SplitButtonV2 label={'Add Filter'} variant={'contained'} icon={<AddCircleOutline />}>
                <MenuList id='split-button-menu' autoFocusItem>
                    {availableFields.map((field, index) => (
                        <MenuItem key={field.name} onClick={() => add(field)}>
                            <Circle color={added(field.name) ? 'warning' : 'success'} sx={{ fontSize: 10 }} />
                            &nbsp;
                            {field.label} {field.name.includes('.') ? `(${field.name})` : ''}
                        </MenuItem>
                    ))}
                </MenuList>
            </SplitButtonV2>
        </>
    )
}
