import { UpdateQueryDTO } from '@/pkgs/queries/types'
import React from 'react'
import { Stack, TextField } from '@mui/material'

type MetaFormProps = {
    state: UpdateQueryDTO | undefined
    setState: React.Dispatch<React.SetStateAction<UpdateQueryDTO | undefined>>
    errors: Partial<Record<keyof UpdateQueryDTO, string>>
}

export const MetaForm = ({ state, setState, errors }: MetaFormProps) => {
    if (!state) return null

    return (
        <Stack spacing={2}>
            <TextField
                label='Title'
                value={state.Title}
                onChange={(e) => setState({ ...state, Title: e.target.value })}
                error={!!errors.Title}
                helperText={errors.Title}
            />
            <TextField
                label='Description'
                multiline
                rows={6}
                value={state.Description}
                onChange={(e) => setState({ ...state, Description: e.target.value })}
            />
        </Stack>
    )
}
