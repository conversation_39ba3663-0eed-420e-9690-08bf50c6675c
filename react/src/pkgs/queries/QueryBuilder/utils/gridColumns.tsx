import { GridColDef } from '@mui/x-data-grid'
import {
    CellLine,
    CellWrapper,
    IDToNameCell,
    PublishPeriodCell,
    SitesCell,
    TrackableCell,
    TwoLinesCell
} from '@/pkgs/grid/cells/GridCells'
import moment from 'moment/moment'

export function getContentGridColumns(): GridColDef[] {
    return [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 1.4,
            filterable: false,
            sortable: false,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'StructureID',
            headerName: 'Structure',
            flex: 1,
            filterable: false,
            sortable: false,
            renderCell: (params) => <IDToNameCell tableName={'structure'} ID={params.row.StructureID} />
        },
        {
            field: 'PublishPeriod',
            headerName: 'Status',
            width: 200,
            sortable: false,
            filterable: false,
            renderCell: (params) => (
                <PublishPeriodCell publishAt={params.row.PublishAt} expireAt={params.row.ExpireAt} />
            )
        },
        {
            field: 'UpdatedAt',
            headerName: 'Updated/by ',
            flex: 1.5,
            sortable: false,
            renderCell: (params) => <TrackableCell trackable={params.row} ownerID={params.row.UpdatedBy} />
        },
        {
            field: 'Sites',
            headerName: 'Sites',
            flex: 1,
            filterable: false,
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params) => <SitesCell sites={params.row?.Sites} />
        },
        {
            field: 'Type',
            headerName: 'Type',
            width: 100,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.Type}</CellLine>
                </CellWrapper>
            )
        }
    ]
}

export function getDocumentGridColumns(): GridColDef[] {
    return [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 1.4,
            filterable: false,
            sortable: false,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'updated_at',
            headerName: 'Updated',
            width: 200,
            filterable: false,
            sortable: false,
            renderCell: (params) => {
                const asStr =
                    params.row.UpdatedAt && params.row.UpdatedAt > params.row.CreatedAt
                        ? params.row.UpdatedAt
                        : params.row.CreatedAt
                const date = moment(asStr).format('YYYY-MM-DD, h:mm:ss a')?.split(',')
                return <TwoLinesCell l1={date[0]} l2={date[1]} />
            }
        },
        {
            field: 'sites',
            headerName: 'Sites',
            flex: 1,
            filterable: false,
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params) => <SitesCell sites={params.row?.Sites} displayCount={2} />
        },
        {
            field: 'type',
            headerName: 'Type',
            width: 100,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.Type}</CellLine>
                </CellWrapper>
            )
        }
    ]
}

export function getImageGridColumns(): GridColDef[] {
    return [
        {
            field: 'Title',
            headerName: 'Title',
            flex: 1.4,
            filterable: false,
            sortable: false,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Title}`} l2={params.row.ID} />
        },
        {
            field: 'updated_at',
            headerName: 'Updated',
            width: 200,
            filterable: false,
            sortable: false,
            renderCell: (params) => {
                const asStr =
                    params.row.UpdatedAt && params.row.UpdatedAt > params.row.CreatedAt
                        ? params.row.UpdatedAt
                        : params.row.CreatedAt
                const date = moment(asStr).format('YYYY-MM-DD, h:mm:ss a')?.split(',')
                return <TwoLinesCell l1={date[0]} l2={date[1]} />
            }
        },
        {
            field: 'sites',
            headerName: 'Sites',
            flex: 1,
            filterable: false,
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params) => <SitesCell sites={params.row?.Sites} displayCount={2} />
        },
        {
            field: 'type',
            headerName: 'Type',
            width: 100,
            filterable: false,
            sortable: false,
            renderCell: (params) => (
                <CellWrapper>
                    <CellLine>{params.row.Type}</CellLine>
                </CellWrapper>
            )
        }
    ]
}
