import React, { useState, useEffect } from 'react'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON>,
    CardContent,
    FormControl,
    FormHelperText,
    IconButton,
    InputLabel,
    MenuItem,
    Select,
    Stack,
    TextField,
    Typography
} from '@mui/material'
import ClearIcon from '@mui/icons-material/Clear'
import { truncateMiddle } from '@/helpers/truncateMiddle'
import { useQueriesDetailsQuery } from './queries'
import { useQueryTemplates } from './templates/queries'
import QuerySelectionDialog from './QuerySelectionDialog'
import { Query } from './types'

export interface QueryValue {
    queryID: string
    templateID?: string
    pageSize?: number
}

interface QuerySelectorProps {
    value: QueryValue | null
    onChange: (value: QueryValue | null) => void
    required?: boolean
    disabled?: boolean
    error?: boolean
    onErrorChange?: (hasError: boolean) => void
    label?: string
    layout?: 'vertical' | 'card'
    useClearIcon?: boolean
}

export default function QuerySelector({
    value,
    onChange,
    required = false,
    disabled = false,
    error = false,
    onErrorChange,
    label = 'Query',
    layout = 'vertical',
    useClearIcon = false
}: QuerySelectorProps) {
    const [localValue, setLocalValue] = useState<QueryValue | null>(value)
    const [queryDialogOpen, setQueryDialogOpen] = useState(false)
    const [queryTitle, setQueryTitle] = useState<string>('')

    const { data: queryDetails } = useQueriesDetailsQuery(localValue?.queryID || '')

    const { data: templatesData, isLoading: templatesLoading } = useQueryTemplates({
        queryID: localValue?.queryID || '',
        q: { pageSize: 100 }
    })

    useEffect(() => {
        setLocalValue(value)
    }, [value])

    // Update query title when details are fetched (for the saved previous value)
    useEffect(() => {
        if (queryDetails) {
            setQueryTitle(queryDetails.Title)
        }
    }, [queryDetails])

    const handleQuerySelect = (query: Query | null) => {
        if (!query) {
            handleClearSelection()
            return
        }

        const newValue: QueryValue = {
            queryID: query.ID,
            templateID: undefined,
            pageSize: localValue?.pageSize || 0
        }

        setLocalValue(newValue)
        setQueryTitle(query.Title)
        onChange(newValue)

        if (onErrorChange && required) {
            onErrorChange(false)
        }
    }

    const handleTemplateChange = (templateId: string) => {
        if (!localValue) return

        const newValue: QueryValue = {
            ...localValue,
            templateID: templateId || undefined
        }

        setLocalValue(newValue)
        onChange(newValue)
    }

    const handlePageSizeChange = (pageSize: number) => {
        if (!localValue) return

        const newValue: QueryValue = {
            ...localValue,
            pageSize
        }

        setLocalValue(newValue)
        onChange(newValue)
    }

    const handleClearSelection = (e?: React.MouseEvent<HTMLButtonElement>) => {
        if (e) {
            e.stopPropagation()
        }

        setLocalValue(null)
        setQueryTitle('')
        onChange(null)

        if (onErrorChange && required) {
            onErrorChange(true)
        }
    }

    const isQuerySelected = !!localValue?.queryID

    // Query selector element (reused in all layouts)
    const querySelector = (
        <FormControl fullWidth required={required} error={error}>
            <div
                title={queryTitle || localValue?.queryID || ''}
                style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginTop: '16px',
                    padding: '8px',
                    border: '1px solid rgba(0, 0, 0, 0.23)',
                    borderRadius: '4px',
                    cursor: disabled ? 'default' : 'pointer',
                    minHeight: '40px'
                }}
                onClick={() => !disabled && setQueryDialogOpen(true)}
            >
                {isQuerySelected ? (
                    <Typography>{truncateMiddle(queryTitle || localValue?.queryID || '', 30)}</Typography>
                ) : (
                    <Typography color='text.secondary'>
                        Select a {label.toLowerCase()} {required ? '(required)' : '(optional)'}
                    </Typography>
                )}

                {!isQuerySelected && (
                    <Button size='small' variant='outlined'>
                        Browse...
                    </Button>
                )}

                {isQuerySelected && useClearIcon && (
                    <IconButton onClick={handleClearSelection} disabled={disabled}>
                        <ClearIcon />
                    </IconButton>
                )}
            </div>
        </FormControl>
    )

    // Template and page size controls that appear when a query is selected
    const templateControl = (
        <FormControl variant='standard' fullWidth disabled={disabled || !isQuerySelected || templatesLoading}>
            <InputLabel>Template (Optional)</InputLabel>
            <Select
                value={localValue?.templateID || ''}
                onChange={(e) => handleTemplateChange(e.target.value)}
                renderValue={(value: string) => {
                    if (!value) {
                        return <em>None</em>
                    }
                    const template = templatesData?.Rows.find((t) => t.ID === value)
                    if (!template) return value
                    return `${template.Title} [${template.Type}]`
                }}
            >
                <MenuItem value=''>
                    <em>None</em>
                </MenuItem>
                {templatesData?.Rows.map((template) => (
                    <MenuItem key={template.ID} value={template.ID}>
                        {template.Title}{' '}
                        <Typography variant='caption' color='textSecondary' component='span'>
                            [{template.Type}]
                        </Typography>
                    </MenuItem>
                ))}
            </Select>
            {templatesLoading && <FormHelperText>Loading templates...</FormHelperText>}
        </FormControl>
    )

    const pageSizeControl = (
        <FormControl variant='standard' fullWidth disabled={disabled || !isQuerySelected}>
            <TextField
                label='Page Size (0 = use template default)'
                type='number'
                variant='standard'
                value={localValue?.pageSize ?? 0}
                onChange={(e) => handlePageSizeChange(parseInt(e.target.value, 10) || 0)}
                disabled={disabled || !isQuerySelected}
                inputProps={{ min: 0 }}
            />
        </FormControl>
    )

    // Dialog component (used in all layouts)
    const dialogComponent = (
        <QuerySelectionDialog
            open={queryDialogOpen}
            onClose={() => setQueryDialogOpen(false)}
            onSelect={handleQuerySelect}
            selectedQueryId={localValue?.queryID}
            title={`Select ${label}`}
        />
    )

    const errorMessage = error && required && <Alert severity='error'>This field is required</Alert>

    // Card layout (for DCT forms)
    if (layout === 'card') {
        return (
            <>
                <Card sx={{ p: 1, mt: 2 }}>
                    <CardContent>
                        <Typography variant='subtitle2'>{label}</Typography>
                        <Stack
                            spacing={2}
                            direction={isQuerySelected ? 'row' : 'column'}
                            alignItems={'center'}
                            justifyItems={'center'}
                            sx={{ width: '100%' }}
                        >
                            {querySelector}

                            {isQuerySelected && templatesData && (
                                <>
                                    {templateControl}
                                    {pageSizeControl}
                                </>
                            )}
                        </Stack>
                        {errorMessage}
                    </CardContent>
                </Card>
                {dialogComponent}
            </>
        )
    }

    // Default vertical layout (for lexical)
    return (
        <>
            <Stack spacing={2} sx={{ mt: 2, width: '100%' }}>
                {querySelector}
                {isQuerySelected && templatesData && (
                    <>
                        {templateControl}
                        {pageSizeControl}
                    </>
                )}
                {errorMessage}
            </Stack>
            {dialogComponent}
        </>
    )
}
