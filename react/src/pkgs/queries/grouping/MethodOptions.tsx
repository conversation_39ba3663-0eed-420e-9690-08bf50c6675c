import { SortingMethod } from '@/pkgs/queries/types'
import React, { useEffect } from 'react'
import { getDefaultOption } from '@/pkgs/queries/grouping/utils'
import { FormControl, InputLabel, MenuItem, Select, TextField } from '@mui/material'

export const MethodOptions = ({
    method,
    value,
    onChange
}: {
    method: SortingMethod
    value: any
    onChange: (value: any) => void
}) => {
    // Handle initial value setup
    useEffect(() => {
        if (value === undefined || value === null || (typeof value === 'object' && Object.keys(value).length === 0)) {
            onChange(getDefaultOption(method))
        }
    }, [method, value, onChange])

    // Make sure we have a valid value for the current method
    const safeValue =
        value === undefined || value === null || (typeof value === 'object' && Object.keys(value).length === 0)
            ? getDefaultOption(method)
            : value

    switch (method) {
        case 'value':
            return (
                <TextField
                    label='Default value'
                    value={safeValue}
                    onChange={(e) => onChange(e.target.value)}
                    size='small'
                    placeholder='Value for null'
                    fullWidth
                />
            )
        case 'firstN':
            return (
                <FormControl fullWidth size='small'>
                    <InputLabel>First N characters</InputLabel>
                    <Select
                        value={String(safeValue)} // Convert to string to avoid type errors
                        onChange={(e) => onChange(Number(e.target.value))}
                        label='First N characters'
                    >
                        {[1, 2, 3, 4, 5].map((num) => (
                            <MenuItem key={num} value={String(num)}>
                                {num}
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            )
        case 'date':
            return (
                <FormControl fullWidth size='small'>
                    <InputLabel>Date format</InputLabel>
                    <Select
                        value={String(safeValue)} // Convert to string to avoid type errors
                        onChange={(e) => onChange(e.target.value)}
                        label='Date format'
                    >
                        <MenuItem value='YYYY'>YYYY</MenuItem>
                        <MenuItem value='YYYY, MMM'>YYYY, MMM</MenuItem>
                        <MenuItem value='MMM'>MMM</MenuItem>
                    </Select>
                </FormControl>
            )
        default:
            return null
    }
}
