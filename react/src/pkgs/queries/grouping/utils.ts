import { SortingMethod } from '@/pkgs/queries/types'

export const getAvailableMethods = (fieldType: string): SortingMethod[] => {
    // Default method for all types
    const methods: SortingMethod[] = ['value']

    if (fieldType === 'text' || fieldType === 'email') {
        methods.push('firstN')
    }

    if (fieldType === 'date') {
        methods.push('date')
    }

    return methods
}
// Get default options based on method
export const getDefaultOption = (method: SortingMethod): any => {
    switch (method) {
        case 'value':
            return 'No value'
        case 'firstN':
            return 1
        case 'date':
            return 'YYYY'
        default:
            return null
    }
}
