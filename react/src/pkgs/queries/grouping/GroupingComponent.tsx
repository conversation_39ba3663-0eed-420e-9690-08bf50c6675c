import { Field, Grouping } from '@/pkgs/queries/types'
import React from 'react'
import { MenuItem, MenuList, Stack } from '@mui/material'
import IconButton from '@mui/material/IconButton'
import DeleteIcon from '@mui/icons-material/Delete'
import { AddCircleOutline } from '@mui/icons-material'
import { GroupingSelector } from '@/pkgs/queries/grouping/GroupingSelector'
import { getAvailableMethods, getDefaultOption } from '@/pkgs/queries/grouping/utils'
import { SplitButtonV2 } from '@/pkgs/queries/SplitButtonV2'

type GroupingComponentProps = {
    value: Grouping[]
    onChange: (value: Grouping[]) => void
    options: Field[]
}
export const GroupingComponent = ({ value, onChange, options }: GroupingComponentProps) => {
    const add = (field: Field) => {
        if (!field) {
            return
        }
        // Get default method for this field type
        const methods = getAvailableMethods(field.type)
        const defaultMethod = methods[0]

        const newGrouping: Grouping = {
            Field: field.name,
            Direction: 'asc',
            Method: defaultMethod,
            Options: getDefaultOption(defaultMethod) // Initialize with proper default value
        }
        onChange([...value, newGrouping])
    }

    const handleRemoveGrouping = (index: number) => {
        const newValue = [...value]
        newValue.splice(index, 1)
        onChange(newValue)
    }

    return (
        <div>
            {value.map((grouping, index) => (
                <Stack spacing={2} direction='row' alignItems='center' key={index} sx={{ mb: 2 }}>
                    <GroupingSelector
                        value={grouping}
                        options={options}
                        onChange={(newGrouping) => {
                            const newValue = [...value]
                            newValue[index] = newGrouping
                            onChange(newValue)
                        }}
                    />
                    <IconButton size='small' onClick={() => handleRemoveGrouping(index)}>
                        <DeleteIcon color={'error'} />
                    </IconButton>
                </Stack>
            ))}

            {value.length < 2 && (
                <SplitButtonV2 label={'Add Grouping'} icon={<AddCircleOutline />} variant='contained' size={'small'}>
                    <MenuList id='split-button-menu' autoFocusItem>
                        {options.map((field, index) => (
                            <MenuItem key={field.name} onClick={() => add(field)}>
                                {field.label}
                            </MenuItem>
                        ))}
                    </MenuList>
                </SplitButtonV2>
            )}
        </div>
    )
}
