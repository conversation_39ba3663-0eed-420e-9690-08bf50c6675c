import React, { useState, useEffect } from 'react'
import { Field, FieldType } from '../types'
import { CellConfig, DateCellConfig, ImageCellConfig } from './types'
import { Stack, Checkbox, FormControlLabel, TextField, Select, MenuItem, FormControl, InputLabel } from '@mui/material'

type ConfigComponentProps = {
    config: CellConfig
    onChange: (config: CellConfig) => void
    field: Field
}

const LinkedCheckbox = ({ config, field, onChange }: ConfigComponentProps) => (
    <FormControlLabel
        control={
            <Checkbox
                checked={config?.linked || false}
                onChange={(e) => onChange({ ...config, linked: e.target.checked })}
                size='small'
            />
        }
        label='Linked'
    />
)

const DateConfig = ({ config, field, onChange }: ConfigComponentProps) => {
    const dateConfig = config as DateCellConfig
    const [format, setFormat] = useState(dateConfig?.format || 'Jan 2, 2006 3:04 PM')

    useEffect(() => {
        setFormat(dateConfig?.format || 'Jan 2, 2006 3:04 PM')
    }, [dateConfig?.format])

    const dateFormats = [
        { value: 'Jan 2, 2006 3:04 PM' },
        { value: '2006-01-02 15:04:05' },
        { value: 'Jan 2, 2006' },
        { value: '2006-01-02' },
        { value: '01/02/2006' },
        { value: '02/01/2006' },
        { value: 'January 2, 2006' },
        { value: '3:04 PM' },
        { value: '15:04' },
        { value: '15:04:05' },
        { value: 'Mon Jan 2 15:04:05 2006' },
        { value: 'Monday, January 2, 2006' },
        { value: '2006-01-02T15:04:05Z07:00' }
    ]

    return (
        <Stack spacing={1}>
            <LinkedCheckbox config={config} onChange={onChange} field={field} />
            <FormControl size='small'>
                <InputLabel>Date Format</InputLabel>
                <Select
                    value={format}
                    label='Date Format'
                    onChange={(e) => {
                        const newFormat = e.target.value
                        setFormat(newFormat)
                        onChange({ ...dateConfig, format: newFormat })
                    }}
                >
                    {dateFormats.map((format) => (
                        <MenuItem key={format.value} value={format.value}>
                            {format.value}
                        </MenuItem>
                    ))}
                </Select>
            </FormControl>
        </Stack>
    )
}

const ImageConfig = ({ config, field, onChange }: ConfigComponentProps) => {
    const imageConfig = config as ImageCellConfig
    const [dimensions, setDimensions] = useState({
        width: imageConfig?.width || '',
        height: imageConfig?.height || '',
        crop: imageConfig?.crop || ''
    })

    useEffect(() => {
        setDimensions({
            width: imageConfig?.width || '',
            height: imageConfig?.height || '',
            crop: imageConfig?.crop || ''
        })
    }, [imageConfig?.width, imageConfig?.height, imageConfig?.crop])

    const handleBlur = () => {
        onChange({
            ...imageConfig,
            width: dimensions.width ? parseInt(String(dimensions.width)) : undefined,
            height: dimensions.height ? parseInt(String(dimensions.height)) : undefined,
            crop: dimensions.crop as '' | '1' | '2' | '3' | '4' | '5'
        })
    }

    return (
        <Stack spacing={1}>
            <LinkedCheckbox config={config} onChange={onChange} field={field} />
            <Stack direction='row' spacing={1}>
                <TextField
                    size='small'
                    type='number'
                    label='Width'
                    value={dimensions.width}
                    onChange={(e) => setDimensions((prev) => ({ ...prev, width: e.target.value }))}
                    onBlur={handleBlur}
                    inputProps={{
                        min: 0,
                        max: 1000
                    }}
                />
                <TextField
                    size='small'
                    type='number'
                    label='Height'
                    value={dimensions.height}
                    onChange={(e) => setDimensions((prev) => ({ ...prev, height: e.target.value }))}
                    onBlur={handleBlur}
                    inputProps={{
                        min: 0,
                        max: 1000
                    }}
                />
                <FormControl size='small' sx={{ minWidth: '120px' }}>
                    <InputLabel>Crop</InputLabel>
                    <Select
                        size='small'
                        value={dimensions.crop}
                        onChange={(e) => setDimensions((prev) => ({ ...prev, crop: e.target.value }))}
                        onBlur={handleBlur}
                        label='Crop'
                    >
                        {['', '1', '2', '3', '4', '5'].map((value) => (
                            <MenuItem key={value} value={value}>
                                {value}&nbsp;
                            </MenuItem>
                        ))}
                    </Select>
                </FormControl>
            </Stack>
        </Stack>
    )
}

const DefaultConfig = ({ config, field, onChange }: ConfigComponentProps) => (
    <LinkedCheckbox config={config} onChange={onChange} field={field} />
)

export const cellConfigComponentsMapping: Record<FieldType, React.ComponentType<ConfigComponentProps>> = {
    date: DateConfig,
    image: ImageConfig,
    checkbox: DefaultConfig,
    select: DefaultConfig,
    text: DefaultConfig,
    email: DefaultConfig,
    privacy: DefaultConfig,
    tags: DefaultConfig,
    sites: DefaultConfig,
    array: DefaultConfig,
    document: DefaultConfig,
    'contact-form-link': DefaultConfig,
    'rich-text': DefaultConfig,
    textarea: DefaultConfig,
    schedule: DefaultConfig,
    folders: DefaultConfig
}
