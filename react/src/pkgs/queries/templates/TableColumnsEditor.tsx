import { TableCell } from './types'
import { Field } from '@/pkgs/queries/types'
import { Sortables } from '@/pkgs/ordered-lists/Sortables'
import { SplitButtonV2 } from '@/pkgs/queries/SplitButtonV2'
import { MenuItem, MenuList, Stack, TextField, Collapse, IconButton } from '@mui/material'
import React, { useEffect, useState } from 'react'
import { AddCircleOutline, ExpandMore, ExpandLess } from '@mui/icons-material'
import DeleteIcon from '@mui/icons-material/Delete'
import { cellConfigComponentsMapping } from './cellConfigMapping'

type TableColumnsEditorProps = {
    value: TableCell[]
    options: Field[]
    onChange: (columns: TableCell[]) => void
}

export const TableColumnsEditor = ({ value, options, onChange }: TableColumnsEditorProps) => {
    const [expandedColumns, setExpandedColumns] = useState<Record<string, boolean>>({})
    const add = (field: Field) => {
        const newColumn: TableCell = {
            Label: field.label,
            Name: field.name,
            Config: { linked: false }
        }
        onChange([...value, newColumn])
    }

    const renderValue = (column: TableCell) => {
        let haseError = false
        let field = options.find((f) => f.name === column.Name)
        if (!field) {
            haseError = true
            field = { name: column.Name, label: column.Name, type: 'text' }
        }

        return (
            <Stack direction='row' spacing={1} alignItems='flex-start' sx={{ flex: 1 }}>
                <EditableColumn
                    disabled={haseError}
                    column={column}
                    field={field}
                    expanded={expandedColumns[column.Name] || false}
                    onExpandToggle={(expanded) => {
                        setExpandedColumns((prev) => ({
                            ...prev,
                            [column.Name]: expanded
                        }))
                    }}
                    onCommit={(newLabel, newConfig) =>
                        onChange(
                            value.map((c) =>
                                c.Name === column.Name
                                    ? {
                                          ...c,
                                          Label: newLabel,
                                          Config: newConfig || c.Config
                                      }
                                    : c
                            )
                        )
                    }
                />
                <IconButton onClick={() => onChange(value.filter((c) => c.Name !== column.Name))} size='small'>
                    <DeleteIcon />
                </IconButton>
            </Stack>
        )
    }

    return (
        <div>
            <Stack direction='row' justifyContent={'end'}>
                <SplitButtonV2 label={'Add Column'} icon={<AddCircleOutline />} size='small' variant='contained'>
                    <MenuList id='split-button-menu' autoFocusItem>
                        {options
                            .filter((f) => !value.some((v) => v.Name === f.name))
                            .map((field, index) => (
                                <MenuItem key={field.name} onClick={() => add(field)}>
                                    {field.label} {field.name.includes('.') && `(${field.name})`}
                                </MenuItem>
                            ))}
                    </MenuList>
                </SplitButtonV2>
            </Stack>

            <Sortables value={value} onChange={onChange} renderValue={renderValue} />
        </div>
    )
}

type EditableColumnProps = {
    column: TableCell
    field: Field
    expanded: boolean
    onExpandToggle: (expanded: boolean) => void
    onCommit: (newLabel: string, newConfig?: TableCell['Config']) => void
    disabled?: boolean
}

const EditableColumn = ({
    column,
    field,
    expanded,
    onExpandToggle,
    onCommit,
    disabled = false
}: EditableColumnProps) => {
    const [localLabel, setLocalLabel] = useState(column.Label)

    useEffect(() => {
        setLocalLabel(column.Label)
    }, [column.Label])

    const ConfigComponent = cellConfigComponentsMapping[field.type] || cellConfigComponentsMapping.text

    return (
        <Stack spacing={1} sx={{ flex: 1 }}>
            <Stack direction='row' spacing={1} alignItems='center'>
                <IconButton size='small' onClick={() => onExpandToggle(!expanded)}>
                    {expanded ? <ExpandLess /> : <ExpandMore />}
                </IconButton>
                <TextField
                    disabled={disabled}
                    size='small'
                    fullWidth
                    title={disabled ? `The field is not available anymore, please remove it` : localLabel}
                    label={`Column name for: ${column.Name}`}
                    value={disabled ? `Error: ${localLabel}` : localLabel}
                    onChange={(e) => setLocalLabel(e.target.value)}
                    onBlur={() => onCommit(localLabel)}
                />
            </Stack>
            <Collapse in={expanded}>
                <ConfigComponent
                    config={column.Config}
                    onChange={(newConfig) => {
                        onCommit(localLabel, newConfig)
                    }}
                    field={field}
                />
            </Collapse>
        </Stack>
    )
}
