import { FilterComponentType } from '@/pkgs/queries/types'
import { Checkbox, FormControlLabel, FormGroup } from '@mui/material'
import { useEffect } from 'react'

export const FilterYesNo: FilterComponentType<boolean> = (props) => {
    useEffect(() => {
        if (props.value === undefined || props.value === null) {
            props.onChange(false)
        }
    }, [props.value])

    return (
        <FormGroup>
            <FormControlLabel
                control={<Checkbox checked={props.value || false} onChange={(_, checked) => props.onChange(checked)} />}
                label={props.value ? 'Yes' : 'No'}
            />
        </FormGroup>
    )
}
