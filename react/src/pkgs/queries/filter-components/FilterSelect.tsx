import { Box, Chip, FormControl, MenuItem, Select, SelectChangeEvent } from '@mui/material'
import { FilterComponentType } from '@/pkgs/queries/types'
import { useEffect } from 'react'

export const FilterSelect: FilterComponentType<string[]> = ({ value, onChange, options = [], config = {} }) => {
    useEffect(() => {
        if (value === undefined) {
            onChange([])
        }
    }, [value])

    const handleChange = (event: SelectChangeEvent<string[]>) => {
        const newValue = event.target.value
        onChange(typeof newValue === 'string' ? newValue.split(',') : newValue)
    }

    return (
        <FormControl fullWidth>
            <Select
                size='small'
                labelId='multi-select-label'
                id='multi-select'
                multiple
                value={value || []}
                onChange={handleChange}
                renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                        {selected.map((value) => {
                            const option = options.find((opt) => opt.value === value)
                            return <Chip key={value} label={option?.label || value} size='small' />
                        })}
                    </Box>
                )}
            >
                {options.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                        {option.label || option.value}
                    </MenuItem>
                ))}
            </Select>
        </FormControl>
    )
}
