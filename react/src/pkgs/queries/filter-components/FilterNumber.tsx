import { FilterComponentType } from '@/pkgs/queries/types'
import { TextField } from '@mui/material'
import { useEffect } from 'react'

export const FilterNumber: FilterComponentType<number> = (props) => {
    useEffect(() => {
        if (!props.value && props.value !== 0) {
            props.onChange(0)
        }
    }, [props])

    return (
        <TextField
            size={'small'}
            value={props.value || 0}
            onChange={(e) => props.onChange(Number(e.target.value))}
            type='number'
        />
    )
}
