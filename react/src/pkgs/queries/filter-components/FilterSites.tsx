import { FilterComponentType } from '@/pkgs/queries/types'
import { Sharable } from '@/pkgs/content/types'
import { useAppContext } from '@/pkgs/auth/atoms'
import { Site } from '@/pkgs/auth/types'
import DialogContent from '@mui/material/DialogContent'
import {
    Autocomplete,
    Box,
    Button,
    Card,
    CardActionArea,
    CardContent,
    Checkbox,
    DialogActions,
    FormControlLabel,
    FormGroup,
    Stack,
    Switch,
    TextField,
    Typography
} from '@mui/material'
import CMDialog from '@/common/components/CMDialog'
import React, { useEffect, useState, useMemo, useCallback } from 'react'
import { matchSorter } from 'match-sorter'
import { TagType } from '@/pkgs/system/tags/types'
import { useTagsQueryV2 } from '@/pkgs/system/tags/queries'

export const FilterSites: FilterComponentType<Sharable> = ({ value, onChange, config = {} }) => {
    useEffect(() => {
        if (!value) {
            onChange({ Sites: [], DepartmentID: null })
        }
    }, [value])

    const appContext = useAppContext()
    const sites = appContext.getTenantSites()

    const [open, setOpen] = useState(false)

    if (!sites) {
        return null
    }

    if (!value) {
        return null
    }

    const selectedSites = value.Sites.map((site) => sites.find((s) => s.ID === site)).filter(Boolean)
    const selectedSitesString =
        selectedSites?.length > 0
            ? selectedSites.map((site) => site!.Name).join(' • ')
            : value.DepartmentID
              ? 'All sites within department'
              : 'Current Site'
    const selectedDepartment = sites.find((site) => site.ID === value.DepartmentID)

    return (
        <>
            <Card sx={{ mb: 2 }}>
                <CardActionArea onClick={() => setOpen(true)}>
                    <CardContent sx={{ p: 1.3 }}>
                        {selectedDepartment?.Name && <strong>Department: {selectedDepartment.Name}&nbsp;</strong>}
                        {selectedSitesString}
                    </CardContent>
                </CardActionArea>
            </Card>
            {open && (
                <CMDialog
                    open={open}
                    showCloseButton
                    onClose={() => setOpen(false)}
                    title='Select Sites'
                    fullWidth
                    maxWidth={'md'}
                >
                    <DialogContent>
                        <SiteSelector value={value} onChange={onChange} sites={sites} />
                    </DialogContent>
                    <DialogActions>
                        <Button onClick={() => setOpen(false)}>Close</Button>
                    </DialogActions>
                </CMDialog>
            )}
        </>
    )
}

type SiteSelectorProps = {
    value: Sharable
    onChange: (value: Sharable) => void
    sites: Site[]
}

const SiteSelector = ({ value, onChange, sites }: SiteSelectorProps) => {
    const departmentSites = useMemo(
        () => sites.filter((site) => site.Type === 'department').sort((a, b) => a.Name.localeCompare(b.Name)),
        [sites]
    )

    const department = useMemo(
        () => departmentSites.find((site) => site.ID === value.DepartmentID),
        [departmentSites, value.DepartmentID]
    )

    const availableSites = useMemo(() => {
        if (department) {
            return sites
                .filter((site) => site.Type !== 'department' && department.Hosts.some((id) => id === site.ID))
                .sort((a, b) => a.Name.localeCompare(b.Name))
        } else {
            return sites.filter((site) => site.Type !== 'department').sort((a, b) => a.Name.localeCompare(b.Name))
        }
    }, [sites, department])

    const [filter, setFilter] = useState({
        search: '',
        selectedOnly: false
    })

    const filteredSites = useMemo(() => {
        let filtered = availableSites

        if (filter.selectedOnly) {
            filtered = filtered.filter((site) => value.Sites.includes(site.ID))
        }

        if (filter.search) {
            filtered = matchSorter(filtered, filter.search, {
                keys: ['Name', 'ID', 'Tags', 'Type', 'TenantID', 'PrimaryDomain'],
                threshold: matchSorter.rankings.CONTAINS
            })
        }

        return filtered
    }, [availableSites, filter.search, filter.selectedOnly, value.Sites])

    return (
        <>
            <DepartmentSelector
                value={value.DepartmentID || null}
                onChange={(departmentId) => {
                    onChange({ DepartmentID: departmentId, Sites: [] })
                }}
                departments={departmentSites}
            />

            <TextField
                fullWidth
                size='small'
                value={filter.search}
                label='Filter'
                variant='outlined'
                margin='dense'
                onChange={(e) => setFilter((prev) => ({ ...prev, search: e.target.value }))}
                InputProps={{
                    autoComplete: 'off', // Disable browser autocomplete
                    spellCheck: false // Disable spellcheck for better performance
                }}
            />
            <FormControlLabel
                control={
                    <Switch
                        size='small'
                        checked={filter.selectedOnly}
                        onChange={(e) => setFilter({ ...filter, selectedOnly: e.target.checked })}
                    />
                }
                label={`Selected only (${value.Sites.length})`}
            />

            <Stack direction={'row'} spacing={1}>
                <Box sx={{ mt: 1, p: 1, width: 200, overflow: 'auto' }}>
                    <TagsSelector
                        sites={availableSites}
                        selectedSiteIDs={value.Sites}
                        onChange={(command, siteIDs) => {
                            if (command === 'select') {
                                const newSites = [...new Set([...value.Sites, ...siteIDs])]
                                onChange({ ...value, Sites: newSites })
                            } else {
                                const newSites = value.Sites.filter((s) => !siteIDs.includes(s))
                                onChange({ ...value, Sites: newSites })
                            }
                        }}
                    />
                </Box>
                <Card sx={{ mt: 1, p: 1, height: '60vh', overflow: 'auto', flex: 1 }}>
                    <FormGroup>
                        {filteredSites.map((site) => (
                            <FormControlLabel
                                key={site.ID}
                                control={
                                    <Checkbox
                                        checked={value.Sites.includes(site.ID)}
                                        onChange={(e) => {
                                            const newSites = e.target.checked
                                                ? [...value.Sites, site.ID]
                                                : value.Sites.filter((s) => s !== site.ID)
                                            onChange({ ...value, Sites: newSites })
                                        }}
                                    />
                                }
                                label={site.Name}
                            />
                        ))}
                    </FormGroup>
                </Card>
            </Stack>
        </>
    )
}

type DepartmentSelectorProps = {
    value: string | null
    onChange: (value: string | null) => void
    departments: Site[]
}

const DepartmentSelector = ({ value, onChange, departments }: DepartmentSelectorProps) => {
    const selectedDepartment = departments.find((dept) => dept.ID === value) || null

    return (
        <Autocomplete
            value={selectedDepartment}
            onChange={(_, newValue) => {
                onChange(newValue ? newValue.ID : null)
            }}
            options={departments}
            getOptionLabel={(option) => option.Name}
            renderInput={(params) => (
                <TextField {...params} label='Department' size='small' fullWidth margin='normal' />
            )}
            isOptionEqualToValue={(option, value) => option.ID === value.ID}
            clearOnBlur={false}
            clearOnEscape
        />
    )
}

type TagsSelectorProps = {
    sites: Site[]
    selectedSiteIDs: string[]
    onChange?: (command: 'select' | 'unselect', siteIDs: string[]) => void
    disabled?: boolean
}

const TagsSelector = ({ onChange, selectedSiteIDs, sites, disabled }: TagsSelectorProps) => {
    const [tagsById, setTagsById] = useState<Record<string, string>>({})
    const tagsResult = useTagsQueryV2({
        Search: '',
        Types: [TagType.Site],
        MatchAnyType: true,
        pageSize: 1000
    })

    // Fetch tags from the server
    useEffect(() => {
        if (!tagsResult.data) return

        const tags = tagsResult.data.Rows || []
        const tagsById = tags.reduce(
            (acc, tag) => {
                acc[tag.ID] = tag.Name
                return acc
            },
            {} as Record<string, string>
        )

        setTagsById(tagsById)
    }, [tagsResult.data])

    const selectedSiteIDsSet = useMemo(() => new Set(selectedSiteIDs), [selectedSiteIDs])

    // Extract all unique tags from sites
    const tagsMap = useMemo(() => {
        const map = new Map<string, { name: string; sites: Site[]; siteIds: Set<string> }>()

        map.set('all', {
            name: 'Select All',
            sites: sites,
            siteIds: new Set(sites.map((site) => site.ID))
        })

        sites.forEach((site) => {
            site.Tags.forEach((tagId) => {
                const tagName = tagsById[tagId] || tagId

                if (!map.has(tagId)) {
                    const siteIds = new Set<string>([site.ID])
                    map.set(tagId, { name: tagName, sites: [site], siteIds })
                } else {
                    const entry = map.get(tagId)!
                    if (!entry.siteIds.has(site.ID)) {
                        entry.sites.push(site)
                        entry.siteIds.add(site.ID)
                    }
                }
            })
        })

        return map
    }, [sites, tagsById])

    const tags = useMemo(() => {
        return Array.from(tagsMap.entries())
            .map(([id, { name, sites, siteIds }]) => ({
                id,
                name,
                sites,
                siteIds: Array.from(siteIds) // Convert Set back to array for component use
            }))
            .sort((a, b) => {
                // Ensure "Select All" is always first
                if (a.id === 'all') return -1
                if (b.id === 'all') return 1
                return a.name.localeCompare(b.name)
            })
    }, [tagsMap])

    // Memoized function to check tag selection state
    const getTagSelectionState = useCallback(
        (tagSiteIds: string[]) => {
            if (tagSiteIds.length === 0) return 'none'

            // Use Set operations for faster checks
            let selectedCount = 0
            for (const id of tagSiteIds) {
                if (selectedSiteIDsSet.has(id)) {
                    selectedCount++
                }
            }

            if (selectedCount === 0) return 'none'
            if (selectedCount === tagSiteIds.length) return 'all'
            return 'some'
        },
        [selectedSiteIDsSet]
    )

    const handleTagChange = useCallback(
        (tagId: string, tagSiteIds: string[]) => (e: React.ChangeEvent<HTMLInputElement>) => {
            if (onChange) {
                onChange(e.target.checked ? 'select' : 'unselect', tagSiteIds)
            }
        },
        [onChange]
    )

    const tagsList = useMemo(() => {
        return tags.map((tag) => {
            const tagSiteIds = tag.siteIds
            const selectionState = getTagSelectionState(tagSiteIds)

            return (
                <FormControlLabel
                    key={tag.id}
                    label={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant='body2'>{tag.name}</Typography>
                            {selectionState === 'some' && (
                                <Box
                                    component='span'
                                    sx={{
                                        display: 'inline-block',
                                        width: 8,
                                        height: 8,
                                        borderRadius: '50%',
                                        bgcolor: 'primary.main',
                                        ml: 1
                                    }}
                                />
                            )}
                        </Box>
                    }
                    control={
                        <Checkbox
                            size='small'
                            disabled={Boolean(disabled)}
                            checked={selectionState === 'all'}
                            indeterminate={selectionState === 'some'}
                            onChange={handleTagChange(tag.id, tagSiteIds)}
                        />
                    }
                />
            )
        })
    }, [tags, getTagSelectionState, handleTagChange, disabled])

    return <FormGroup>{tagsList}</FormGroup>
}
