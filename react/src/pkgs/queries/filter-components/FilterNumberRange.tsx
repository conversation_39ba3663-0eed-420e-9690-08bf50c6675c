import { FilterComponentType } from '@/pkgs/queries/types'
import { Stack, Typography } from '@mui/material'
import React, { useEffect } from 'react'
import { FilterNumber } from '@/pkgs/queries/filter-components/FilterNumber'

export const FilterNumberRange: FilterComponentType<number[]> = (props) => {
    const { value = [0, 1], onChange, config = {} } = props

    useEffect(() => {
        if (props.value === undefined) {
            props.onChange([0, 1])
        }
    }, [props])

    const handleFromChange = (from: number) => {
        value[0] = from
        onChange([...value])
    }

    const handleToChange = (to: number) => {
        value[1] = to
        onChange([...value])
    }

    return (
        <Stack direction='row' spacing={1} alignItems='center'>
            <Typography variant='body2' color='text.secondary'>
                from
            </Typography>
            <FilterNumber
                value={value[0]}
                onChange={handleFromChange}
                config={{
                    ...config,
                    max: value[1] || undefined
                }}
            />
            <Typography variant='body2' color='text.secondary' marginLeft={'auto'}>
                to
            </Typography>
            <FilterNumber
                value={value[1]}
                onChange={handleToChange}
                config={{
                    ...config,
                    min: value[0] || undefined
                }}
            />
        </Stack>
    )
}
