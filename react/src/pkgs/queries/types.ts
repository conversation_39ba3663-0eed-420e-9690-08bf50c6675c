import { paged, PagingQuery, SortingQuery, trackable } from '@/common/react-query'
import { z } from 'zod'
import { structure } from '@/pkgs/structure/types'

const queryableTypesArray = [
    'checkbox',
    'select',
    'text',
    'email',
    'date',
    'privacy',
    'tags',
    'sites',
    'folders',
    'array',
    'document',
    'image',
    'schedule',
    'contact-form-link'
] as const
const QueryableTypeSchema = z.enum(queryableTypesArray)

const queryableTypesSet = new Set(queryableTypesArray)

export function isQueryableType(value: string): value is QueryableType {
    return queryableTypesSet.has(value as QueryableType)
}

export type QueryableType = (typeof queryableTypesArray)[number]

export type FieldType = QueryableType | 'rich-text' | 'textarea'

const OperationSchema = z.enum([
    'checked',
    'startsWith',
    'endsWith',
    'contains',
    'equalTo',
    'greaterThan',
    'after',
    'lessThan',
    'before',
    'between',
    'in',
    'containsAll',
    'privacyLevel',
    'exists',
    'sites',
    'folders',
    'lengthEqualTo',
    'lengthGreaterThan',
    'lengthLessThan',
    'lengthBetween',
    'timeRange'
])

export type Operation = z.infer<typeof OperationSchema>
const invertible: Operation[] = ['equalTo', 'in', 'between', 'startsWith', 'endsWith', 'contains', 'containsAll']

export function isInvertible(Operation: Operation): boolean {
    return invertible.includes(Operation)
}

export type SelectOption = {
    label: string
    value: string
}

type BaseField = {
    name: string
    label: string
}

export type Field =
    | (BaseField & {
          type: 'select'
          options: SelectOption[]
      })
    | (BaseField & {
          type: Exclude<FieldType, 'select'>
      })

const FilterSchema = z.object({
    FieldName: z.string(),
    FieldType: QueryableTypeSchema,
    Operation: OperationSchema,
    Inverted: z.boolean().optional(),
    Value: z.any() // You might want to refine this based on your needs
})

// Define the type based on the schema
export type Filter = z.infer<typeof FilterSchema>

type FilterComponentProps<TValue> = {
    value: TValue
    onChange: (value: TValue) => void
    options?: SelectOption[]
    config?: any
}

export type FilterComponentType<TValue = any> = React.ComponentType<FilterComponentProps<TValue>>

export type FilterComponentMapping = {
    [K in QueryableType]: {
        [F in Operation]?: FilterComponentType
    }
}

export type RunQueryParams = {
    ContentTypes: string[]
    StructureID: string | null
    Filters: Filter[]
} & PagingQuery &
    SortingQuery

export const queryResults = paged.extend({
    Rows: z.array(z.any())
})

export type SearchQueries = {
    Search?: string
    StructureID?: string
    Inactive?: boolean
    ContentType?: string
} & PagingQuery &
    SortingQuery

const sortingSchema = z.object({
    Field: z.string(),
    Direction: z.enum(['asc', 'desc'])
})

const sortingMethodsArray = ['firstN', 'date', 'value'] as const
const sortingMethodsSchema = z.enum(sortingMethodsArray)
const sortingMethodsSet = new Set(sortingMethodsArray)

export function isSortingMethod(value: string): value is SortingMethod {
    return sortingMethodsSet.has(value as SortingMethod)
}

const groupingSchema = sortingSchema.extend({
    Method: sortingMethodsSchema,
    Options: z.any().optional()
})

const dataSchema = z.object({
    Filters: z.array(FilterSchema),
    Sorting: sortingSchema,
    Grouping: z.array(groupingSchema),
    PageSize: z.number()
})

export const updateQueryDTO = z.object({
    Title: z.string(),
    Description: z.string(),
    Data: dataSchema
})

export const createQueryDTO = updateQueryDTO.extend({
    ContentTypes: z.array(z.string()),
    StructureID: z.string().nullable()
})

export const query = trackable.merge(createQueryDTO).extend({
    ID: z.string(),
    Active: z.boolean()
})

export const details = query.extend({
    Structure: structure.nullish()
})

export const queries = paged.extend({
    Rows: z.array(query)
})

type DataSchema = z.infer<typeof dataSchema>
export type Sorting = z.infer<typeof sortingSchema>
export type Grouping = z.infer<typeof groupingSchema>
export type Query = z.infer<typeof query>
export type Queries = z.infer<typeof queries>
export type Details = z.infer<typeof details>
export type UpdateQueryDTO = z.infer<typeof updateQueryDTO>
export type CreateQueryDTO = z.infer<typeof createQueryDTO>
export type SortingMethod = (typeof sortingMethodsArray)[number]
