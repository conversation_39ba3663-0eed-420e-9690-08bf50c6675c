import { httpDelete, httpPatch } from '@/common/client'
import { BASE } from '@/common/constants'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import { Edit, RestoreFromTrash } from '@mui/icons-material'
import DeleteIcon from '@mui/icons-material/Delete'
import React from 'react'
import { useAppNavigation } from '@/app/useAppNavigation'
import { Query } from '@/pkgs/queries/types'

export function queriesGridMenu(refetch: () => void) {
    const { navigateTo } = useAppNavigation()

    const deleteQuery = (id: string) => {
        httpDelete(`${BASE}/api/v1/queries/${id}`)
            .then(() => {
                notify('Query deleted', 'info')
                refetch()
            })
            .catch((err) => {
                notify(guessErrorMessage(err), 'error')
                refetch()
            })
    }

    const restoreQuery = (id: string) => {
        httpPatch(`${BASE}/api/v1/queries/${id}/restore`, null)
            .then(() => {
                notify('Query restored', 'info')
                refetch()
            })
            .catch((err) => {
                notify(guessErrorMessage(err), 'error')
                refetch()
            })
    }

    const menuItems = (query: Query) => {
        return (onClose: () => void) => {
            return query.Active
                ? [
                      <CustomMenuItem
                          key={'Edit'}
                          text={'Edit'}
                          onClick={() => {
                              navigateTo(`/queries/${query?.ID}`)
                          }}
                      >
                          <Edit />
                      </CustomMenuItem>,

                      <CustomMenuItem
                          key={'Delete'}
                          text={'Delete'}
                          onClick={() => {
                              window.confirm(
                                  `Are you sure you want to delete the query "${query.Title}" with ID="${query.ID}"?`
                              ) && deleteQuery(query.ID)
                              onClose()
                          }}
                      >
                          <DeleteIcon />
                      </CustomMenuItem>
                  ]
                : [
                      <CustomMenuItem
                          key={'Restore'}
                          text={'Restore'}
                          onClick={() => {
                              restoreQuery(query.ID)
                              onClose()
                          }}
                      >
                          <RestoreFromTrash />
                      </CustomMenuItem>
                  ]
        }
    }

    return { menuItems }
}
