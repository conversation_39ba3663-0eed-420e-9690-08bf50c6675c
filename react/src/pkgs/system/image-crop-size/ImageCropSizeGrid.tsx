import PageContainerWithHeader from '@/common/components/PageContainerWithHeader'
import { createImageCropSizeQuery, deleteImageCropSizeQuery, useImageCropSizeQuery } from './queries'
import { GridColDef } from '@mui/x-data-grid'
import { CellLine, CellWrapper, TwoLinesCell } from '@/pkgs/grid/cells/GridCells'
import { DataGridBase } from '@/pkgs/grid/DataGridBase'
import { Box, Button, DialogActions, DialogContent, IconButton, Stack, Typography } from '@mui/material'
import CustomIconButton from '@/common/components/CustomIconButton'
import { useCallback, useEffect, useState } from 'react'
import { AddButton, ConfirmAction } from '@/common/components'
import DeleteIcon from '@mui/icons-material/Delete'
import { useMutation } from '@tanstack/react-query'
import CMDialog from '@/common/components/CMDialog'
import CMTextField from '@/common/components/CMTextField'
import { BoxForm } from '@/common/components/BoxForm'
import { notify } from '@/helpers'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { ImageCropSize, ImageCropSizeDTO } from './types'
import moment from 'moment'
import { useAppContext } from '@/pkgs/auth/atoms'

function ImageCropSizeGrid() {
    const appContext = useAppContext()
    const hasPermission = appContext.entityScopeAny('cm.image.crop')
    const { data, refetch, isLoading } = useImageCropSizeQuery()
    const [addCropSizeDialogIsOpen, setAddCropSizeDialogIsOpen] = useState(false)
    const [addCropSizeFormData, setAddCropSizeFormData] = useState({
        Name: '',
        Width: '',
        Height: ''
    })
    const [addCropSizeFormDataErrors, setAddCropSizeFormDataErrors] = useState<
        Partial<Record<keyof ImageCropSizeDTO, string>>
    >({})

    const createCropSizeMutation = useMutation({
        mutationFn: createImageCropSizeQuery,
        onSuccess: () => {
            // toast, success
            notify(`Success! Created CropSize: ${addCropSizeFormData.Name}`, 'info')
            refetch()
            setAddCropSizeDialogIsOpen(false)
            setAddCropSizeFormData({
                Name: '',
                Width: '',
                Height: ''
            })
        },
        onError: (e) => {
            const msg = guessErrorMessage(e)
            notify(`Failed to create CropSize: \n ${msg}`)
        }
    })

    const handleSubmit = useCallback(() => {
        const width = Number(addCropSizeFormData.Width)
        const height = Number(addCropSizeFormData.Height)
        const name = addCropSizeFormData.Name

        const newErrors: Partial<Record<keyof ImageCropSizeDTO, string>> = {}

        if (!name) {
            newErrors.Name = 'Invalid name provided.'
        }

        if (width < 10 || width > 2048 || isNaN(width)) {
            newErrors.Width = 'Invalid width provided.'
        }

        if (height < 10 || height > 2048 || isNaN(height)) {
            newErrors.Height = 'Invalid height provided.'
        }

        if (Object.keys(newErrors).length > 0) {
            setAddCropSizeFormDataErrors(newErrors)
            return
        }

        const newCropSize: ImageCropSizeDTO = {
            Name: addCropSizeFormData.Name,
            Width: Number(addCropSizeFormData.Width),
            Height: Number(addCropSizeFormData.Height)
        }

        createCropSizeMutation.mutate(newCropSize)
    }, [addCropSizeFormData, createImageCropSizeQuery])

    const deleteCropSizeMutation = useMutation({
        mutationFn: deleteImageCropSizeQuery,
        onSuccess: () => {
            // toast, success
            refetch()
        }
    })

    useEffect(() => {
        // FORM VALIDATION
        setAddCropSizeFormDataErrors((p) => ({ ...p, Name: '' }))
        if (addCropSizeFormData.Width != '') {
            const valueAsNum = Number(addCropSizeFormData.Width)
            const valueAsNumIsWithinBoundary = valueAsNum >= 1 && valueAsNum <= 2048

            setAddCropSizeFormDataErrors((p) => ({
                ...p,
                Width: !valueAsNumIsWithinBoundary || !valueAsNum ? 'Invalid value' : ''
            }))
        } else {
            setAddCropSizeFormDataErrors((p) => ({
                ...p,
                Width: ''
            }))
        }

        if (addCropSizeFormData.Height != '') {
            const valueAsNum = Number(addCropSizeFormData.Height)
            const valueAsNumIsWithinBoundary = valueAsNum >= 1 && valueAsNum <= 2048

            setAddCropSizeFormDataErrors((p) => ({
                ...p,
                Height: !valueAsNumIsWithinBoundary || !valueAsNum ? 'Invalid value' : ''
            }))
        } else {
            setAddCropSizeFormDataErrors((p) => ({
                ...p,
                Height: ''
            }))
        }
    }, [addCropSizeFormData, setAddCropSizeFormDataErrors])

    const columns: GridColDef[] = [
        {
            field: 'Name',
            headerName: 'Name',
            flex: 2,
            disableColumnMenu: true,
            sortable: true,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Name}`} l2={params.row.ID} />
        },
        {
            field: 'Dimensions',
            headerName: 'Dimensions',
            flex: 2,
            align: 'left',
            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderCell: (params) => (
                <CellLine>
                    {params.row.Width} x {params.row.Height} px
                </CellLine>
            )
        },
        {
            field: 'CreatedAt',
            headerName: 'Created Date',
            flex: 2,
            align: 'left',
            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderCell: (params) => {
                const date = moment(params.row.CreatedAt).format('YYYY-MM-DD, h:mm:ss a')?.split(',')
                return <TwoLinesCell l1={date[0]} l2={date[1]} />
            }
        },
        {
            field: 'Menu',
            headerName: 'Actions',
            headerAlign: 'left',
            flex: 2,
            align: 'left',
            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderCell: (params) => {
                const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false)

                const handleDialogDelete = (cropSize: ImageCropSize) => {
                    deleteCropSizeMutation.mutate(cropSize.ID)
                    setConfirmDeleteDialogOpen(false)
                }

                return (
                    <CellWrapper style={{ textAlign: 'left' }}>
                        -
                        {/* <CustomIconButton
                            disabled={!hasPermission}
                            disabled={true}
                            size={'small'}
                            aria-haspopup={'true'}
                            onClick={(e) => {
                                setConfirmDeleteDialogOpen(true)
                            }}
                        >
                            <DeleteIcon />
                        </CustomIconButton>
                        <ConfirmAction
                            open={confirmDeleteDialogOpen}
                            title={`Delete CropSize?: "${params?.row.Name}"`}
                            text={'Are you sure you want to delete this image CropSize?'}
                            handleClose={() => setConfirmDeleteDialogOpen(false)}
                            handleDisagree={() => setConfirmDeleteDialogOpen(false)}
                            handleAgree={() => handleDialogDelete(params.row)}
                        /> */}
                    </CellWrapper>
                )
            }
        }
    ]

    return (
        <PageContainerWithHeader
            title='Image Crop Sizes'
            topRightElement={
                hasPermission ? (
                    <AddButton title={'ADD CropSize'} func={() => setAddCropSizeDialogIsOpen(true)} />
                ) : undefined
            }
        >
            <DataGridBase
                loading={isLoading}
                disableColumnFilter
                disableDensitySelector
                disableColumnSelector
                columns={columns}
                state={data}
                setQuery={(val) => {}}
                hideFooter
            />
            {addCropSizeDialogIsOpen && (
                <CMDialog
                    open={addCropSizeDialogIsOpen}
                    title='Add Image CropSize'
                    showCloseButton
                    onClose={() => setAddCropSizeDialogIsOpen(false)}
                >
                    <DialogContent>
                        <BoxForm>
                            <CMTextField
                                required
                                label='Name'
                                size='small'
                                helperText='Enter a descriptive name for this CropSize without any spaces'
                                errorText={addCropSizeFormDataErrors?.Name}
                                value={addCropSizeFormData?.Name || ''}
                                onChange={(ev) => {
                                    const value = ev.target.value
                                    setAddCropSizeFormData((p) => ({ ...p, Name: value.replace(' ', '-') }))
                                }}
                            />
                            <Stack direction='row' gap='4px'>
                                <CMTextField
                                    required
                                    label='Width'
                                    size='small'
                                    errorText={addCropSizeFormDataErrors?.Width}
                                    helperText='Between 1 and 2048'
                                    value={addCropSizeFormData?.Width || ''}
                                    onChange={(ev) => {
                                        setAddCropSizeFormData((p) => ({ ...p, Width: ev.target.value }))
                                    }}
                                />
                                <CMTextField
                                    required
                                    label='Height'
                                    size='small'
                                    errorText={addCropSizeFormDataErrors?.Height}
                                    helperText='Between 1 and 2048'
                                    value={addCropSizeFormData?.Height || ''}
                                    onChange={(ev) => {
                                        setAddCropSizeFormData((p) => ({ ...p, Height: ev.target.value }))
                                    }}
                                />
                            </Stack>
                            <Typography textAlign='center'>
                                Dimensions: {addCropSizeFormData.Width || 0} x {addCropSizeFormData.Height || 0} pixels
                            </Typography>
                        </BoxForm>
                    </DialogContent>
                    <DialogActions>
                        <Button
                            onClick={() =>
                                setAddCropSizeFormData({
                                    Name: '',
                                    Width: '',
                                    Height: ''
                                })
                            }
                        >
                            Clear
                        </Button>
                        <Button disabled={!hasPermission} variant='contained' onClick={() => handleSubmit()}>
                            Save
                        </Button>
                    </DialogActions>
                </CMDialog>
            )}
        </PageContainerWithHeader>
    )
}

export default ImageCropSizeGrid
