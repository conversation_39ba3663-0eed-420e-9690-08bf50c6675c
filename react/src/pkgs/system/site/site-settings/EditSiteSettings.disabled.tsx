import { render } from '@testing-library/react'
import EditSiteSettings from './EditSiteSettings'
import { BrowserRouter } from 'react-router-dom'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import * as useSiteForAccountsQueryMutation from '../../../../common/query-hooks/useSiteForAccountsQueryMutation'
import { primaryTheme } from '../../../../app/theme'
import { ThemeProvider } from '@mui/material'
import { vi } from 'vitest'

const queryClient = new QueryClient({
    defaultOptions: {
        queries: {
            retry: false
        }
    }
})

const mockData = {
    data: {
        name: 'Mock Site Name'
    },
    isLoading: false,
    error: {}
}

function MockThemeProvider({ children }: any) {
    return <ThemeProvider theme={primaryTheme}>{children}</ThemeProvider>
}

export const globalWrapper = ({ children }) => {
    return (
        <BrowserRouter>
            <QueryClientProvider client={queryClient}>
                <MockThemeProvider>{children}</MockThemeProvider>
            </QueryClientProvider>
        </BrowserRouter>
    )
}

describe('EditSiteSettings.tsx', () => {
    beforeEach(() => {
        vi.spyOn(useSiteForAccountsQueryMutation, 'default').mockImplementation(() => ({
            siteData: mockData.data,
            error: mockData.error,
            refetch: vi.fn(),
            mutation: vi.fn() as any
        }))
    })

    test('shows subtitle', () => {
        const t = render(<EditSiteSettings />, { wrapper: globalWrapper })

        // all tabs visible
        // editSettingsTabs.forEach((tab) => {
        //     expect(screen.getByText(tab.name)).toBeVisible()
        // })
        //
        // // subtitle visible
        // const subtitleElement = screen.getByText(mockData.data.name)
        // expect(subtitleElement).toBeTruthy()
    })
})
