// import React, { useEffect, useState } from 'react'
// import { <PERSON><PERSON>, Card, CardContent, CircularProgress } from '@mui/material'
// import { makeStyles } from '@mui/styles'
// import axios from 'axios'
// import CheckCircleIcon from '@mui/icons-material/CheckCircle'
// import { contentService } from '../../../content/content.service'
// import { siteService } from '../site.service'
// import { integrationService } from './integration.service'
// import Integration from './IntegrationItem'
// import { useCurrentSite, useCurrentSiteID } from '../../../auth/atoms'
// import { PageContainerWithHeader } from '../../../../common/components/PageContainerWithHeader'
// import { BackButton } from '../../../../common/components'
// import { ContentType } from '../../../content/types'
// import { FbAuthAPI, FbAuthVerifyAPI, GCVerifyAPI } from '../../../../common/constants'
//
// export const Create = 'create'
// export const Delete = 'delete'
// export const templateContext = React.createContext([{}, () => {}])
// // TODO => CM-2213
// //  This should be hoisted up into App, avoid any naming overlap in the meantime
// export const currentSiteContext = React.createContext([{}, () => {}])
//
// export const Integrations = () => {
//     const classes = useStyles()
//     const currentSiteID = useCurrentSiteID()
//     const currentSite = useCurrentSite()
//     const [site, setSite] = useState({})
//
//     const [templates, setTemplates] = useState({ news: [], events: [] })
//     const [availableIntegrations, setAvailableIntegrations] = useState({ facebook: false, googleCalendar: false })
//     // Facebook
//     const [isLoggedIntoFacebook, setIsLoggedIntoFacebook] = useState(true)
//     const [isLoading, setIsLoading] = useState(false)
//     // Google Calendar
//     const [googleCalendarResponse, setGoogleCalendarResponse] = useState(200)
//     const [isGoogleCalendarLoading, setIsGoogleCalendarLoading] = useState(false)
//
//     useEffect(() => {
//         setIsLoading(true)
//         if (true /* sites?.some((x) => x?.settings?.FacebookPageId) */) {
//             // setHasFacebook(true)
//             setAvailableIntegrations((prev) => ({ ...prev, facebook: true }))
//             axios
//                 .get(FbAuthVerifyAPI)
//                 .then(() => {
//                     setIsLoggedIntoFacebook(true)
//                     setIsLoading(false)
//                 })
//                 .catch(() => {
//                     setIsLoggedIntoFacebook(false)
//                     setIsLoading(false)
//                 })
//         }
//     }, [])
//
//     useEffect(() => {
//         if (!isLoading) setIsLoading(true)
//
//         Promise.all([
//             // siteService.get(currentSiteID, DefaultPage, DefaultMax, false).then(({ results }) => results),
//             siteService.getById(currentSiteID),
//             contentService
//                 .getAllByClassification(currentSiteID, ContentType.News, false)
//                 .then(({ results }) => results),
//             contentService
//                 .getAllByClassification(currentSiteID, ContentType.Event, false)
//                 .then(({ results }) => results)
//         ]).then(([site, newsTemplates, eventTemplates, tags]) => {
//             setSite(site)
//             setTemplates({
//                 news: newsTemplates.filter((x) => !x.structure) || [],
//                 events: eventTemplates.filter((x) => !x.structure) || []
//             })
//             setIsLoading(false)
//         })
//
//         setIsGoogleCalendarLoading(true)
//         axios
//             .get(GCVerifyAPI, { params: { siteId: currentSiteID } })
//             .then(
//                 () => setAvailableIntegrations((prev) => ({ ...prev, googleCalendar: true })),
//                 (error) => {
//                     setGoogleCalendarResponse(error?.response?.status)
//                     setAvailableIntegrations((prev) => ({ ...prev, googleCalendar: false }))
//                 }
//             )
//             .finally(() => setIsGoogleCalendarLoading(false))
//     }, [currentSiteID])
//
//     const fbLogin = () => {
//         if (window.FB) {
//             window.FB.init({
//                 appId: '913641556131535',
//                 autoLogAppEvents: true,
//                 xfbml: true,
//                 version: 'v11.0'
//             })
//             window.FB.getLoginStatus(function (response) {
//                 if (response.authResponse) {
//                     axios
//                         .post(FbAuthAPI, response, { params: { siteId: currentSiteID } })
//                         .then((x) => setIsLoggedIntoFacebook(true))
//                 } else {
//                     window.FB.login(
//                         function (response) {
//                             if (response.authResponse) {
//                                 axios
//                                     .post(FbAuthAPI, response, { params: { siteId: currentSiteID } })
//                                     .then((x) => setIsLoggedIntoFacebook(true))
//                             } else {
//                                 setIsLoggedIntoFacebook(false)
//                                 console.log('User cancelled login or did not fully authorize.')
//                             }
//                         },
//                         { scope: 'public_profile,email,pages_show_list,pages_read_engagement' }
//                     )
//                 }
//             })
//         }
//     }
//
//     function RenderImportIntegrations() {
//         return Object.values(integrationService.ImportInfo).map((integration) => (
//             <Integration key={integration?.pretty + integration?.idName} {...integration} site={site} />
//         ))
//     }
//
//     if (!Object.keys(site).length) {
//         return <CircularProgress />
//     }
//     console.log(site)
//     return (
//         <currentSiteContext.Provider value={[site, () => {}]}>
//             <templateContext.Provider value={[templates, setTemplates]}>
//                 {currentSite?.Type === 'department' ? (
//                     <div>This feature is not available for departments</div>
//                 ) : (
//                     <PageContainerWithHeader title='Integrations' topRightElement={<BackButton route={'/'} />}>
//                         <Card className={classes.root}>
//                             <CardContent>
//                                 <div className='groups-container' style={{ padding: '5px' }}>
//                                     <h2 className='four-font-weight' style={{ padding: '2px', margin: '0' }}>
//                                         {' '}
//                                         Facebook{' '}
//                                     </h2>
//                                     {availableIntegrations.facebook ? (
//                                         <div>
//                                             {isLoading ? (
//                                                 <CircularProgress />
//                                             ) : (
//                                                 <div>
//                                                     <div className='flex-row'>
//                                                         <p style={{ padding: '2px' }}>
//                                                             {isLoggedIntoFacebook
//                                                                 ? 'Facebook Integration is active'
//                                                                 : 'Facebook Integration is inactive'}
//                                                         </p>
//                                                         <CheckCircleIcon
//                                                             style={
//                                                                 isLoggedIntoFacebook
//                                                                     ? { color: 'green' }
//                                                                     : { color: 'red' }
//                                                             }
//                                                         />
//                                                     </div>
//                                                     <Button
//                                                         variant='outlined'
//                                                         color={'primary'}
//                                                         size='medium'
//                                                         onClick={fbLogin}
//                                                         disabled={isLoggedIntoFacebook}
//                                                     >
//                                                         Login to Facebook
//                                                     </Button>
//                                                 </div>
//                                             )}
//                                         </div>
//                                     ) : (
//                                         <div>
//                                             <h3 className='four-font-weight'>
//                                                 {' '}
//                                                 Your tenant is not configured for Facebook Integration
//                                             </h3>
//                                             <p>
//                                                 {' '}
//                                                 To integrate with Facebook, Begin adding your Facebook Page ID's as
//                                                 "FacebookPageId" to the Site Settings of the corresponding school site
//                                             </p>
//                                         </div>
//                                     )}
//                                     <hr style={{ opacity: '40%' }} />
//                                 </div>
//                                 <div className='groups-container' style={{ padding: '5px' }}>
//                                     <h2 className='four-font-weight' style={{ padding: '2px', margin: '0' }}>
//                                         {' '}
//                                         Google Calendar{' '}
//                                     </h2>
//                                     <div>
//                                         {isGoogleCalendarLoading ? (
//                                             <CircularProgress />
//                                         ) : (
//                                             <div>
//                                                 {availableIntegrations.googleCalendar ? (
//                                                     <div className='flex-row'>
//                                                         <p style={{ padding: '2px' }}>
//                                                             Google Calendar Integration is active
//                                                         </p>
//                                                         <CheckCircleIcon style={{ color: 'green' }} />
//                                                     </div>
//                                                 ) : (
//                                                     <div>
//                                                         <h3 className='four-font-weight'>
//                                                             Your {googleCalendarResponse === 401 ? 'tenant' : 'site'} is
//                                                             not configured for Google Calendar Integration
//                                                         </h3>
//                                                         {googleCalendarResponse === 401 ? (
//                                                             <p>
//                                                                 {' '}
//                                                                 To integrate with Google Calendar, please reach out to
//                                                                 <a
//                                                                     target={'_blank'}
//                                                                     href={'mailto:<EMAIL>'}
//                                                                     style={{ color: '#f15d43', textDecoration: 'none' }}
//                                                                 >
//                                                                     {' '}
//                                                                     Imagine Everything Support
//                                                                 </a>
//                                                             </p>
//                                                         ) : (
//                                                             <p>
//                                                                 {' '}
//                                                                 To integrate with Google Calendar, Please add your
//                                                                 Google Calendar ID as "GoogleCalendarID" to your Site
//                                                                 Settings
//                                                             </p>
//                                                         )}
//                                                     </div>
//                                                 )}
//                                             </div>
//                                         )}
//                                     </div>
//                                     <hr style={{ opacity: '40%' }} />
//                                 </div>
//                             </CardContent>
//                         </Card>
//                         <RenderImportIntegrations />
//                     </PageContainerWithHeader>
//                 )}
//             </templateContext.Provider>
//         </currentSiteContext.Provider>
//     )
// }
// const useStyles = makeStyles((theme) => ({
//     root: {
//         minWidth: 275,
//         marginBottom: '1vh'
//     },
//     card: {
//         overflow: 'auto',
//         minHeight: '42vh',
//         maxHeight: '30vh',
//         minWidth: 400,
//         maxWidth: 400
//     },
//     form: {
//         marginTop: '2vh',
//         marginBottom: '3vh',
//         height: '5%',
//         width: '30%'
//     },
//     edsbyForm: {
//         marginTop: '1vh',
//         marginBottom: '1vh',
//         width: '50%'
//     },
//     label: { fontSize: '0.9em' }
// }))
