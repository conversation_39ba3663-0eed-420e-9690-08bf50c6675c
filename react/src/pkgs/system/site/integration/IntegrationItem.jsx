// import React, { useContext, useEffect, useState } from 'react'
// import { AddButton, ConfirmAction } from '../../../../common/components'
// import {
//     Button,
//     Card,
//     CardContent,
//     Checkbox,
//     CircularProgress,
//     Dialog,
//     DialogActions,
//     DialogContent,
//     DialogContentText,
//     DialogTitle,
//     FormControl,
//     FormControlLabel,
//     FormGroup,
//     InputLabel,
//     MenuItem,
//     Select,
//     TextField
// } from '@mui/material'
// import { Create, Delete, templateContext } from './Integrations'
// import { makeStyles } from '@mui/styles'
// import { is, validateUUID } from '../../../../helpers'
// import _ from 'lodash'
// import { tagService } from '../../tags/tag.service'
// import { useIntegrationContext } from './useIntegrationContext'
//
// export default function Integration({ name, pretty, idName, createDialogText, handlesNews, handlesEvents, site }) {
//     // const [site] = useContext(currentSiteContext)
//     const [templates] = useContext(templateContext)
//     const [tags, setTags] = useState([])
//     const ctx = useIntegrationContext(site, tags, name, handleDialog)
//     const [dialogState, setDialogState] = useState({ create: false, delete: false })
//     const classes = useStyles()
//
//     useEffect(() => {
//         const run = async () => {
//             const res = await tagService.getAllAlt(null, { tagType: 'content' })
//             if (res.success) setTags(res.data.results)
//         }
//         run().catch(console.error)
//     }, [site])
//
//     function handleDialog(type, value) {
//         if (type in dialogState) setDialogState((prev) => ({ ...prev, [type]: value }))
//     }
//
//     return (
//         <Card className={classes.card}>
//             <CardContent>
//                 {ctx.isLoading ? (
//                     <div className={classes.progressIndicator}>
//                         <CircularProgress />
//                     </div>
//                 ) : (
//                     <>
//                         <div className='flex-row-align-center' style={{ justifyContent: 'space-between' }}>
//                             <h2 className='four-font-weight' style={{ padding: '2px', margin: '0' }}>
//                                 {pretty}
//                             </h2>
//                             {ctx.isIntegrationAvailable && (
//                                 <AddButton title={'Add Integration'} func={() => handleDialog(Create, true)} />
//                             )}
//                         </div>
//                         {ctx.isIntegrationAvailable ? (
//                             <div>
//                                 <FormControl
//                                     variant='standard'
//                                     className={classes.formControl}
//                                     style={{ display: 'flex', flexDirection: 'row', alignItems: 'center' }}
//                                 >
//                                     <InputLabel>Existing {pretty} Integrations</InputLabel>
//                                     <Select
//                                         variant='standard'
//                                         disabled={ctx.isApplyingToAll()}
//                                         value={ctx.currentConfig.id || ''}
//                                         style={{ width: '100%' }}
//                                         onChange={(e) =>
//                                             ctx.setCurrentConfig(
//                                                 ctx.collection.find((x) => x.id === String(e?.target?.value)) || {}
//                                             )
//                                         }
//                                     >
//                                         {ctx.collection.map((e) => (
//                                             <MenuItem key={e.id} value={e.id}>
//                                                 {e.id}
//                                             </MenuItem>
//                                         ))}
//                                     </Select>
//                                 </FormControl>
//
//                                 {handlesNews && (
//                                     <>
//                                         <ConfigSelect
//                                             ctx={ctx}
//                                             label={'News Templates'}
//                                             type={'news'}
//                                             property={'templateId'}
//                                             opts={templates.news}
//                                         />
//                                         <ConfigSelect
//                                             ctx={ctx}
//                                             multiple
//                                             label={'News Tags'}
//                                             type={'news'}
//                                             field={'tags'}
//                                             property={'default'}
//                                             opts={tags || []}
//                                         />
//                                     </>
//                                 )}
//
//                                 {handlesEvents && (
//                                     <>
//                                         <ConfigSelect
//                                             ctx={ctx}
//                                             label={'Event Templates'}
//                                             type={'events'}
//                                             property={'templateId'}
//                                             opts={templates.events}
//                                         />
//                                         <ConfigSelect
//                                             ctx={ctx}
//                                             multiple
//                                             label={'Event Tags'}
//                                             type={'events'}
//                                             field={'tags'}
//                                             property={'default'}
//                                             opts={tags || []}
//                                         />
//                                     </>
//                                 )}
//
//                                 <div className='flex-row' style={{ justifyContent: 'flex-end' }}>
//                                     <Button
//                                         size={'small'}
//                                         color={'secondary'}
//                                         variant={'outlined'}
//                                         style={{ margin: '5px', marginTop: '10px' }}
//                                         disabled={!ctx.currentConfig.id || ctx.isApplyingToAll()}
//                                         onClick={() => handleDialog(Delete, true)}
//                                     >
//                                         Delete
//                                     </Button>
//                                     <Button
//                                         size={'small'}
//                                         color={'primary'}
//                                         variant={'outlined'}
//                                         style={{ margin: '5px', marginTop: '10px' }}
//                                         disabled={!ctx.currentConfig.id && !ctx.applyAll.events && !ctx.applyAll.news}
//                                         onClick={ctx.updateConfig}
//                                     >
//                                         Save
//                                     </Button>
//                                 </div>
//                             </div>
//                         ) : (
//                             <UnavailableIntegration name={pretty} />
//                         )}
//                     </>
//                 )}
//             </CardContent>
//             <Dialog maxWidth='sm' open={dialogState.create} onClose={() => handleDialog(Create, false)}>
//                 <DialogTitle>Integrate with {pretty}</DialogTitle>
//                 <DialogContent>
//                     <DialogContentText>{createDialogText}</DialogContentText>
//                     <TextField
//                         variant='standard'
//                         label={idName}
//                         value={ctx.createModel.id || ''}
//                         onChange={(e) => {
//                             ctx.setCreateModel((prev) => ({ ...prev, id: e?.target?.value || '' }))
//                             ctx.setErrors((prev) => ({ ...prev, id: false }))
//                         }}
//                         error={ctx.errors.id}
//                         style={{ width: '100%' }}
//                     />
//                 </DialogContent>
//                 <DialogActions>
//                     <Button onClick={() => handleDialog(Create, false)}>CANCEL</Button>
//                     <Button onClick={ctx.addConfig}>SAVE</Button>
//                 </DialogActions>
//             </Dialog>
//             <ConfirmAction
//                 open={dialogState.delete}
//                 handleAgree={ctx.deleteConfig}
//                 handleClose={() => handleDialog(Delete, false)}
//                 handleDisagree={() => handleDialog(Delete, false)}
//                 text={`This will remove the integration for ${site.name} - News and/or Events will not be imported from this source for ${site.name} after confirming`}
//                 title={`Remove integration for ${pretty} with ${idName}: ${ctx.currentConfig.id}`}
//             />
//         </Card>
//     )
// }
//
// function ConfigSelect({ ctx, label, type, property, field, opts, multiple }) {
//     const classes = useStyles()
//     const def = multiple ? [] : ''
//
//     function handleNullIdAsValue(v) {
//         if (!multiple) return validateUUID(v) ? v : ''
//         if (multiple) {
//             if (!v || !Array.isArray(v)) return []
//         }
//         return v
//     }
//
//     function handleChange(event) {
//         let { value = '' } = event?.target || {}
//         if (multiple && typeof value === 'string') {
//             value = getSelectProperty()
//         }
//         // v2
//         const copy = _.cloneDeep(ctx.currentConfig)
//         if (field && copy?.[type]?.fields) {
//             if (copy?.[type]?.fields?.[field]) {
//                 copy[type].fields[field][property] = value
//             } else {
//                 copy[type].fields[field] = { [property]: value }
//             }
//         } else if (copy?.[type]?.[property]) {
//             copy[type][property] = value
//         }
//         ctx.setCurrentConfig(copy)
//         // v1
//         // if ( field ) ctx.setCurrentConfig(prev => ({ ...prev, [type]: {...prev?.[type], fields: {...prev?.[type]?.fields, [field]: {...prev?.[type]?.[field], [name]: value}} }}))
//         // else ctx.setCurrentConfig(prev => ({ ...prev, [type]: {...prev?.[type], [name]: value} }))
//     }
//
//     function getSelectProperty() {
//         let base = ctx.currentConfig?.[type]
//         if (!base) return def
//         if (field) {
//             base = base?.fields?.[field]
//         }
//         return base?.[property] || def
//     }
//
//     function handleCheckedChange() {
//         // const copy = _.cloneDeep(ctx.applyAll)
//         // if ( field && copy?.[type]) {
//         //     copy[type][field] = !getCheckedProperty()
//         // }
//         ctx.setApplyAll((prev) => ({
//             ...prev,
//             [type]: { ...prev?.[type], [field || property]: !getCheckedProperty() }
//         }))
//     }
//
//     function getCheckedProperty() {
//         let base = ctx.applyAll?.[type]
//         if (!base) return false
//         let key = field || property
//         return Boolean(base[key])
//     }
//
//     function renderValue(selected) {
//         if (is.array(selected)) {
//             const re = {}
//             for (const item of selected) {
//                 let key = item?.id || item
//                 let found = opts?.find((opt) => opt?.id === key)
//                 re[key] = found?.name || found?.title
//             }
//             return Object.values(re).join(', ')
//         }
//         if (is.uuid(selected)) {
//             const found = opts?.find((opt) => opt?.id === selected)
//             return found?.name || found?.title || ''
//         }
//         return selected
//     }
//
//     return (
//         <div className='flex-row'>
//             <FormControl variant='standard' className={classes.formControl}>
//                 <InputLabel>{label}</InputLabel>
//                 <Select
//                     variant='standard'
//                     value={handleNullIdAsValue(getSelectProperty())}
//                     onChange={handleChange}
//                     multiple={Boolean(multiple)}
//                     renderValue={renderValue}
//                     style={{ width: '100%' }}
//                 >
//                     {opts.map((nt) => (
//                         <MenuItem key={nt.id + (nt.title || nt.name)} value={nt.id}>
//                             {nt.title || nt.name}
//                         </MenuItem>
//                     ))}
//                 </Select>
//             </FormControl>
//             <FormGroup style={{ width: '50%' }}>
//                 <FormControlLabel
//                     control={
//                         <Checkbox
//                             style={{ marginLeft: '2rem' }}
//                             onChange={handleCheckedChange}
//                             checked={getCheckedProperty()}
//                             name='first'
//                         />
//                     }
//                     label={`Apply to all configurations for all sites you have access to`}
//                     classes={{ label: classes.label }}
//                 />
//             </FormGroup>
//         </div>
//     )
// }
//
// function UnavailableIntegration({ name }) {
//     return (
//         <div>
//             <h3 className='four-font-weight'> Your tenant is not configured for {name} Integration</h3>
//             <p>
//                 {' '}
//                 To integrate with {name}, please reach out to
//                 <a
//                     target={'_blank'}
//                     href={'mailto:<EMAIL>'}
//                     style={{ color: '#f15d43', textDecoration: 'none' }}
//                 >
//                     {' '}
//                     Imagine Everything Support
//                 </a>
//             </p>
//         </div>
//     )
// }
//
// const useStyles = makeStyles((theme) => ({
//     formControl: {
//         marginTop: '1vh',
//         marginBottom: '1vh',
//         width: '50%'
//     },
//     progressIndicator: {
//         display: 'flex',
//         flexDirection: 'row',
//         alignItems: 'center',
//         minHeight: '125px',
//         minWidth: '100%',
//         justifyContent: 'center'
//     },
//     label: {
//         fontSize: '0.9em'
//     },
//     card: {
//         marginBottom: '1vh',
//         padding: '5px'
//     }
// }))
//
// // <div className="flex-row" style={{ alignItems:"end" }}>
// //     <FormControl className={classes.formControl}>
// //         <InputLabel >News Templates</InputLabel>
// //         <Select
// //             value={handleNullIdAsValue(ctx.currentConfig?.news?.templateId)}
// //             style={{width:"100%"}}
// //             name={"templateId"}
// //             onChange={(e) => {
// //
// //                 console.log({e, tar:e.target})
// //                 ctx.setCurrentConfig(prev => ({ ...prev, news: {...prev.news, templateId: e?.target?.value || "" } }))
// //             }}
// //         >
// //             {templates.news.map(nt => <MenuItem key={nt.id + nt.title} value={nt.id}>{nt.title}</MenuItem>)}
// //         </Select>
// //     </FormControl>
// //     <FormGroup style={{width:"50%"}}>
// //         <FormControlLabel
// //             control={<Checkbox style={{marginLeft:"2rem"}} onChange={() => ctx.setApplyAll(prev => ({...prev, news: !prev.news}))} checked={ctx.applyAll.news} name="first" />}
// //             label={`Apply this news template to ${pretty} configurations for all sites you have access to`}
// //             classes={{label: classes.label}}
// //         />
// //     </FormGroup>
// // </div>
// // <div  className="flex-row" style={{ alignItems:"end"}}>
// //     <FormControl className={classes.formControl}>
// //         <InputLabel >Event Templates</InputLabel>
// //         <Select
// //             style={{width:"100%"}}
// //             value={handleNullIdAsValue(ctx.currentConfig?.events?.templateId)}
// //             onChange={(e) => {
// //
// //                 console.log({e, tar:e.target})
// //                 ctx.setCurrentConfig(prev => ({ ...prev, events: {...prev.events, templateId: e?.target?.value || "" }}))
// //             }}
// //         >
// //             {templates.events.map(et => <MenuItem key={et.id + et.title} value={et.id}>{et.title}</MenuItem>)}
// //         </Select>
// //     </FormControl>
// //     <FormGroup style={{width:"50%"}}>
// //         <FormControlLabel
// //             control={<Checkbox style={{marginLeft:"2rem"}} onChange={() => ctx.setApplyAll(prev => ({...prev, events: !prev.events}))} checked={ctx.applyAll.events} name="first" />}
// //             label={`Apply this event template to ${pretty} configurations for all sites you have access to`}
// //             classes={{label: classes.label}}
// //         />
// //     </FormGroup>
// // </div>
