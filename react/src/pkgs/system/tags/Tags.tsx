import React, { useCallback, useEffect, useState } from 'react'
import { DialogA<PERSON>, DialogContent, Grid, IconButton, Stack } from '@mui/material'
import ConfirmAction from '../../../common/components/ConfirmAction'
import CreateIcon from '@mui/icons-material/Create'
import DeleteIcon from '@mui/icons-material/Delete'
import { MoreHoriz } from '@mui/icons-material'
import { GridColDef } from '@mui/x-data-grid'
import { useMutation } from '@tanstack/react-query'
import { AddButton } from '../../../common/components'
import PageContainerWithHeader from '../../../common/components/PageContainerWithHeader'
import SplitButton from '../../../common/components/SplitButton'
import { CustomMenu, CustomMenuItem } from '../../../common/components/custom-context-menu/CustomMenu'
import { defaultPageQuery } from '../../../common/react-query'
import { capitalizeFirstLetter, notify } from '../../../helpers'
import { guessErrorMessage } from '../../../helpers/guessErrorMessage'
import { validateRequiredValues } from '../../../helpers/requiresValues'
import { useCurrentSite, useAppContext } from '../../auth/atoms'
import { DataGridBase } from '../../grid/DataGridBase'
import { TwoLinesCell, CellWrapper } from '../../grid/cells/GridCells'
import { copyToClipboard } from '../../media/copyToClipboard'
import { TagsQueryParamsV2, createTagQuery, deleteTagQuery, updateTagQuery, useTagsQueryV2 } from './queries'
import FileCopyIcon from '@mui/icons-material/FileCopy'
import SearchBar from '@/common/components/SearchBar'
import { TagTypeFilter } from './TagTypeFilter'
import { useStateWithStorage } from '@/common/storage.service'
import Button from '@mui/material/Button'
import { TagForm } from './TagForm'
import CMDialog from '@/common/components/CMDialog'
import { LoadingButton } from '@mui/lab'
import { TagDTO } from './types'

function getLatestDocumentWithTagURL(tagId: string, primaryDomain: string, fullUrl = false) {
    if (!tagId || !primaryDomain) return null
    const link = `/sys/v1/tagged/${tagId}/latest`

    if (fullUrl) {
        return new URL(link, 'https://' + primaryDomain)
    }

    return link
}

export function Tags() {
    const defaultQuery = {
        ...defaultPageQuery,
        Search: '',
        Types: []
    }
    const cacheKey = 'v3-' + window.location.pathname
    const currentSite = useCurrentSite()
    const evaluators = useAppContext()

    const [queryParams, setQueryParams] = useStateWithStorage<TagsQueryParamsV2>('tags-query-params', defaultQuery)

    const { data, refetch, isLoading } = useTagsQueryV2(queryParams)

    const createTagMutation = useMutation({
        mutationFn: createTagQuery,
        onSuccess: (data) => {
            notify(`Success! ${localCreateState.Name} has been created`, 'info')
            refetch()
        },
        onError: (err: any) => {
            notify('Oops! Tag could not be created', 'error')
            const errorMessage = guessErrorMessage(err)
            console.log('errorMsg', errorMessage)
        }
    })

    const editTagMutation = useMutation({
        mutationFn: updateTagQuery,
        onSuccess: (data) => {
            notify('Success! Tag has been updated', 'info')
            refetch()
        },
        onError: (err: any) => {
            notify('Oops! Tag could not be updated', 'error')
            const errorMessage = guessErrorMessage(err)
            console.log('errorMsg', errorMessage)
        }
    })

    const deleteTagMutation = useMutation({
        mutationFn: deleteTagQuery,
        onSuccess: (data) => {
            notify('Success! Tag has been deleted', 'info')
            refetch()
        },
        onError: (err: any) => {
            notify('Oops! Tag could not be deleted', 'error')
            const errorMessage = guessErrorMessage(err)
            console.log('errorMsg', errorMessage)
        }
    })

    const [localCreateState, setLocalCreateState] = useState<TagDTO>({
        Name: '',
        Types: []
    })
    const [errors, setErrors] = useState({
        Name: '',
        Types: ''
    })

    const [createDialogIsOpen, setCreateDialogIsOpen] = useState(false)
    const handleClose = () => setCreateDialogIsOpen(false)

    const handleCloseAdapter = () => {
        handleClose()
        setLocalCreateState({ Name: '', Types: [] })
        setErrors({ Name: '', Types: '' })
    }

    const createTagOnChangeHandler = useCallback((tagDto: TagDTO) => {
        setLocalCreateState(tagDto)
        setErrors((prev) => ({ ...prev, Name: '' }))
    }, [])

    const handleCreate = () => {
        createTagMutation.mutate(localCreateState)
        // axios.post(TagAPI, { ...localCreateState }, { params: { siteId: currentSiteID } }).then(() => refetch())
        setLocalCreateState({ Name: '', Types: [] })
        handleClose()
    }

    useEffect(() => {
        setErrors({
            Name: '',
            Types: ''
        })
    }, [createDialogIsOpen])

    const columns: GridColDef[] = [
        {
            field: 'Name',
            headerName: 'Name',
            flex: 2,
            disableColumnMenu: true,
            sortable: true,
            renderCell: (params) => <TwoLinesCell l1={`${params.row.Name}`} l2={params.row.ID} />
        },
        {
            field: 'Types',
            headerName: 'Type',
            // width: 250,
            flex: 1,
            align: 'left',

            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderCell: (params) => (
                <TwoLinesCell l1={`${params.row.Types.map((t) => capitalizeFirstLetter(t)).join(', ')}`} l2={''} />
            )
        },
        {
            field: 'link',
            headerName: 'Link',
            width: 180,
            sortable: false,
            filterable: false,
            disableColumnMenu: true,
            renderHeader: () => undefined,
            renderCell: (params) =>
                params.row.Types.includes('media') ? (
                    <SplitButton
                        sx={{
                            color: '#757575'
                        }}
                        button={{
                            label: 'Latest',
                            buttonProps: {
                                onClick: (e) => {
                                    const url = getLatestDocumentWithTagURL(
                                        params.row.ID,
                                        currentSite?.PrimaryDomain || ''
                                    )
                                    if (url) {
                                        copyToClipboard(
                                            url.toString(),
                                            `Copied link to latest document with tag: "${
                                                params.row.Name
                                            }". ${url.toString()}`
                                        )
                                    }
                                },
                                startIcon: <FileCopyIcon />
                            }
                        }}
                        options={[
                            {
                                label: 'Share Latest',
                                menuItemProps: {
                                    onClick: () => {
                                        const url = getLatestDocumentWithTagURL(
                                            params.row.ID,
                                            currentSite?.PrimaryDomain || '',
                                            true
                                        )

                                        if (url) {
                                            copyToClipboard(
                                                url.toString(),
                                                `Copied link to latest document with tag: "${
                                                    params.row.Name
                                                }". ${url.toString()}`
                                            )
                                        }
                                    }
                                }
                            }
                        ]}
                    />
                ) : undefined
        },
        { field: 'UpdatedAt', headerName: 'Updated', width: 200, sortable: true },

        {
            field: 'Menu',
            headerName: 'Actions',
            headerAlign: 'left',
            filterable: false,
            hideable: false,
            width: 80,
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params) => {
                const [actionMenuAnchor, setActionMenuAnchor] = useState<HTMLElement | null>(null)
                const [confirmDeleteDialogOpen, setConfirmDeleteDialogOpen] = useState(false)

                const [dialogOpenEdit, setDialogOpenEdit] = useState(false)
                const [editTagForm, setEditTagForm] = useState(params.row)
                const [editErrors, setEditErrors] = useState({
                    // currently can only edit name of tag
                    Name: '',
                    Types: ''
                })

                const handleEditChange = (tagDto: TagDTO) => {
                    setEditTagForm({
                        ...editTagForm,
                        ...tagDto
                    })
                    setEditErrors((prev) => ({ ...prev, Name: '', Types: '' }))
                }

                const handleDialogDelete = (tag) => {
                    deleteTagMutation.mutate(tag.ID)
                    setConfirmDeleteDialogOpen(false)
                }

                const handleEdit = () => {
                    editTagMutation.mutate({
                        id: editTagForm.ID,
                        tag: editTagForm
                    })
                    setDialogOpenEdit(false)
                }

                return (
                    <CellWrapper style={{ textAlign: 'left' }}>
                        <IconButton
                            size={'small'}
                            aria-haspopup={'true'}
                            onClick={(e) => {
                                setActionMenuAnchor(e.currentTarget)
                            }}
                        >
                            <MoreHoriz />
                        </IconButton>
                        <CustomMenu
                            key={params.row.id}
                            anchorElement={actionMenuAnchor}
                            onClose={() => setActionMenuAnchor(null)}
                        >
                            <CustomMenuItem
                                text={'Edit'}
                                disabled={!evaluators.action(params?.row, 'update')}
                                onClick={() => {
                                    setActionMenuAnchor(null)
                                    setDialogOpenEdit(true)
                                }}
                            >
                                <CreateIcon />
                            </CustomMenuItem>
                            <CustomMenuItem
                                disabled={!evaluators.action(params?.row, 'delete')}
                                text={'Delete'}
                                onClick={() => {
                                    setConfirmDeleteDialogOpen(true)
                                    setActionMenuAnchor(null)
                                }}
                            >
                                <DeleteIcon />
                            </CustomMenuItem>
                        </CustomMenu>

                        <CMDialog
                            maxWidth='lg'
                            fullWidth
                            title='Edit Tag'
                            open={dialogOpenEdit}
                            onClose={() => setDialogOpenEdit(false)}
                            showCloseButton
                        >
                            <DialogContent>
                                <TagForm
                                    value={editTagForm}
                                    onChange={handleEditChange}
                                    errors={editErrors}
                                    disableTypeField
                                />
                            </DialogContent>
                            <DialogActions>
                                <Stack direction='row' gap='25px'>
                                    <Button onClick={() => setEditTagForm(params.row)}>Reset Changes</Button>
                                    <LoadingButton
                                        disabled={editTagForm.Name == params.row.Name}
                                        loading={editTagMutation.isLoading}
                                        variant='contained'
                                        onClick={() => {
                                            const { error, errors } = validateRequiredValues(editTagForm)
                                            if (error) {
                                                return setEditErrors((p) => ({ ...p, ...errors }))
                                            }
                                            return handleEdit()
                                        }}
                                    >
                                        Save
                                    </LoadingButton>
                                </Stack>
                            </DialogActions>
                        </CMDialog>

                        {/* Delete */}
                        <ConfirmAction
                            open={confirmDeleteDialogOpen}
                            title={`Delete "${params?.row.name}"`}
                            text={'Are you sure you want to delete this tag?'}
                            handleClose={() => setConfirmDeleteDialogOpen(false)}
                            handleDisagree={() => setConfirmDeleteDialogOpen(false)}
                            handleAgree={() => handleDialogDelete(params.row)}
                        />
                    </CellWrapper>
                )
            }
        }
    ]

    return (
        <PageContainerWithHeader
            title='Tags'
            // titleSlot={<WhatsNew link={'/vanity-url?hash=doL3Zhbml0eS11cmw='} />}
            topRightElement={
                evaluators.actionForEntityScope('cm.tag', 'create') ? (
                    <AddButton title={'ADD TAG'} func={() => setCreateDialogIsOpen(true)} />
                ) : null
            }
        >
            <Grid container spacing={2}>
                <Grid item md={6}>
                    <SearchBar
                        value={queryParams.Search || ''}
                        onChange={(val) => setQueryParams((p) => ({ ...p, Search: val, page: 1 }))}
                    />
                </Grid>
                <Grid item md={3}>
                    <TagTypeFilter
                        value={queryParams.Types || []}
                        onChange={(v) => setQueryParams((p) => ({ ...p, Types: v, page: 1 }))}
                    />
                </Grid>
                <Grid item md={3}>
                    <Button
                        sx={{ marginTop: 'auto', marginLeft: 'auto' }}
                        onClick={() => {
                            setQueryParams(defaultQuery)
                        }}
                    >
                        Reset Filters
                    </Button>
                </Grid>
                <Grid item xs={12}>
                    {data && (
                        <DataGridBase
                            loading={isLoading}
                            disableColumnFilter
                            disableDensitySelector
                            disableColumnSelector
                            columns={columns}
                            state={data}
                            setQuery={setQueryParams}
                        />
                    )}
                </Grid>
            </Grid>

            <CMDialog
                maxWidth='lg'
                fullWidth
                title='Create a Tag'
                open={createDialogIsOpen}
                onClose={() => handleCloseAdapter()}
                showCloseButton
            >
                <DialogContent>
                    <TagForm value={localCreateState} onChange={createTagOnChangeHandler} errors={errors} />
                </DialogContent>
                <DialogActions>
                    <Stack direction='row' gap='25px'>
                        <Button>Cancel</Button>
                        <LoadingButton
                            loading={createTagMutation.isLoading}
                            variant='contained'
                            onClick={() => {
                                const { error, errors } = validateRequiredValues(localCreateState)
                                if (error) {
                                    return setErrors((p) => ({ ...p, ...errors }))
                                }
                                return handleCreate()
                            }}
                        >
                            Save
                        </LoadingButton>
                    </Stack>
                </DialogActions>
            </CMDialog>
        </PageContainerWithHeader>
    )
}
