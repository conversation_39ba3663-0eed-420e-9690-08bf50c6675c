import { Autocomplete, Checkbox, FormControl, TextField, Typography } from '@mui/material'
import { TagType, getTagTypes } from './types'

interface TagTypeFilterProps {
    value: string[]
    onChange: (v: string[]) => void
    label?: string
    variant?: 'standard' | 'outlined' | 'filled'
    required?: boolean
    disabled?: boolean
    availableTypes?: TagType[]
}

export function TagTypeFilter({
    value,
    onChange,
    label = 'Type',
    variant,
    required,
    disabled,
    availableTypes
}: TagTypeFilterProps) {
    const tagTypes = getTagTypes()

    if (!tagTypes) {
        return <></>
    }

    return (
        <FormControl variant={variant || 'outlined'} required={required} sx={{ width: '100%' }} disabled={disabled}>
            <Autocomplete
                disabled={disabled || !tagTypes?.length}
                sx={{
                    '&.MuiAutocomplete-root .MuiFormControl-root .MuiInputBase-root': {
                        flexWrap: 'nowrap'
                    }
                }}
                aria-required={required}
                multiple
                disableCloseOnSelect
                disablePortal
                options={availableTypes || tagTypes}
                value={value}
                onChange={(ev, newValue) => {
                    onChange(newValue)
                }}
                getOptionLabel={(option) => {
                    return option
                }}
                renderInput={(params) => (
                    <TextField
                        {...params}
                        label={label || 'Type'}
                        variant={variant || 'outlined'}
                        required={required}
                    />
                )}
                renderOption={(props, option, { selected }) => {
                    const { ...optionProps } = props
                    return (
                        <li key={option} {...optionProps}>
                            <Checkbox style={{ marginRight: 8 }} checked={selected} size='small' />
                            <Typography textTransform='capitalize'>{option}</Typography>
                        </li>
                    )
                }}
            />
        </FormControl>
    )
}

export default TagTypeFilter
