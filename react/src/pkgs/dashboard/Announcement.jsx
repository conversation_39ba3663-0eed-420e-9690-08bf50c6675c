import React, { useState } from 'react'
import { ArrowForward, CheckCircleOutline, Event } from '@mui/icons-material'
import { renderNextThirdThursday } from '../../helpers/date'
import { colours } from '../../common/colours'
import './Announcement.css'
import { Divider } from '@mui/material'
import { Avatar, Box, Card, ListItem, ListItemAvatar, Typography } from '@mui/material'

export function DashboardAnnouncement({ additionalContent }) {
    const [hasNavigatedToSurvey, setHasNavigatedToSurvey] = useState(false)
    const iconProps = {
        style: { fontSize: '1.7rem', paddingLeft: '30px', color: 'white' }
    }

    return (
        <div
            className='col-md-12 col-lg-5 col-lg-offset-2 first-xs md-size'
            style={{ marginBottom: '1rem', fontSize: '1.3em' }}
        >
            <div
                className='LegacyMuiCard-Wrapper MuiPaper-root MuiCard-root jss14 MuiPaper-elevation1 MuiPaper-rounded'
                style={{ backgroundColor: colours.topbar }}
            >
                <div>
                    <ListItem>
                        <ListItemAvatar>
                            <Event
                                style={{
                                    fontSize: '2rem',
                                    color: 'white'
                                }}
                            />
                        </ListItemAvatar>
                        <Typography variant='h6' color='white'>
                            Zoom Community Drop-in Training Sessions
                        </Typography>
                    </ListItem>
                    <Box sx={{ marginLeft: '70px', display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                        <Typography style={{ color: colours.white, marginTop: '10px', fontSize: '1rem' }}>
                            Join us for a focused training session every 3rd Thursday!
                        </Typography>
                        <Box color='white' display='inline'>
                            <Typography color='white' display='inline'>
                                The next training session is on: &nbsp;
                            </Typography>
                            <Typography fontWeight={'bold'}>
                                {renderNextThirdThursday()} at 10 am - 10:30 am MST
                            </Typography>
                        </Box>
                        <Typography variant='subtitle1'>
                            <a
                                target='_blank'
                                href='https://us02web.zoom.us/j/81785623746'
                                style={{ color: colours.orange, textDecoration: 'none' }}
                            >
                                Click here to join the meeting
                            </a>
                        </Typography>
                        <Typography variant='subtitle1'>
                            <a
                                target='_blank'
                                href='https://cdn.imagineeverything.com/incase-you-missed-it'
                                style={{ color: colours.orange, textDecoration: 'none' }}
                            >
                                In case you missed it...
                            </a>
                        </Typography>
                    </Box>
                </div>
            </div>
            {additionalContent && (
                <div className=' MuiPaper-root MuiCard-root jss14 MuiPaper-elevation1 MuiPaper-rounded'>
                    {additionalContent}
                </div>
            )}
            <div
                className='LegacyMuiCard-Wrapper MuiPaper-root MuiCard-root jss14 MuiPaper-elevation1 MuiPaper-rounded'
                style={{
                    backgroundColor: hasNavigatedToSurvey ? colours.published : colours.orange,
                    paddingTop: '10px',
                    paddingBottom: '10px',
                    marginBottom: '1.5rem',
                    cursor: 'pointer'
                }}
                onClick={() => {
                    window.open('https://forms.gle/fA7mNATq8X2WzQQK7', '_blank')
                    // setHasNavigatedToSurvey(true)
                }}
            >
                <li className='LegacyMuiListItem-Root LegacyMuiListItem-Gutters'>
                    <div className='flex-row-align-center'>
                        <h3 className='typography no-margin' style={{ color: colours.white, fontSize: '1.2rem' }}>
                            {hasNavigatedToSurvey
                                ? 'Thank you for filling out our Community Survey'
                                : 'Fill out our Content Manager Community Survey'}
                        </h3>
                        {hasNavigatedToSurvey ? <CheckCircleOutline {...iconProps} /> : <ArrowForward {...iconProps} />}
                    </div>
                </li>
            </div>
        </div>
    )
}
