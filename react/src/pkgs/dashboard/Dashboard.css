.dashboard-wrapper {
    background-image: url('../../assets/ui-background-dashboard.svg');
    background-repeat: no-repeat;
    background-position: 90%;
    background-size: 40%;
    background-attachment: fixed;
    min-height: 75vh;
    padding-top: 2rem;
}

.typography {
    font-size: 1rem;
    font-family: '<PERSON><PERSON>', 'Helvetica', 'Arial', sans-serif;
    font-weight: 400;
    line-height: 1.43;
    letter-spacing: 0.01071em;
}

.subtitle {
    color: rgba(0, 0, 0, 0.54);
}

h3.typography {
    font-size: 1.2rem;
}

@media only screen and (max-width: 1200px) {
    .dashboard-wrapper {
        background-size: 60%;
        background-position: center;
    }
}

@media only screen and (max-width: 1024px) {
    .md-size {
        width: 100%;
        margin: 10px;
    }
}
