import CMTextField, { CMTextFieldProps } from '@/common/components/CMTextField'
import { Chip, InputAdornment } from '@mui/material'

type DocumentFilenameTextFieldProps = {
    value: string
    onChange: (v: string) => void
} & CMTextFieldProps

export function DocumentFilenameTextField({ value, onChange, ...textFieldProps }: DocumentFilenameTextFieldProps) {
    const extension = value?.split('.').pop()

    return (
        <CMTextField
            {...textFieldProps}
            value={value?.replace(`.${extension}`, '')}
            onChange={(e) => {
                onChange?.(`${e.target.value}.${extension}`)
            }}
            InputProps={{
                endAdornment: (
                    <InputAdornment position={'end'}>
                        <Chip label={`.${extension}`} />
                    </InputAdornment>
                )
            }}
        />
    )
}
