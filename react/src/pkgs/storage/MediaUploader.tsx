import CMDialog from '@/common/components/CMDialog'
import { S3Uploader, S3UploaderProps } from './S3Uploader'
import { ReactNode } from 'react'

interface StorageUploaderProps extends S3UploaderProps {
    title?: ReactNode
    isOpen: boolean
}

export default function StorageUploader({ isOpen, title, ...S3UploaderProps }: StorageUploaderProps) {
    return (
        <CMDialog open={isOpen} title={title}>
            <S3Uploader {...S3UploaderProps} />
        </CMDialog>
    )
}
