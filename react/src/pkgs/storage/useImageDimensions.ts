import { useEffect, useState } from 'react'

export function useImageDimensions(url: string | undefined) {
    const [isLoading, setIsLoading] = useState(true)
    const [dimensions, setDimensinos] = useState<{ width: number; height: number } | null>(null)

    useEffect(() => {
        if (!url) {
            return
        }

        var i = new Image()

        i.onload = function () {
            setDimensinos({
                width: i.width,
                height: i.height
            })
            setIsLoading(false)
        }

        i.src = url
    }, [url])

    return { dimensions, isLoading }
}
