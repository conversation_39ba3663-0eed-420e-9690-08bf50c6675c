import CMDialog from '@/common/components/CMDialog'
import CustomIconButton from '@/common/components/CustomIconButton'
import { forwardRef, useCallback, useState } from 'react'
import FullscreenIcon from '@mui/icons-material/Fullscreen'

interface FullScreenPreviewProps {
    preview: string | undefined
    title?: string
}

export default function useFullScreenPreview({ preview, title = 'Preview' }: FullScreenPreviewProps) {
    const [fullScreenPreviewIsOpen, setFullScreenPreviewIsOpen] = useState(false)

    const OpenFullScreenPreviewButton = useCallback(() => {
        if (!preview) return <></>

        return (
            <CustomIconButton
                sx={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px'
                }}
                onClick={(e) => {
                    e.stopPropagation()
                    setFullScreenPreviewIsOpen(true)
                }}
            >
                <FullscreenIcon />
            </CustomIconButton>
        )
    }, [])
    const Preview = useCallback(() => {
        if (!preview) return <></>

        return (
            <CMDialog
                fullWidth
                maxWidth='xl'
                open={fullScreenPreviewIsOpen}
                title={title}
                showCloseButton
                onClick={(e) => {
                    e.stopPropagation()
                    setFullScreenPreviewIsOpen(false)
                }}
                onClose={(e) => {
                    setFullScreenPreviewIsOpen(false)
                }}
            >
                <div
                    style={{
                        zIndex: 50000,
                        height: '80vh',
                        cursor: 'pointer',
                        width: '100%',
                        backgroundImage: `${preview}`,
                        backgroundSize: 'contain',
                        backgroundRepeat: 'no-repeat',
                        backgroundPosition: 'center',
                        backgroundColor: '#f5f5f5'
                    }}
                ></div>
            </CMDialog>
        )
    }, [fullScreenPreviewIsOpen, preview, title])
    return {
        Preview,
        OpenFullScreenPreviewButton,
        fullScreenPreviewIsOpen,
        setFullScreenPreviewIsOpen: (isOpen: boolean) => setFullScreenPreviewIsOpen(isOpen)
    }
}
