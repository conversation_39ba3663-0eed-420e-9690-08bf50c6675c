import {
    <PERSON><PERSON>,
    <PERSON>,
    But<PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    DialogContent,
    Divider,
    FormControl,
    FormHelperText,
    Grid,
    InputAdornment,
    Tab,
    Typography
} from '@mui/material'
import { ImageLoader, ImageLoaderHandlers, MediaObject } from '../media/upload/ImageLoader'
import { useCallback, useEffect, useRef, useState } from 'react'
import CMDialog from '@/common/components/CMDialog'
import { MEDIA_IMAGES, MEDIA_THUMBNAILS } from '@/common/constants'
import { TagsSelector } from '../system/tags/TagsSelector'
import { TagType } from '../system/tags/types'
import { EntityScopeEnum } from '../auth/entityScope'
import { useAppContext } from '../auth/atoms'
import { BoxForm } from '@/common/components/BoxForm'
import { useMutation } from '@tanstack/react-query'
import { z } from 'zod'
import { notify } from '@/helpers'
import { LoadingButton, Tab<PERSON>ontext, <PERSON><PERSON><PERSON><PERSON>, <PERSON>b<PERSON><PERSON><PERSON> } from '@mui/lab'
import { copyToClipboard } from '../media/copyToClipboard'
import CMTextField from '@/common/components/CMTextField'
import FileCopyIcon from '@mui/icons-material/FileCopy'
import DownloadIcon from '@mui/icons-material/Download'
import { ResourceLocationButton } from '@/pkgs/storage/ResourceLocationButton'
import { useImageDimensions } from './useImageDimensions'
import { ImageCropList } from './image_crop/ImageCropList'
import { deleteImageQuery, Image, ImageDTO, imageDto, updateImageQuery, useImageQuery } from './queries'
import { colours } from '@/common/colours'
import { SitesSelectorComponent } from '@/common/components/selectors/SitesSelectorComponent'

enum ImageEditorModalTab {
    EDITOR = 'EDITOR',
    CROPS = 'CROPS'
}

interface ImageEditorProps {
    imageId: string
    isOpen: boolean
    onClose: () => void
}

export default function ImageEditor({ imageId, isOpen, onClose }: ImageEditorProps) {
    const imageLoaderRef = useRef<ImageLoaderHandlers | null>(null)
    const appContext = useAppContext()

    const [tab, setTab] = useState<ImageEditorModalTab>(ImageEditorModalTab.EDITOR)
    const { data: image, isLoading: getImageIsLoading, isError, refetch: refetchImage } = useImageQuery(imageId)

    const { dimensions, isLoading: dimensionsIsLoading } = useImageDimensions(
        image ? `${MEDIA_IMAGES}/${image?.id}?updated=${image?.updated.toISOString()}` : undefined
    )

    const [formData, setFormData] = useState<Image | undefined>(image)
    const [formDataErrors, setFormDataErrors] = useState<Partial<Image>>()

    const updateImageMutation = useMutation({
        mutationFn: updateImageQuery
    })

    const deleteImageMutation = useMutation({
        mutationFn: deleteImageQuery
    })

    const isLoading = updateImageMutation.isLoading || deleteImageMutation.isLoading || getImageIsLoading
    const hasError = updateImageMutation.isError || deleteImageMutation.isError

    // replace image
    const [newImage, setNewImage] = useState<MediaObject | null>(null)

    const [replaceCropDialogIsOpen, setReplaceCropDialogIsOpen] = useState(false)
    const extension = formData?.filename.split('.').pop()

    const preview = newImage
        ? `url(${newImage.thumbnail})`
        : image
          ? `url(${MEDIA_THUMBNAILS}/${image?.id}?updated=${image?.updated.toISOString()})`
          : undefined

    useEffect(() => {
        setFormData(image)
    }, [image])

    useEffect(() => {
        try {
            imageDto.parse(formData)
            setFormDataErrors({})
        } catch (e) {
            const zError = e as z.ZodError
            zError.errors.forEach((err) => {
                setFormDataErrors((prev) => ({ ...prev, [err.path[0]]: err.message }))
            })
        }
    }, [formData])

    const updateImage = async (imageDto: ImageDTO) => {
        updateImageMutation.mutate(imageDto, {
            onSuccess: (res) => {
                notify(`Image ${imageDto.filename} updated`, 'info')
                if (newImage) {
                    setNewImage(null)
                    setReplaceCropDialogIsOpen(true)
                }
                refetchImage()
            },
            onError: (err) => {
                console.error('update image error', err)
                notify(err, 'error')
            }
        })
    }

    const deleteImage = (id: string) => {
        deleteImageMutation.mutate(id, {
            onSuccess: (res) => {
                notify(`Image ${formData?.filename} deleted`, 'info')
                onClose && onClose()
            },
            onError: (err) => {
                console.error('update image error', err)
                notify(err, 'error')
            }
        })
    }

    const EditorTitle = useCallback(() => {
        return (
            <>
                Image Editor: {formData?.filename}
                <Typography fontStyle='italic'>
                    Adjust attributes such as alt text and filename. Delete or replace image and change where it is
                    shared.
                </Typography>
            </>
        )
    }, [formData])

    const EditorActions = useCallback(() => {
        const viewingCrops = tab == ImageEditorModalTab.CROPS
        return (
            <DialogActions
                sx={{
                    justifyContent: 'space-between',
                    borderTop: `1px solid ${colours.off_white_but_darker}`,
                    padding: '16px 24px'
                }}
            >
                <BoxForm boxRowGap='sm'>
                    <Box className='box-row'>
                        <Button
                            disabled={!appContext.action(formData, 'update') || viewingCrops}
                            variant='outlined'
                            role={undefined}
                            component={'label'}
                            onClick={() => imageLoaderRef?.current?.inputRef?.current?.click?.()}
                        >
                            Replace Image
                        </Button>
                        <Button
                            disabled={viewingCrops}
                            variant='outlined'
                            onClick={() => copyToClipboard(`/images/${formData?.id}`)}
                            startIcon={<FileCopyIcon />}
                        >
                            Copy Link
                        </Button>
                        {image?.id && (
                            <Button
                                disabled={viewingCrops}
                                download
                                href={`${window.location.origin}/images/${image.id}`}
                                variant='outlined'
                                target='_blank'
                                startIcon={<DownloadIcon />}
                            >
                                Download
                            </Button>
                        )}
                        <Button
                            disabled={isLoading || !appContext.action(formData, 'delete') || viewingCrops}
                            style={{ marginRight: 'auto' }}
                            variant='outlined'
                            color='error'
                            onClick={() => {
                                if (!formData) return

                                if (window.confirm('Are you sure you want to delete this image?')) {
                                    deleteImage(formData.id)
                                }
                            }}
                        >
                            Delete Image
                        </Button>
                    </Box>
                </BoxForm>
                <BoxForm>
                    <Box className='box-row'>
                        {hasError && (
                            <FormHelperText error={true}>Something went wrong. Please try again.</FormHelperText>
                        )}
                        {newImage && <Button onClick={() => setNewImage(null)}>Cancel</Button>}
                        <LoadingButton
                            loading={isLoading || updateImageMutation.isLoading}
                            disabled={
                                isLoading ||
                                Object.keys(formDataErrors || {})?.length > 0 ||
                                !appContext.action(formData, 'update') ||
                                viewingCrops
                            }
                            variant='contained'
                            onClick={() => {
                                const data = imageDto.parse(formData)
                                if (newImage) {
                                    data.data = newImage.data
                                    data.thumbnail = newImage.thumbnail
                                }
                                updateImage(data)
                            }}
                            color={newImage ? 'warning' : undefined}
                        >
                            {newImage ? 'Replace' : 'Save'}
                        </LoadingButton>
                    </Box>
                </BoxForm>
            </DialogActions>
        )
    }, [appContext, formData, updateImage, deleteImage, imageLoaderRef, isLoading, tab])

    return (
        <CMDialog fullWidth maxWidth='xl' open={isOpen} title={<EditorTitle />} showCloseButton onClose={onClose}>
            <DialogContent sx={{ height: '600px' }}>
                <TabContext value={tab}>
                    <TabList onChange={(e, newValue) => setTab(newValue)}>
                        <Tab label={ImageEditorModalTab.EDITOR} value={ImageEditorModalTab.EDITOR} />
                        <Tab label={ImageEditorModalTab.CROPS} value={ImageEditorModalTab.CROPS} />
                    </TabList>
                    <TabPanel value={ImageEditorModalTab.EDITOR}>
                        <Grid container>
                            <Grid item md={7} xs={12} sx={{ paddingX: '24px' }}>
                                <ImageLoader
                                    ref={imageLoaderRef}
                                    preview={preview}
                                    onChange={(media) => {
                                        setNewImage(media)
                                    }}
                                    previewHeight={450}
                                    maxFiles={1}
                                />
                            </Grid>
                            <Grid item md={5} xs={12} sx={{ paddingX: '24px' }}>
                                <BoxForm>
                                    <CMTextField
                                        margin={'dense'}
                                        error={!!formDataErrors?.alt}
                                        label='Alt'
                                        value={formData?.alt || ''}
                                        onChange={(e) =>
                                            setFormData((prev) => prev && { ...prev, alt: e.target.value })
                                        }
                                        fullWidth
                                        errorText={formDataErrors?.alt}
                                    />
                                    <CMTextField
                                        error={!!formDataErrors?.filename}
                                        margin={'dense'}
                                        label='Filename'
                                        value={formData?.filename.split('.').shift() || ''}
                                        onChange={(e) =>
                                            setFormData(
                                                (prev) =>
                                                    prev && { ...prev, filename: `${e.target.value}.${extension}` }
                                            )
                                        }
                                        fullWidth
                                        InputProps={{
                                            endAdornment: (
                                                <InputAdornment position={'end'}>
                                                    <Chip label={`.${extension}`} />
                                                </InputAdornment>
                                            )
                                        }}
                                    />

                                    <FormControl fullWidth>
                                        <TagsSelector
                                            selected={formData?.tags || []}
                                            disabled={false}
                                            tagTypes={[TagType.Image]}
                                            onChange={(tags) => setFormData((prev) => prev && { ...prev, tags: tags })}
                                        />
                                    </FormControl>
                                    <FormControl fullWidth>
                                        <SitesSelectorComponent
                                            value={
                                                (formData?.sites && {
                                                    Sites: formData?.sites,
                                                    DepartmentID: formData?.department_id
                                                }) ||
                                                undefined
                                            }
                                            onChange={(v) => {
                                                setFormData(
                                                    (prev) =>
                                                        prev && {
                                                            ...prev,
                                                            sites: v.Sites,
                                                            department_id: v.DepartmentID
                                                        }
                                                )
                                            }}
                                            contentType={EntityScopeEnum.Image}
                                            error={formDataErrors?.sites?.join(', ')}
                                        />
                                    </FormControl>
                                    <Divider />
                                    {image && (
                                        <ResourceLocationButton
                                            resources={[
                                                `${image.type}/${image.id}`,
                                                `${image.type}/${image.filename}`,
                                                `${image.type}/${encodeURIComponent(image.filename)}`
                                            ]}
                                        />
                                    )}
                                    <Box>
                                        <FormHelperText>
                                            Last updated: {new Date(image?.updated || '').toLocaleString()}
                                        </FormHelperText>
                                        <FormHelperText>Id: {image?.id}</FormHelperText>
                                        {dimensions && (
                                            <FormHelperText>
                                                Dimensions: {dimensions.width}x{dimensions.height}px
                                            </FormHelperText>
                                        )}
                                    </Box>
                                </BoxForm>
                            </Grid>
                        </Grid>
                    </TabPanel>
                    <TabPanel value={ImageEditorModalTab.CROPS}>
                        <ImageCropList imageId={imageId} />
                    </TabPanel>
                </TabContext>
            </DialogContent>
            <EditorActions />
            {replaceCropDialogIsOpen && !!image?.image_crop_size_ids?.length && (
                <CMDialog
                    title={<Alert severity='warning'>Warning</Alert>}
                    open={replaceCropDialogIsOpen && !!image?.image_crop_size_ids?.length}
                >
                    <DialogContent sx={{ marginTop: '1.2rem' }}>
                        <Typography>
                            {image?.image_crop_size_ids?.length}{' '}
                            {image?.image_crop_size_ids?.length == 1 ? 'crop' : 'crops'} exist for this image. Click the
                            button below to update the crops.
                        </Typography>
                    </DialogContent>
                    <DialogActions>
                        <Button
                            variant='contained'
                            onClick={() => {
                                setTab(ImageEditorModalTab.CROPS)
                                setReplaceCropDialogIsOpen(false)
                            }}
                        >
                            Update Crops
                        </Button>
                    </DialogActions>
                </CMDialog>
            )}
        </CMDialog>
    )
}
