import { Alert, DialogContent, Stack, Typography } from '@mui/material'
import { useResourceLocationQuery } from './queries'
import CMDialog from '@/common/components/CMDialog'
import { useState } from 'react'
import { LoadingButton } from '@mui/lab'
import { ResourceLocationList } from '@/pkgs/storage/ResourceLocationList'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'

interface ResourceLocationButtonProps {
    resources: string[]
    dialogTitle?: string
}

export function ResourceLocationButton({ resources, dialogTitle }: ResourceLocationButtonProps) {
    const { data: refs, isLoading, refetch, error, isError } = useResourceLocationQuery({ resources })
    const [locationsInUseDialogIsOpen, setLocationsInUseDialogIsOpen] = useState(false)

    return (
        <>
            <LoadingButton
                loading={isLoading}
                disabled={!refs?.length}
                variant='outlined'
                sx={{ justifyContent: 'flex-start', textTransform: 'none' }}
                onClick={() => {
                    if (isError) {
                        refetch()
                    } else {
                        setLocationsInUseDialogIsOpen(true)
                    }
                }}
            >
                <Stack direction='column'>
                    {isError ? (
                        <Alert severity='error'>{guessErrorMessage(error)}</Alert>
                    ) : (
                        <>
                            <Typography variant='subtitle1' textAlign='start'>
                                Locations In Use ({refs?.length})
                            </Typography>
                            <Typography>See where this resource is being used</Typography>
                        </>
                    )}
                </Stack>
            </LoadingButton>
            <CMDialog
                maxWidth={'lg'}
                fullWidth
                showCloseButton
                open={locationsInUseDialogIsOpen}
                title={dialogTitle || 'Resource Location'}
                onClose={() => setLocationsInUseDialogIsOpen(false)}
            >
                <DialogContent sx={{ paddingX: 0 }}>
                    <ResourceLocationList refs={refs} />
                </DialogContent>
            </CMDialog>
        </>
    )
}
