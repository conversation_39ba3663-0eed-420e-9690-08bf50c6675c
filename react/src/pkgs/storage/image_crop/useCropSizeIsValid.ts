import { ImageCropSize } from '@/pkgs/system/image-crop-size/types'
import { useImageQuery } from '../queries'
import { getImagePreviewUrl } from '../getImagePreviewUrl'
import { useImageDimensions } from '../useImageDimensions'

export function cropSizeIsWithinDimensions(imageWidth: number, imageHeight: number, cropSize: ImageCropSize) {
    return cropSize.Width <= imageWidth && cropSize.Height <= imageHeight
}

export function useCropSizeIsValid(imageId: string, size: ImageCropSize | undefined | null) {
    const imagePreviewUrl = getImagePreviewUrl(imageId)
    const { dimensions } = useImageDimensions(imagePreviewUrl)

    if (!dimensions || !size) return true

    return cropSizeIsWithinDimensions(dimensions?.width, dimensions?.height, size)
}
