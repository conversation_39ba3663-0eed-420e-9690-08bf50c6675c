import { useRef } from 'react'
import 'cropperjs/dist/cropper.css'
import { ImageCropSize } from '@/pkgs/system/image-crop-size/types'
import { alpha, Box, Typography } from '@mui/material'
import { colours } from '@/common/colours'

export interface CropPreviewProps {
    cropSize: ImageCropSize
}

// Used as a standalone crop preview given original image source and crop size.
// if no src, show "Click to create" preview box
export const PreviewCropSize = ({ cropSize }: CropPreviewProps) => {
    const testRef = useRef<HTMLDivElement | null>(null)

    return (
        <Box
            ref={testRef}
            sx={{
                backgroundColor: alpha(colours.base_blue, 0.25),
                aspectRatio: cropSize.Width / cropSize.Height,
                objectFit: 'contain',
                maxWidth: '100%',
                width: cropSize.Width / 1.5,
                height: 'auto',
                borderRadius: '8px',
                justifyContent: 'center',
                textAlign: 'center',
                alignItems: 'center',
                display: 'flex'
            }}
        >
            <Typography sx={{ color: colours.base_blue, fontStyle: 'italic' }}>Click to Create</Typography>
        </Box>
    )
}
