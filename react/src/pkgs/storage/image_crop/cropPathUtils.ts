import { MEDIA_IMAGES } from '@/common/constants'

// imageId: original image id
// cropName: name of crop size (id string can also be used)
// returns string: '<image id>/crop=<crop name>
// used by API
export function getCropId(imageId: string, cropName: string) {
    if (!imageId || !cropName) return ''

    return `${imageId}?crop=${cropName}`
}

// returns crop size from "crop id" (look above)
export function getCropSizeFromCropId(cropId: string) {
    const urlParams = new URLSearchParams(cropId.split('?')[1])

    return urlParams.get('crop')
}

// returns '/images/<image id>?crop=<crop name>&updated=<now>
export function getCropPath(imageId: string, cropName: string, time?: Date) {
    if (!imageId || !cropName) return ''

    if (!time) {
        return `${MEDIA_IMAGES}/${imageId}?crop=${cropName}`
    }

    return `${MEDIA_IMAGES}/${imageId}?crop=${cropName}&updated=${time?.toISOString()}`
}
