import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import {
    $createParagraphNode,
    $getSelection,
    $isRangeSelection,
    COMMAND_PRIORITY_EDITOR,
    createCommand,
    LexicalCommand,
    LexicalEditor
} from 'lexical'
import { useEffect } from 'react'
import { _$insertNodeToNearestRoot } from '../helpers/LexicalUtils'
import { $createDocumentLinkNode, DocumentLinkNode } from './DocumentLinkNode'

export const INSERT_DOCUMENT_LINK_COMMAND: LexicalCommand<string> = createCommand('INSERT_DOCUMENT_LINK_COMMAND')

export function insertDocumentLink(activeEditor: LexicalEditor, documentId: string) {
    activeEditor.dispatchCommand(INSERT_DOCUMENT_LINK_COMMAND, documentId)
}

export default function DocumentLinkPlugin(): JSX.Element | null {
    const [editor] = useLexicalComposerContext()

    useEffect(() => {
        if (!editor.hasNodes([DocumentLinkNode])) {
            throw new Error('DocumentLinkPlugin: DocumentLinkNode not registered on editor')
        }

        return editor.registerCommand<string>(
            INSERT_DOCUMENT_LINK_COMMAND,
            (payload) => {
                const documentLinkNode = $createDocumentLinkNode(payload)
                const selection = $getSelection()
                if ($isRangeSelection(selection)) {
                    selection.insertNodes([documentLinkNode])
                }
                return true
            },
            COMMAND_PRIORITY_EDITOR
        )
    }, [editor])

    return null
}
