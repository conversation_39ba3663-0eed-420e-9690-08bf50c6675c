import { Suspense, useCallback, useState } from 'react'
import AssignmentIcon from '@mui/icons-material/Assignment'
import { useQuery } from '@tanstack/react-query'
import { documentService } from '@/pkgs/media/document/document.service'
import { useCurrentSiteID } from '@/pkgs/auth/atoms'
import { copyToClipboard, useGetLinkToDocument } from '@/pkgs/media/copyToClipboard'
import { Button, CircularProgress, DialogActions, DialogContent, Stack, Typography } from '@mui/material'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { LoadingButton } from '@mui/lab'
import CMDialog from '@/common/components/CMDialog'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { $getNodeByKey } from 'lexical'
import { $isDocumentLinkNode } from './DocumentLinkNode'
import DocumentGallery from '@/pkgs/media/document/DocumentGallery'
import { notify } from '@/helpers'
import { colours } from '@/common/colours'

function DocumentLinkNodeComponent({ nodeKey, documentId }: { nodeKey: string; documentId: string }) {
    const [editor] = useLexicalComposerContext()
    const currentSiteID = useCurrentSiteID()
    const [linkDetailsIsOpen, setLinkDetailsIsOpen] = useState(false)
    const [documentGalleryIsOpen, setDocumentGalleryIsOpen] = useState(false)
    const getLinkToDocument = useGetLinkToDocument()

    const { data, isLoading, isError, error } = useQuery({
        queryKey: ['getDocument', documentId],
        queryFn: async () => documentService.getDocumentById(currentSiteID!, documentId)
    })

    const documentUrl = getLinkToDocument({
        doc: data,
        fullUrl: true,
        by: 'name'
    })

    const replaceDocument = useCallback(
        (documentId: string) => {
            editor.update(() => {
                const node = $getNodeByKey(nodeKey)
                if ($isDocumentLinkNode(node)) {
                    node.setDocumentId(documentId)
                    notify('Success! Document replaced.', 'info')
                }
            })
        },
        [editor, nodeKey]
    )

    return (
        <Suspense fallback={<CircularProgress size={10} />}>
            <>
                <LoadingButton
                    loading={isLoading}
                    onClick={() => setLinkDetailsIsOpen(true)}
                    sx={{ textAlign: 'left', color: isError ? colours.error : undefined }}
                >
                    <AssignmentIcon />
                    {isError ? 'ERROR' : data?.title || data?.filename}
                </LoadingButton>

                <CMDialog
                    open={linkDetailsIsOpen}
                    maxWidth='md'
                    showCloseButton
                    onClose={() => setLinkDetailsIsOpen(false)}
                    title={data?.title || data?.filename}
                >
                    <DialogContent>
                        {!!error ? (
                            <>{guessErrorMessage(error)}</>
                        ) : (
                            <Stack direction='column' sx={{ overflow: 'auto', gap: '0.8rem' }}>
                                <Stack direction='row' gap='8px' sx={{ float: 'left' }}>
                                    <Typography variant='subtitle1' display='inline-block'>
                                        Link:
                                    </Typography>
                                    <a href={documentUrl} target='_blank'>
                                        <Typography>{documentUrl}</Typography>
                                    </a>
                                </Stack>
                                <Stack direction='row' gap='8px' sx={{ float: 'left', alignItems: 'center' }}>
                                    <Typography variant='subtitle1'>Document Filename:</Typography>
                                    <Typography>{data?.filename}</Typography>
                                </Stack>
                            </Stack>
                        )}
                    </DialogContent>
                    <DialogActions sx={{ display: 'flex', flexDirection: 'row', justifyContent: 'space-between' }}>
                        <Button disabled={isError} variant='outlined' onClick={() => copyToClipboard(documentUrl)}>
                            Copy Link
                        </Button>
                        <Button disabled={isError} variant='outlined' onClick={() => setDocumentGalleryIsOpen(true)}>
                            Replace Document
                        </Button>
                    </DialogActions>
                </CMDialog>

                <DocumentGallery
                    isGalleryOpen={documentGalleryIsOpen}
                    setGalleryClose={() => setDocumentGalleryIsOpen(false)}
                    isForDct={true}
                    saveForDct={(doc) => {
                        replaceDocument(doc.id)
                        setLinkDetailsIsOpen(false)
                        setDocumentGalleryIsOpen(false)
                    }}
                />
            </>
        </Suspense>
    )
}

export default DocumentLinkNodeComponent
