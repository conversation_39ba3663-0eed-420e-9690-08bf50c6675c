import { FORMAT_ELEMENT_COMMAND, FORMAT_TEXT_COMMAND, LexicalEditor, REDO_COMMAND, UNDO_COMMAND } from 'lexical'
import { ToolbarButton } from '@/pkgs/lexical-editor/toolbar/ToolbarButton'
import { TOGGLE_LINK_COMMAND } from '@/pkgs/lexical-editor/Link/LinkNode'
import { SET_ANCHOR_EDITOR_OPEN_COMMAND } from '@/pkgs/lexical-editor/Anchors/AnchorPlugin'
import React from 'react'
import { LexicalToolbarButtonConfig } from '@/pkgs/lexical-editor/toolbar/LexicalToolbar'
import { TOOLBAR_BUTTON } from '@/pkgs/lexical-editor/toolbar/getDefaultConfig'

type LimitedToolbarButton = Exclude<TOOLBAR_BUTTON, 'format' | 'insert' | 'code'>
export const toolbarButtons: Record<
    LimitedToolbarButton,
    (editor: LexicalEditor, options?: LexicalToolbarButtonConfig) => any
> = {
    undo: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-undo'}
                onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
                aria-label={'Undo'}
                icon={'undo'}
                active={options?.active}
                disabled={options?.disabled}
                spaced
            />
        ),
    redo: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-redo'}
                onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
                aria-label={'Redo'}
                icon={'redo'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    bold: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-bold'}
                onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'bold')}
                aria-label={'Bold'}
                icon={'format_bold'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    italic: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-italic'}
                onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'italic')}
                aria-label={'Italics'}
                icon={'format_italic'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    underline: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-underline'}
                onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'underline')}
                aria-label={'Underline'}
                icon={'format_underlined'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    strikethrough: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-strikethrough'}
                onClick={() => editor.dispatchCommand(FORMAT_TEXT_COMMAND, 'strikethrough')}
                aria-label={'Strikethrough'}
                icon={'strikethrough_s'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    link: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-link'}
                onClick={() => editor.dispatchCommand(TOGGLE_LINK_COMMAND, options?.active ? null : '')}
                aria-label={'Insert Link'}
                icon={'insert_link'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    anchor: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-anchor'}
                onClick={() => editor.dispatchCommand(SET_ANCHOR_EDITOR_OPEN_COMMAND, true)}
                aria-label={'Insert Anchor'}
                icon={'anchor'}
                active={options?.active}
                disabled={options?.disabled}
            />
        ),
    left: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-left'}
                onClick={() => editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'left')}
                aria-label={'Left Align'}
                icon={'format_align_left'}
                disabled={options?.disabled}
            />
        ),
    right: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-right'}
                onClick={() => editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'right')}
                aria-label={'Right Align'}
                icon={'format_align_right'}
                disabled={options?.disabled}
            />
        ),
    center: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-center'}
                onClick={() => editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'center')}
                aria-label={'Center Align'}
                icon={'format_align_center'}
                disabled={options?.disabled}
            />
        ),
    justify: (editor, options) =>
        options?.hide ? null : (
            <ToolbarButton
                key={'toolbar-button-memo-justify'}
                onClick={() => editor.dispatchCommand(FORMAT_ELEMENT_COMMAND, 'justify')}
                aria-label={'Justify Align'}
                icon={'format_align_justify'}
                disabled={options?.disabled}
            />
        )
}
