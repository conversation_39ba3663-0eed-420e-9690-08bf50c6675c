import React, { useCallback, useEffect, useState } from 'react'
import { Box, Button, Dialog, DialogActions, DialogContent, DialogTitle, Typography } from '@mui/material'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { $getNodeByKey, NodeKey } from 'lexical'
import { QueryNodeData, $isQueryNode } from './QueryNode'
import { useQueriesDetailsQuery } from '@/pkgs/queries/queries'
import QuerySelector, { QueryValue } from '@/pkgs/queries/QuerySelector'

interface QueryNodeComponentProps {
    nodeKey: NodeKey
    data: QueryNodeData
}

// CSS to prevent selection of query node contents
const QUERY_NODE_STYLES = `
[data-lexical-query-node] {
  pointer-events: none;
}
[data-lexical-query-node] > div {
  pointer-events: auto;
}
`

export default function QueryNodeComponent({ nodeKey, data }: QueryNodeComponentProps) {
    const [editor] = useLexicalComposerContext()
    const [dialogOpen, setDialogOpen] = useState(false)
    const [queryTitle, setQueryTitle] = useState<string>('')
    const [templateTitle, setTemplateTitle] = useState<string>('')

    // Local state for the dialog
    const [localData, setLocalData] = useState<QueryNodeData>({
        queryId: data.queryId,
        templateId: data.templateId,
        pageSize: data.pageSize === undefined ? 0 : data.pageSize
    })

    // Convert between QueryNodeData and CompleteQueryValue
    const mapToCompleteQueryValue = (data: QueryNodeData): QueryValue => ({
        queryID: data.queryId,
        templateID: data.templateId,
        pageSize: data.pageSize
    })

    const mapToQueryNodeData = (data: QueryValue | null): QueryNodeData => {
        if (!data) {
            return {
                queryId: '',
                templateId: undefined,
                pageSize: 0
            }
        }
        return {
            queryId: data.queryID,
            templateId: data.templateID,
            pageSize: data.pageSize
        }
    }

    // Fetch query details
    const { data: queryDetails } = useQueriesDetailsQuery(data.queryId)

    // Update titles when data changes
    useEffect(() => {
        if (queryDetails) {
            setQueryTitle(queryDetails.Title)
        }

        // We'll set template title from the CompleteQuerySelector now
    }, [queryDetails])

    const handleOpenDialog = () => {
        setDialogOpen(true)
    }

    const handleCloseDialog = () => {
        setDialogOpen(false)
    }

    const handleSave = useCallback(() => {
        editor.update(() => {
            const node = $getNodeByKey(nodeKey)
            if ($isQueryNode(node)) {
                node.setData(localData)
            }
        })
        handleCloseDialog()
    }, [editor, nodeKey, localData])

    return (
        <>
            <style>{QUERY_NODE_STYLES}</style>
            <Box
                onClick={handleOpenDialog}
                contentEditable={false}
                suppressContentEditableWarning={true}
                sx={{
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    padding: '10px',
                    cursor: 'pointer',
                    backgroundColor: '#f9f9f9',
                    userSelect: 'none',
                    '&:hover': {
                        backgroundColor: '#f0f0f0'
                    }
                }}
            >
                <Typography variant='body1' fontWeight='bold'>
                    Query: {queryTitle || data.queryId}
                </Typography>
                {templateTitle && <Typography variant='body2'>Template: {templateTitle}</Typography>}
            </Box>

            <Dialog open={dialogOpen} onClose={handleCloseDialog} fullWidth maxWidth='sm'>
                <DialogTitle>Edit Query</DialogTitle>
                <DialogContent>
                    <QuerySelector
                        value={localData.queryId ? mapToCompleteQueryValue(localData) : null}
                        onChange={(newValue) => setLocalData(mapToQueryNodeData(newValue))}
                        label='Query'
                        required={true}
                        layout='vertical'
                    />
                </DialogContent>
                <DialogActions>
                    <Button onClick={handleCloseDialog}>Cancel</Button>
                    <Button onClick={handleSave} variant='contained'>
                        Save
                    </Button>
                </DialogActions>
            </Dialog>
        </>
    )
}
