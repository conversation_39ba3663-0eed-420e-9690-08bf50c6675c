/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import './DraggableBlockPlugin.css'

import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { eventFiles } from '@lexical/rich-text'
import { mergeRegister } from '@lexical/utils'
import {
    $createRangeSelection,
    $getNearestNodeFromDOMNode,
    $getNodeByKey,
    $getRoot,
    COMMAND_PRIORITY_HIGH,
    COMMAND_PRIORITY_LOW,
    DRAGOVER_COMMAND,
    DROP_COMMAND,
    LexicalEditor,
    $setSelection,
    $getSelection
} from 'lexical'
import * as React from 'react'
import { DragEvent as ReactDragEvent, useEffect, useRef, useState } from 'react'
import { Point } from './point'
import { Rect } from './rect'
import { _createPortal, CAN_USE_DOM } from '../helpers/helpers'
import { $isLayoutContainerNode, LayoutContainerNode } from '../Layout/LayoutContainerNode'
import { LayoutItemNode } from '../Layout/LayoutItemNode'
import { $isCollapsibleContainerNode } from '../Collapsible/CollapsibleContainerNode'

const getDOMSelection = (targetWindow: Window | null): Selection | null =>
    CAN_USE_DOM ? (targetWindow || window).getSelection() : null

function isHTMLElement(x: unknown): x is HTMLElement {
    return x instanceof HTMLElement
}

const SPACE = -5
const TARGET_LINE_HALF_HEIGHT = 2
const DRAGGABLE_BLOCK_MENU_CLASSNAME = 'draggable-block-menu'
const DRAG_DATA_FORMAT = 'application/x-lexical-drag-block'
const TEXT_BOX_HORIZONTAL_PADDING = 28

const Downward = 1
const Upward = -1
const Indeterminate = 0

let prevIndex = Infinity

function getCurrentIndex(keysLength: number): number {
    if (keysLength === 0) {
        return Infinity
    }
    if (prevIndex >= 0 && prevIndex < keysLength) {
        return prevIndex
    }

    return Math.floor(keysLength / 2)
}

function getTopLevelNodeKeys(editor: LexicalEditor): string[] {
    return editor.getEditorState().read(() => $getRoot().getChildrenKeys())
}

function getCollapsedMargins(elem: HTMLElement): {
    marginTop: number
    marginBottom: number
} {
    const getMargin = (element: Element | null, margin: 'marginTop' | 'marginBottom'): number =>
        element ? parseFloat(window.getComputedStyle(element)[margin]) : 0

    const { marginTop, marginBottom } = window.getComputedStyle(elem)
    const prevElemSiblingMarginBottom = getMargin(elem.previousElementSibling, 'marginBottom')
    const nextElemSiblingMarginTop = getMargin(elem.nextElementSibling, 'marginTop')
    const collapsedTopMargin = Math.max(parseFloat(marginTop), prevElemSiblingMarginBottom)
    const collapsedBottomMargin = Math.max(parseFloat(marginBottom), nextElemSiblingMarginTop)

    return { marginBottom: collapsedBottomMargin, marginTop: collapsedTopMargin }
}

export function getBlockElement(
    anchorElem: HTMLElement,
    editor: LexicalEditor,
    event: MouseEvent,
    useEdgeAsDefault = false
): HTMLElement | null {
    const anchorElementRect = anchorElem.getBoundingClientRect()
    const topLevelNodeKeys = getTopLevelNodeKeys(editor)

    let blockElem: HTMLElement | null = null

    editor.getEditorState().read(() => {
        if (useEdgeAsDefault) {
            const [firstNode, lastNode] = [
                editor.getElementByKey(topLevelNodeKeys[0]),
                editor.getElementByKey(topLevelNodeKeys[topLevelNodeKeys.length - 1])
            ]

            const [firstNodeRect, lastNodeRect] = [
                firstNode?.getBoundingClientRect(),
                lastNode?.getBoundingClientRect()
            ]

            if (firstNodeRect && lastNodeRect) {
                if (event.y < firstNodeRect.top) {
                    blockElem = firstNode
                } else if (event.y > lastNodeRect.bottom) {
                    blockElem = lastNode
                }

                if (blockElem) {
                    return
                }
            }
        }

        let index = getCurrentIndex(topLevelNodeKeys.length)
        let direction = Indeterminate

        while (index >= 0 && index < topLevelNodeKeys.length) {
            const key = topLevelNodeKeys[index]
            const elem = editor.getElementByKey(key)
            if (elem === null) {
                break
            }
            const point = new Point(event.x, event.y)
            const domRect = Rect.fromDOM(elem)
            const { marginTop, marginBottom } = getCollapsedMargins(elem)

            const rect = domRect.generateNewRect({
                bottom: domRect.bottom + marginBottom,
                left: anchorElementRect.left,
                right: anchorElementRect.right,
                top: domRect.top - marginTop
            })

            const {
                result,
                reason: { isOnTopSide, isOnBottomSide }
            } = rect.contains(point)

            if (result) {
                blockElem = elem
                prevIndex = index
                break
            }

            if (direction === Indeterminate) {
                if (isOnTopSide) {
                    direction = Upward
                } else if (isOnBottomSide) {
                    direction = Downward
                } else {
                    // stop search block element
                    direction = Infinity
                }
            }

            index += direction
        }
    })

    return blockElem
}

function isOnMenu(element: HTMLElement): boolean {
    return !!element.closest(`.${DRAGGABLE_BLOCK_MENU_CLASSNAME}`)
}

function setMenuPosition(targetElem: HTMLElement | null, floatingElem: HTMLElement, anchorElem: HTMLElement) {
    if (!targetElem) {
        floatingElem.style.opacity = '0'
        floatingElem.style.transform = 'translate(-10000px, -10000px)'
        return
    }

    const targetRect = targetElem.getBoundingClientRect()
    const targetStyle = window.getComputedStyle(targetElem)
    const floatingElemRect = floatingElem.getBoundingClientRect()
    const anchorElementRect = anchorElem.getBoundingClientRect()

    const top =
        targetRect.top + (parseInt(targetStyle.lineHeight, 10) - floatingElemRect.height) / 2 - anchorElementRect.top

    const left = SPACE

    floatingElem.style.opacity = '1'
    floatingElem.style.transform = `translate(${left}px, ${top - 5}px)`
}

function setDragImage(dataTransfer: DataTransfer, draggableBlockElem: HTMLElement) {
    const { transform } = draggableBlockElem.style

    // Remove dragImage borders
    draggableBlockElem.style.transform = 'translateZ(0)'
    dataTransfer.setDragImage(draggableBlockElem, 0, 0)

    setTimeout(() => {
        draggableBlockElem.style.transform = transform
    })
}

function getDragSelection(event: DragEvent): Range | null | undefined {
    let range
    const target = event.target as null | Element | Document
    const targetWindow =
        target == null
            ? null
            : target.nodeType === 9
              ? (target as Document).defaultView
              : (target as Element).ownerDocument.defaultView
    const domSelection = getDOMSelection(targetWindow)
    if (document.caretRangeFromPoint) {
        range = document.caretRangeFromPoint(event.clientX, event.clientY)
    } else if (event.rangeParent && domSelection !== null) {
        domSelection.collapse(event.rangeParent, event.rangeOffset || 0)
        range = domSelection.getRangeAt(0)
    } else {
        throw Error(`Cannot get the selection when dragging`)
    }

    return range
}

function $setSelectionOnDrag(event: DragEvent) {
    const range = getDragSelection(event)
    const rangeSelection = $createRangeSelection()
    if (range !== null && range !== undefined) {
        rangeSelection.applyDOMRange(range)
    }
    $setSelection(rangeSelection)
}

function setTargetLine(
    targetLineElem: HTMLElement,
    targetBlockElem: HTMLElement,
    mouseY: number,
    anchorElem: HTMLElement
) {
    const { top: targetBlockElemTop, height: targetBlockElemHeight } = targetBlockElem.getBoundingClientRect()
    const { top: anchorTop, width: anchorWidth } = anchorElem.getBoundingClientRect()

    const { marginTop, marginBottom } = getCollapsedMargins(targetBlockElem)
    let lineTop = targetBlockElemTop
    if (mouseY >= targetBlockElemTop) {
        lineTop += targetBlockElemHeight + marginBottom / 2
    } else {
        lineTop -= marginTop / 2
    }

    const top = lineTop - anchorTop - TARGET_LINE_HALF_HEIGHT
    const left = TEXT_BOX_HORIZONTAL_PADDING - SPACE

    targetLineElem.style.transform = `translate(${left}px, ${top}px)`
    targetLineElem.style.width = `${anchorWidth - (TEXT_BOX_HORIZONTAL_PADDING - SPACE) * 2}px`
    targetLineElem.style.opacity = '.4'
}

function hideTargetLine(targetLineElem: HTMLElement | null) {
    if (targetLineElem) {
        targetLineElem.style.opacity = '0'
        targetLineElem.style.transform = 'translate(-10000px, -10000px)'
    }
}

function useDraggableBlockMenu(editor: LexicalEditor, anchorElem: HTMLElement, isEditable: boolean): JSX.Element {
    const scrollerElem = anchorElem.parentElement

    const menuRef = useRef<HTMLDivElement>(null)
    const targetLineRef = useRef<HTMLDivElement>(null)
    const isDraggingBlockRef = useRef<boolean>(false)
    const [draggableBlockElem, setDraggableBlockElem] = useState<HTMLElement | null>(null)

    useEffect(() => {
        function onMouseMove(event: MouseEvent) {
            const target = event.target
            if (!isHTMLElement(target)) {
                setDraggableBlockElem(null)
                return
            }

            if (isOnMenu(target)) {
                return
            }

            const _draggableBlockElem = getBlockElement(anchorElem, editor, event)

            setDraggableBlockElem(_draggableBlockElem)
        }

        function onMouseLeave() {
            setDraggableBlockElem(null)
        }

        scrollerElem?.addEventListener('mousemove', onMouseMove)
        scrollerElem?.addEventListener('mouseleave', onMouseLeave)

        return () => {
            scrollerElem?.removeEventListener('mousemove', onMouseMove)
            scrollerElem?.removeEventListener('mouseleave', onMouseLeave)
        }
    }, [scrollerElem, anchorElem, editor])

    useEffect(() => {
        if (menuRef.current) {
            setMenuPosition(draggableBlockElem, menuRef.current, anchorElem)
        }
    }, [anchorElem, draggableBlockElem])

    useEffect(() => {
        function onDragover(event: DragEvent): boolean {
            if (!isDraggingBlockRef.current) {
                return false
            }
            const [isFileTransfer] = eventFiles(event)
            if (isFileTransfer) {
                return false
            }
            const { pageY, target } = event
            if (!isHTMLElement(target)) {
                return false
            }
            const targetBlockElem = getBlockElement(anchorElem, editor, event, true)

            $setSelectionOnDrag(event)

            const targetLineElem = targetLineRef.current
            if (targetBlockElem === null || targetLineElem === null) {
                return false
            }
            setTargetLine(targetLineElem, targetBlockElem, pageY, anchorElem)
            // Prevent default event to be able to trigger onDrop events
            event.preventDefault()
            return true
        }

        function onDrop(event: DragEvent): boolean {
            if (!isDraggingBlockRef.current) {
                return false
            }
            const [isFileTransfer] = eventFiles(event)
            if (isFileTransfer) {
                return false
            }
            const { target, dataTransfer, pageY } = event
            const dragData = dataTransfer?.getData(DRAG_DATA_FORMAT) || ''
            const draggedNode = $getNodeByKey(dragData)
            if (!draggedNode) {
                return false
            }
            if (!isHTMLElement(target)) {
                return false
            }
            const targetBlockElem = getBlockElement(anchorElem, editor, event, true)
            if (!targetBlockElem) {
                return false
            }
            const targetNode = $getNearestNodeFromDOMNode(targetBlockElem)
            if ($isLayoutContainerNode(targetNode) || $isCollapsibleContainerNode(targetNode)) {
                $getSelection()?.insertNodes([draggedNode])
                setDraggableBlockElem(null)
                return true
            }

            if (!targetNode) {
                return false
            }
            if (targetNode === draggedNode) {
                return true
            }
            const targetBlockElemTop = targetBlockElem.getBoundingClientRect().top
            if (pageY >= targetBlockElemTop) {
                targetNode.insertAfter(draggedNode)
            } else {
                targetNode.insertBefore(draggedNode)
            }
            setDraggableBlockElem(null)

            return true
        }

        return mergeRegister(
            editor.registerCommand(
                DRAGOVER_COMMAND,
                (event) => {
                    return onDragover(event)
                },
                COMMAND_PRIORITY_LOW
            ),
            editor.registerCommand(
                DROP_COMMAND,
                (event) => {
                    return onDrop(event)
                },
                COMMAND_PRIORITY_HIGH
            )
        )
    }, [anchorElem, editor])

    function onDragStart(event: ReactDragEvent<HTMLDivElement>): void {
        const dataTransfer = event.dataTransfer
        if (!dataTransfer || !draggableBlockElem) {
            return
        }
        setDragImage(dataTransfer, draggableBlockElem)
        let nodeKey = ''
        editor.update(() => {
            const node = $getNearestNodeFromDOMNode(draggableBlockElem)
            if (node) {
                nodeKey = node.getKey()
            }
        })
        isDraggingBlockRef.current = true
        dataTransfer.setData(DRAG_DATA_FORMAT, nodeKey)
    }

    function onDragEnd(): void {
        isDraggingBlockRef.current = false
        hideTargetLine(targetLineRef.current)
    }

    return _createPortal(
        <>
            <div
                className='icon draggable-block-menu'
                style={{
                    width: '16px',
                    height: '16px',
                    position: 'absolute',
                    left: 3,
                    top: 3,
                    borderRadius: '4px',
                    padding: '2px 1px',
                    cursor: 'grab',
                    opacity: 0,
                    willChange: 'transform'
                }}
                ref={menuRef}
                draggable={true}
                onDragStart={onDragStart}
                onDragEnd={onDragEnd}
            >
                <div className={isEditable ? 'icon' : ''} />
                <span className='material-icons-outlined' style={{ fontSize: '18px' }}>
                    drag_indicator
                </span>
            </div>
            <div className='draggable-block-target-line' ref={targetLineRef} />
        </>,
        anchorElem
    )
}

export default function DraggableBlockPlugin({
    anchorElem = document.body
}: {
    anchorElem?: HTMLElement
}): JSX.Element {
    const [editor] = useLexicalComposerContext()
    return useDraggableBlockMenu(editor, anchorElem, editor._editable)
}
