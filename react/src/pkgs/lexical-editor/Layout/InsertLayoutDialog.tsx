/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { LexicalEditor } from 'lexical'
import { useState } from 'react'
import { INSERT_LAYOUT_COMMAND } from './LayoutPlugin'
import { Button, MenuItem, Select, Typography } from '@mui/material'
import { DialogFormAction } from '@/common/components'

const LAYOUTS = [
    { label: '2 columns (equal width)', value: '1fr 1fr' },
    { label: '2 columns (25% - 75%)', value: '1fr 3fr' },
    { label: '2 columns (75% - 25%)', value: '3fr 1fr' },
    { label: '3 columns (equal width)', value: '1fr 1fr 1fr' },
    { label: '3 columns (25% - 50% - 25%)', value: '1fr 2fr 1fr' },
    { label: '4 columns (equal width)', value: '1fr 1fr 1fr 1fr' }
]

export default function InsertLayoutDialog({
    activeEditor,
    onClose
}: {
    activeEditor: LexicalEditor
    onClose: () => void
}): JSX.Element {
    const [layout, setLayout] = useState(LAYOUTS[0].value)

    const onClick = () => {
        activeEditor.dispatchCommand(INSERT_LAYOUT_COMMAND, layout)
        onClose()
    }

    return (
        <DialogFormAction
            open={true}
            handleClose={onClose}
            text={undefined}
            title={'Insert Columns Layout'}
            item={
                <>
                    <Select
                        variant='standard'
                        sx={{
                            width: '100%',
                            marginTop: '1vh',
                            marginBottom: '1vh',
                            textTransform: 'capitalize'
                        }}
                        value={layout}
                        renderValue={(v) => LAYOUTS.find((l) => l.value == v)?.label}
                        onChange={(event) => {
                            setLayout(event.target.value)
                        }}
                    >
                        {LAYOUTS.map(({ label, value }) => {
                            return (
                                <MenuItem key={value} value={value}>
                                    <Typography textTransform='capitalize'>{label}</Typography>
                                </MenuItem>
                            )
                        })}
                    </Select>
                </>
            }
            handleDisagree={onClose}
            handleAgree={onClick}
            buttonDisagreeLabel={'Close'}
            buttonAgreeLabel={'Insert'}
            alternate={undefined}
            alternateAction={undefined}
            fullWidth={true}
            headerComponent={undefined}
        ></DialogFormAction>
    )
}
