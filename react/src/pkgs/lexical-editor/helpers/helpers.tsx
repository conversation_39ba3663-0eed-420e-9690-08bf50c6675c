import { $createParagraphNode, isHTMLElement, LexicalNode, ParagraphNode } from 'lexical'
import React, { isValidElement, useMemo } from 'react'
import { ReactNode, ReactPortal } from 'react'
import { createPortal } from 'react-dom'

export function _createPortal(
    children: ReactNode,
    container: Element | DocumentFragment,
    key?: null | string
): ReactPortal {
    if (!isValidElement(children)) {
        return createPortal(children, container, key)
    }

    const isInsideDialog = isHTMLElement(container) && !!container.closest('.MuiDialog-root')

    const childrenList = React.Children.map(children, (el, idx) => {
        if (idx == 0) {
            // @ts-ignore
            return React.cloneElement(el, { style: { zIndex: isInsideDialog ? '1300' : '100' } })
        }
        return React.cloneElement(el)
    })

    return createPortal(childrenList[0], container, key)
}

// path: event.getCompoesdPath()
export function composedPathHasId(path: EventTarget[], id: string) {
    for (const el of path) {
        if ((el as HTMLElement)?.id === id) {
            return true
        }
    }

    return false
}

// insert paragraph before node
export function $insertParagraphAtNode(node: LexicalNode, position: 'before' | 'after'): ParagraphNode {
    const $p = $createParagraphNode()
    if (position == 'before') {
        node.insertBefore($p)
    } else {
        node.insertAfter($p)
    }
    $p.select()
    return $p
}

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

// invariant(condition, message) will refine types based on "condition", and
// if "condition" is false will throw an error. This function is special-cased
// in flow itself, so we can't name it anything else.
export function invariant(cond?: boolean, message?: string, ...args: string[]): asserts cond {
    if (cond) {
        return
    }

    throw new Error(
        'Internal Lexical error: invariant() is meant to be replaced at compile ' +
            'time. There is no runtime version. Error: ' +
            message
    )
}

// from @lexical
export const CAN_USE_DOM: boolean =
    typeof window !== 'undefined' &&
    typeof window.document !== 'undefined' &&
    typeof window.document.createElement !== 'undefined'

// export const IS_APPLE: boolean = CAN_USE_DOM && /Mac|iPod|iPhone|iPad/.test(navigator.platform)
export const IS_FIREFOX: boolean = CAN_USE_DOM && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent)
// export const IS_SAFARI: boolean = CAN_USE_DOM && /Version\/[\d.]+.*Safari/.test(navigator.userAgent)

// export const IS_IOS: boolean = CAN_USE_DOM && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream

// export const IS_ANDROID: boolean = CAN_USE_DOM && /Android/.test(navigator.userAgent)

// Keep these in case we need to use them in the future.
// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);
// export const IS_CHROME: boolean = CAN_USE_DOM && /^(?=.*Chrome).*/i.test(navigator.userAgent)
// export const canUseTextInputEvent: boolean = CAN_USE_DOM && 'TextEvent' in window && !documentMode;

// export const IS_ANDROID_CHROME: boolean = CAN_USE_DOM && IS_ANDROID && IS_CHROME

// export const IS_APPLE_WEBKIT = CAN_USE_DOM && /AppleWebKit\/[\d.]+/.test(navigator.userAgent) && !IS_CHROME
/**
 * Calculates the zoom level of an element as a result of using
 * css zoom property.
 * @param element
 */

export function calculateZoomLevel(element: Element | null): number {
    if (IS_FIREFOX) {
        return 1
    }
    let zoom = 1
    while (element) {
        zoom *= Number(window.getComputedStyle(element).getPropertyValue('zoom'))
        element = element.parentElement
    }
    return zoom
}
