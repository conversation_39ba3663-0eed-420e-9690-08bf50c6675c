import { expect, it } from 'vitest'
import { sanitizeHTML } from '../sanitizeHTML'
import { readdir, readFile } from 'fs/promises'

// verify no information is lost when it is sanitized
// assume if sanitizeHTML can parse CKEditor HTML correctly then it will be able to parse migrated content correctly

describe('sanitizeHTML converts from CKEditor properly', async () => {
    const fileNames = await readdir(`${__dirname}/html`)

    for (const fileName of fileNames) {
        const fileNameWithoutExtension = fileName.split('.')[0]
        it(fileNameWithoutExtension, async () => {
            const htmlString = await readFile(`${__dirname}/html/${fileName}`, 'utf-8')
            const result = sanitizeHTML(htmlString)
            expect(result).toMatchFileSnapshot(`./__snapshots__/${fileNameWithoutExtension}.snap.html`)
        })
    }
})
