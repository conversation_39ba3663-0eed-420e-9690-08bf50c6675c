/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import {
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    ElementNode,
    LexicalEditor,
    LexicalNode,
    NodeKey,
    SerializedElementNode,
    Spread
} from 'lexical'

type SerializedCollapsibleContainerNode = Spread<
    {
        open: boolean
    },
    SerializedElementNode
>

export function convertDetailsElement(domNode: HTMLElement): DOMConversionOutput | null {
    const isDetailsElement = domNode.tagName == 'DETAILS'
    if (!isDetailsElement) {
        return null
    }

    const detailsDomNode = domNode as HTMLDetailsElement

    const isOpen = detailsDomNode.open !== undefined ? detailsDomNode.open : true
    const node = $createCollapsibleContainerNode(isOpen)
    return {
        node
    }
}

export class CollapsibleContainerNode extends ElementNode {
    __open: boolean

    constructor(open: boolean, key?: <PERSON><PERSON><PERSON><PERSON>) {
        super(key)
        this.__open = open
    }

    static getType(): string {
        return 'collapsible-container'
    }

    static clone(node: CollapsibleContainerNode): CollapsibleContainerNode {
        return new CollapsibleContainerNode(node.__open, node.__key)
    }

    createDOM(config: EditorConfig, editor: LexicalEditor): HTMLElement {
        const dom = document.createElement('details')
        dom.classList.add('Collapsible__container')
        dom.open = this.__open
        dom.addEventListener('toggle', () => {
            const open = editor.getEditorState().read(() => this.getOpen())
            if (open !== dom.open) {
                editor.update(() => this.toggleOpen())
            }
        })

        return dom
    }

    updateDOM(prevNode: CollapsibleContainerNode, dom: HTMLDetailsElement): boolean {
        if (prevNode.__open !== this.__open) {
            dom.open = this.__open
        }

        return false
    }

    static importDOM(): DOMConversionMap<HTMLDetailsElement | HTMLDivElement> | null {
        return {
            details: (domNode: HTMLElement) => {
                return {
                    conversion: convertDetailsElement,
                    priority: 1
                }
            },
            div: (domNode: HTMLElement) => {
                // Support converting from CKEditor collapsible boxes
                if (!domNode.classList.contains('collapsible-box')) {
                    return null
                }

                return {
                    conversion: (domNode) => {
                        const node = $createCollapsibleContainerNode(true)

                        return {
                            node
                        }
                    },
                    priority: 4
                }
            }
        }
    }

    static importJSON(serializedNode: SerializedCollapsibleContainerNode): CollapsibleContainerNode {
        const node = $createCollapsibleContainerNode(serializedNode.open)
        return node
    }

    exportDOM(): DOMExportOutput {
        const element = document.createElement('details')
        element.setAttribute('open', this.__open.toString())
        return { element }
    }

    exportJSON(): SerializedCollapsibleContainerNode {
        return {
            ...super.exportJSON(),
            open: this.__open,
            type: 'collapsible-container',
            version: 1
        }
    }

    setOpen(open: boolean): void {
        const writable = this.getWritable()
        writable.__open = open
    }

    getOpen(): boolean {
        return this.getLatest().__open
    }

    toggleOpen(): void {
        this.setOpen(!this.getOpen())
    }
}

export function $createCollapsibleContainerNode(isOpen: boolean): CollapsibleContainerNode {
    return new CollapsibleContainerNode(isOpen)
}

export function $isCollapsibleContainerNode(node: LexicalNode | null | undefined): node is CollapsibleContainerNode {
    return node instanceof CollapsibleContainerNode
}
