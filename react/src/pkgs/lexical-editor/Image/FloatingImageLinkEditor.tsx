import { useEffect, useRef } from 'react'
import { LexicalEditor } from 'lexical'
import { _createPortal } from '../helpers/helpers'
import LinkEditor, { LinkEditorHandlers } from '../LinkEditor'
import { LinkAttributes } from '../Link/LinkNode'

interface FloatingImageLinkEditorProps {
    editor: LexicalEditor
    imageRef: { current: null | HTMLElement }
    linkOnChangeHandler: (link: string, attributes?: LinkAttributes) => void
    defaultLink: string
    defaultAttributes?: LinkAttributes
}

// Close copy of FloatingLinkEditor
export function FloatingImageLinkEditor({
    editor,
    imageRef,
    linkOnChangeHandler,
    defaultLink,
    defaultAttributes,
    ...props
}: FloatingImageLinkEditorProps) {
    const editorRef = useRef<LinkEditorHandlers>(null)

    useEffect(() => {
        if (editorRef?.current && imageRef?.current) {
            // above FloatingImageMenu
            editorRef.current._position(imageRef?.current, 'top', -45)
        }
    }, [editorRef, imageRef])

    return _createPortal(
        <LinkEditor
            ref={editorRef}
            boxProps={props}
            defaultLink={defaultLink}
            attributes={defaultAttributes}
            linkOnChangeHandler={(v, attributes) => {
                linkOnChangeHandler(v, attributes)
            }}
        />,
        document.body
    )
}
