/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */

import type {
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    LexicalEditor,
    LexicalNode,
    NodeKey,
    SerializedEditor,
    SerializedLexicalNode,
    Spread
} from 'lexical'

import { $applyNodeReplacement, $isParagraphNode, DecoratorNode, ParagraphNode } from 'lexical'
import * as React from 'react'
import { Suspense } from 'react'
import { parseInlineStyle } from './parseInlineStyle'
import { $getChildrenRecursively } from '../helpers/LexicalUtils'
import { $findClosestInstanceOf } from '../helpers/$findClosestInstanceOf'

const ImageComponent = React.lazy(
    // @ts-ignore
    () => import('./ImageComponent')
)

function getValidWidthAndHeightString(value: string | number) {
    if (typeof value == 'string') {
        // 100% | 'inherit'
        return value
    }

    return `${value.toString()}px`
}

export interface ImagePayload {
    altText: string
    key?: NodeKey
    caption?: string
    captionsEnabled?: boolean
    showCaption?: boolean
    width?: number | string
    maxWidth?: string | number
    height?: number
    src: string
    href?: string
    target?: string
    style?: string
}

function convertImageElement(domNode: Node, href?: string, target?: string): null | DOMConversionOutput {
    if (domNode instanceof HTMLImageElement) {
        const { alt: altText, width, height, style } = domNode
        const src = domNode.getAttribute('src') || ''
        const node = $createImageNode({
            altText,
            height,
            src,
            width,
            href,
            target,
            style: style?.cssText || undefined
        })

        return { node }
    }

    return null
}

function convertFigureElement(domNode: Node): null | DOMConversionOutput {
    if ((domNode as HTMLElement).tagName == 'FIGURE') {
        let figCaptionNode: HTMLElement | null = null
        let imgNode: HTMLImageElement | null = null
        let linkNode: HTMLLinkElement | null = null
        // @ts-ignore

        for (const node of domNode.childNodes) {
            if (node instanceof HTMLImageElement) {
                imgNode = node as HTMLImageElement
            } else if ((node as HTMLElement).tagName == 'FIGCAPTION') {
                // @ts-ignore
                figCaptionNode = node
            } else if ((node as HTMLElement).tagName == 'A') {
                // @ts-ignore
                linkNode = node

                for (const linkChildNode of node.childNodes) {
                    if (linkChildNode instanceof HTMLImageElement) {
                        imgNode = linkChildNode as HTMLImageElement
                    }
                }
            }
        }

        if (!imgNode) {
            return null
        }

        if (!figCaptionNode) {
            return convertImageElement(
                imgNode as Node,
                linkNode?.getAttribute('href') || undefined,
                linkNode?.getAttribute('target') || undefined
            )
        }

        const { alt: altText, width, height, style } = imgNode

        const src = imgNode.getAttribute('src') || ''
        const node = $createImageNode({
            altText,
            height,
            src,
            width,
            caption: figCaptionNode.textContent || undefined,
            showCaption: !!figCaptionNode.textContent,
            captionsEnabled: true,
            href: linkNode?.getAttribute('href') || undefined,
            target: linkNode?.getAttribute('target') || undefined,
            style: style?.cssText || undefined
        })

        return { node }
    }

    return null
}

export type SerializedImageNode = Spread<
    {
        altText: string
        caption: string
        height?: number
        maxWidth: string | number
        showCaption: boolean
        src: string
        width?: number | string
        href?: string
        target?: string
        style?: string
    },
    SerializedLexicalNode
>

export class ImageNode extends DecoratorNode<JSX.Element> {
    __src: string
    __altText: string
    __width: 'inherit' | number | string
    __height: 'inherit' | number
    __maxWidth: string | number
    __showCaption: boolean
    __caption: string
    // Captions cannot yet be used within editor cells
    __captionsEnabled: boolean
    __href: string
    __target: string | undefined
    __style?: string

    static getType(): string {
        return 'image'
    }

    static clone(node: ImageNode): ImageNode {
        return new ImageNode(
            node.__src,
            node.__altText,
            node.__maxWidth,
            node.__width,
            node.__height,
            node.__showCaption,
            node.__caption,
            node.__captionsEnabled,
            node.__key,
            node.__href,
            node.__target,
            node.__style
        )
    }

    static importJSON(serializedNode: SerializedImageNode): ImageNode {
        const { altText, height, width, maxWidth, caption, src, showCaption, href, target, style } = serializedNode
        const node = $createImageNode({
            altText,
            height,
            maxWidth,
            showCaption,
            caption,
            src,
            width,
            href,
            target,
            style
        })

        // const nestedEditor = node.__caption
        // const editorState = nestedEditor.parseEditorState(caption.editorState)
        // if (!editorState.isEmpty()) {
        //     nestedEditor.setEditorState(editorState)
        // }
        return node
    }

    exportDOM(): DOMExportOutput {
        const imgElement = document.createElement('img')

        imgElement.setAttribute('src', this.__src)
        imgElement.setAttribute('alt', this.__altText)
        imgElement.setAttribute('width', this.__width.toString())
        imgElement.setAttribute('height', this.__height.toString())

        let currentStyle = parseInlineStyle(this.__style)
        currentStyle['width'] = getValidWidthAndHeightString(this.__width)
        currentStyle['height'] = getValidWidthAndHeightString(this.__height)

        if (!currentStyle?.['display']) {
            this.setInlineStyle('display', 'inline-block')
            currentStyle = parseInlineStyle(this.__style)
        }

        imgElement.setAttribute(
            'style',
            Object.entries(currentStyle)
                .map(([k, v]) => `${k}:${v}`)
                .join(';')
        )

        const figure = document.createElement('figure')

        if (this.__href) {
            const href = document.createElement('a')
            href.setAttribute('href', this.__href)

            if (this.__target) {
                href.setAttribute('target', this.__target)
            }

            href.setAttribute('title', this.__showCaption ? this.__caption : this.__altText || this.__href)

            href.appendChild(imgElement)
            figure.appendChild(href)
        } else {
            figure.append(imgElement)
        }

        if (this.__caption && this.__showCaption) {
            const figureCaption = document.createElement('figureCaption')
            figureCaption.textContent = this.__caption

            figure.appendChild(figureCaption)

            return { element: figure }
        }

        return { element: this.__href ? figure : imgElement }
    }

    static importDOM(): DOMConversionMap | null {
        return {
            img: (node: Node) => ({
                conversion: convertImageElement,
                priority: 1
            }),
            figure: (node: Node) => ({
                conversion: convertFigureElement,
                priority: 1
            })
        }
    }

    constructor(
        src: string,
        altText: string,
        maxWidth: string | number,
        width?: 'inherit' | number | string,
        height?: 'inherit' | number,
        showCaption?: boolean,
        caption?: string,
        captionsEnabled?: boolean,
        key?: NodeKey,
        href?: string,
        target?: string,
        style?: string
    ) {
        super(key)

        this.__src = src

        // When an image is copied using browser menu (right-click) the src attribute becomes absolute
        if (this.__src.includes('http') && this.__src.includes('/images/')) {
            this.__src = this.__src.match('/images/.*')?.[0] || this.__src
        }

        this.__altText = altText
        this.__maxWidth = maxWidth
        this.__width = width || '100%'
        this.__height = height || 'inherit'
        this.__showCaption = showCaption || false
        this.__caption = caption || ''
        this.__captionsEnabled = captionsEnabled || captionsEnabled === undefined
        this.__href = href || ''
        this.__target = target || ''
        this.__style =
            style ||
            `width: ${getValidWidthAndHeightString(this.__width)}; height: ${getValidWidthAndHeightString(
                this.__height
            )};`
    }

    exportJSON(): SerializedImageNode {
        return {
            altText: this.getAltText(),
            caption: this.__caption,
            height: this.__height === 'inherit' ? 0 : this.__height,
            maxWidth: this.__maxWidth,
            showCaption: this.__showCaption,
            src: this.getSrc(),
            type: 'image',
            version: 1,
            width: this.__width === 'inherit' ? 0 : this.__width,
            href: this.__href,
            target: this.getTarget(),
            style: this.__style
        }
    }

    setWidthAndHeight(width: 'inherit' | '100%' | number, height: 'inherit' | number): void {
        const writable = this.getWritable()
        width = typeof width == 'number' ? Math.round(width) : width
        height = typeof height == 'number' ? Math.round(height) : height
        writable.__width = width
        writable.__height = height

        this.setInlineStyle('width', getValidWidthAndHeightString(width))
        this.setInlineStyle('height', getValidWidthAndHeightString(height))
    }

    setShowCaption(showCaption: boolean): void {
        const writable = this.getWritable()
        writable.__showCaption = showCaption
    }

    setCaption(caption: string): void {
        const writable = this.getWritable()
        writable.__caption = caption
    }

    setHref(href: string): void {
        const writable = this.getWritable()
        writable.__href = href
    }

    getHref(): string {
        return this.__href
    }

    setTarget(target: string | undefined): void {
        const writable = this.getWritable()
        writable.__target = target
    }

    getTarget(): string | undefined {
        return this.__target
    }

    private setInlineStyle(attribute: string, value: string | null | undefined): void {
        const writable = this.getWritable()
        const currentStyle = parseInlineStyle(writable.__style)

        if (value) {
            currentStyle[attribute] = value
        } else if (attribute in currentStyle) {
            delete currentStyle[attribute]
        }

        const styleString = Object.entries(currentStyle)
            .map(([k, v]) => `${k}:${v}`)
            .join(';')
        writable.__style = styleString
    }

    setFloat(float: 'left' | 'right' | null | undefined): void {
        this.setInlineStyle('float', float)
    }

    getFloat(): string | undefined {
        const currentStyle = parseInlineStyle(this.__style)
        return currentStyle['float']
    }

    // View
    createDOM(config: EditorConfig): HTMLElement {
        const span = document.createElement('span')

        if (this.__style) {
            span.style.cssText = this.__style
        }

        const theme = config.theme
        const className = theme.image
        if (className !== undefined) {
            span.className = className
        }
        return span
    }

    updateDOM(): true {
        // needs to be true to allow for <span> from createDOM to maintain ImageNode.__style
        return true
    }

    getSrc(): string {
        return this.__src
    }

    getAltText(): string {
        return this.__altText
    }

    decorate(): JSX.Element {
        return (
            <Suspense fallback={null}>
                <ImageComponent
                    src={this.__src}
                    altText={this.__altText}
                    width={this.__width}
                    height={this.__height}
                    maxWidth={this.__maxWidth}
                    nodeKey={this.getKey()}
                    showCaption={this.__showCaption}
                    caption={this.__caption}
                    captionsEnabled={this.__captionsEnabled}
                    resizable={true}
                    href={this.__href}
                    target={this.__target}
                    style={this.__style}
                />
            </Suspense>
        )
    }
}

export function $createImageNode({
    altText,
    height,
    maxWidth = 'inherit',
    captionsEnabled,
    src,
    width,
    showCaption,
    caption,
    key,
    href,
    target,
    style
}: ImagePayload): ImageNode {
    return $applyNodeReplacement(
        new ImageNode(
            src,
            altText,
            maxWidth,
            width,
            height,
            showCaption,
            caption,
            captionsEnabled,
            key,
            href,
            target,
            style
        )
    )
}

export function $isImageNode(node: LexicalNode | null | undefined): node is ImageNode {
    return node instanceof ImageNode || node?.__type == ImageNode.getType()
}
