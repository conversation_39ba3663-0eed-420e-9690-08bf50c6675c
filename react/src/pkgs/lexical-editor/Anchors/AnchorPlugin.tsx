import { mergeRegister } from '@lexical/utils'
import { useLexicalComposerContext } from '@lexical/react/LexicalComposerContext'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { $getRoot, $getSelection, createCommand, type LexicalCommand } from 'lexical'
import { HighPriority } from '@/pkgs/lexical-editor/toolbar/toolbar.helpers'
import { Box, DialogContent, FormHelperText, InputAdornment } from '@mui/material'
import { $getClosestAnchorNode, IAnchorNode } from '@/pkgs/lexical-editor/Anchors/utils'
import TextField from '@mui/material/TextField'
import AnchorIcon from '@mui/icons-material/Anchor'
import ContentCopyIcon from '@mui/icons-material/ContentCopy'
import CustomIconButton from '@/common/components/CustomIconButton'
import { copyToClipboard } from '@/pkgs/media/copyToClipboard'
import CheckCircleOutlinedIcon from '@mui/icons-material/CheckCircleOutlined'
import DeleteIcon from '@mui/icons-material/Delete'
import { colours } from '@/common/colours'
import CMDialog from '@/common/components/CMDialog'
import { $getAllNodes } from '../helpers/LexicalUtils'
import { $isLinkNode } from '../Link/LinkNode'

export const SET_ANCHOR_EDITOR_OPEN_COMMAND: LexicalCommand<boolean> = createCommand('SET_ANCHOR_EDITOR_OPEN_COMMAND')

export function AnchorPlugin() {
    const [editor] = useLexicalComposerContext()
    const [open, setOpen] = useState(false)
    const [anchorNode, setAnchorNode] = useState<IAnchorNode | null>(null)
    const [anchorValue, setAnchorValue] = useState<string>('')

    const anchorNodeHasId = !!anchorNode?.getId()
    const hasChanges = anchorValue != anchorNode?.getId()

    const anchorValueUseCount = useMemo(() => {
        let count = 0
        editor.update(() => {
            const nodes = $getAllNodes($getRoot())
            const anchorNodes = nodes.filter((node) => $isLinkNode(node) && node.getURL().includes(anchorValue))
            count = anchorNodes.length
        })
        return count
    }, [anchorValue, editor])

    const closeEditor = () => {
        setAnchorNode(null)
        setAnchorValue('')
        setOpen(false)
    }

    const openEditor = () => {
        editor.read(() => {
            const selection = $getSelection()
            const selectedNode = $getClosestAnchorNode(selection)

            if (selectedNode) {
                setAnchorNode(selectedNode)
                setAnchorValue(selectedNode.getId() || getDefaultId(selectedNode))
                setOpen(true)
            } else {
                closeEditor()
            }
        })
    }

    useEffect(() => {
        return mergeRegister(
            editor.registerCommand(
                SET_ANCHOR_EDITOR_OPEN_COMMAND,
                (isOpen) => {
                    if (isOpen) {
                        openEditor()
                    } else {
                        closeEditor()
                    }
                    return true
                },
                HighPriority
            )
        )
    }, [])

    if (!open) {
        return null
    }

    return (
        <CMDialog open={open} onClose={closeEditor}>
            <DialogContent
                // tabIndex={-1}
                sx={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '0.8rem'
                }}
            >
                <Box display='flex' flexDirection='row'>
                    <TextField
                        label='Anchor Nickname'
                        InputProps={{
                            startAdornment: (
                                <InputAdornment position='start'>
                                    <AnchorIcon sx={{ color: colours.anchor }} />
                                </InputAdornment>
                            ),
                            endAdornment: (
                                <InputAdornment position='end'>
                                    <CustomIconButton
                                        title='Copy to clipboard'
                                        onClick={() => copyToClipboard(`#${anchorValue}`)}
                                    >
                                        <ContentCopyIcon fontSize='small' />
                                    </CustomIconButton>
                                </InputAdornment>
                            )
                        }}
                        value={anchorValue}
                        onChange={(e) => setAnchorValue(sanitizeId(e.target.value))}
                    />
                    <Box display='flex' flexDirection='row' sx={{ marginRight: '-1rem' }}>
                        <CustomIconButton
                            title='Save'
                            disabled={!hasChanges}
                            onClick={() => {
                                editor.update(() => {
                                    if (anchorNode) {
                                        anchorNode.setId(anchorValue)
                                    }
                                    closeEditor()
                                })
                            }}
                        >
                            <CheckCircleOutlinedIcon sx={{ color: !hasChanges ? undefined : colours.published }} />
                        </CustomIconButton>
                        <CustomIconButton
                            disabled={!anchorNode || !anchorNodeHasId}
                            title='Delete anchor'
                            onClick={() => {
                                editor.update(() => {
                                    if (anchorNode) {
                                        anchorNode.setId('')
                                    }
                                    closeEditor()
                                })
                            }}
                        >
                            <DeleteIcon sx={{ color: !anchorNode || !anchorNodeHasId ? undefined : colours.expired }} />
                        </CustomIconButton>
                    </Box>
                </Box>
                {anchorValueUseCount > 0 && (
                    <FormHelperText sx={{ color: colours.anchor }}>
                        This anchor link is referenced {anchorValueUseCount}{' '}
                        {anchorValueUseCount == 1 ? 'time' : 'times'} in this page.
                    </FormHelperText>
                )}
            </DialogContent>
        </CMDialog>
    )
}

function sanitizeId(input: string | undefined | null): string {
    if (!input) {
        return ''
    }

    let sanitized = input

    sanitized = sanitized.replace(/\s+/g, '-')
    sanitized = sanitized.replace(/[^A-Za-z0-9\-_:.]/g, '')

    // IDs cannot start with a digit; prefix with a letter if necessary
    if (/^[0-9]/.test(sanitized)) {
        sanitized = 'id-' + sanitized
    }

    return sanitized.substring(0, 50)
}

function getDefaultId(node: any) {
    return node && typeof node['getTextContent'] === 'function' ? sanitizeId(node.getTextContent()) : ''
}
