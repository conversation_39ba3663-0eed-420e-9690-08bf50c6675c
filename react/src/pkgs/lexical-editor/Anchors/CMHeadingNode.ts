import {
    $createParagraphNode,
    DOMConversionMap,
    DOMConversionOutput,
    DOMExportOutput,
    EditorConfig,
    ElementFormatType,
    ElementNode,
    isHTMLElement,
    LexicalEditor,
    LexicalNode,
    NodeKey,
    RangeSelection
} from 'lexical'
import { HeadingTagType, SerializedHeadingNode } from '@lexical/rich-text'
import { addClassNamesToElement } from '@lexical/utils'
import { CMParagraphNode } from '@/pkgs/lexical-editor/Anchors/CMParagraphNode'

type SerializedCMHeadingNode = SerializedHeadingNode & {
    id?: string
}

export class CMHeadingNode extends ElementNode {
    __id?: string

    getId() {
        return this.__id
    }

    setId(id: string) {
        const writable = this.getWritable()
        writable.__id = id
    }

    __tag: HeadingTagType

    static getType(): string {
        return 'heading'
    }

    static clone(node: CMHeadingNode): CMHeadingNode {
        return new CMHeadingNode(node.__tag, node.__id || '', node.__key)
    }

    constructor(tag: HeadingTagType, id: string, key?: NodeKey) {
        super(key)
        this.__tag = tag
        this.__id = id
    }

    getTag(): HeadingTagType {
        return this.__tag
    }

    // View

    createDOM(config: EditorConfig): HTMLElement {
        const tag = this.__tag
        const element = document.createElement(tag)
        if (this.__id) {
            element.id = this.__id
        }
        const theme = config.theme
        const classNames = theme.heading
        if (classNames !== undefined) {
            const className = classNames[tag]
            addClassNamesToElement(element, className)
        }
        return element
    }

    updateDOM(prevNode: CMHeadingNode, dom: HTMLElement): boolean {
        return this.__id !== prevNode.__id
    }

    static importDOM(): DOMConversionMap | null {
        return {
            h1: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            h2: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            h3: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            h4: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            h5: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            h6: (node: Node) => ({
                conversion: $convertHeadingElement,
                priority: 0
            }),
            p: (node: Node) => {
                // domNode is a <p> since we matched it by nodeName
                const paragraph = node as HTMLParagraphElement
                const firstChild = paragraph.firstChild
                if (firstChild !== null && isGoogleDocsTitle(firstChild)) {
                    return {
                        conversion: () => ({ node: null }),
                        priority: 3
                    }
                }
                return null
            },
            span: (node: Node) => {
                if (isGoogleDocsTitle(node)) {
                    return {
                        conversion: (domNode: Node) => {
                            return {
                                node: $createCMHeadingNode('h1')
                            }
                        },
                        priority: 3
                    }
                }
                return null
            }
        }
    }

    exportDOM(editor: LexicalEditor): DOMExportOutput {
        const { element } = super.exportDOM(editor)

        if (element && isHTMLElement(element)) {
            if (this.isEmpty()) {
                element.append(document.createElement('br'))
            }

            if (this.__id) {
                element.id = this.__id
            }

            const formatType = this.getFormatType()
            element.style.textAlign = formatType

            const direction = this.getDirection()
            if (direction) {
                element.dir = direction
            }
        }

        return {
            element
        }
    }

    static importJSON(serializedNode: SerializedCMHeadingNode): CMHeadingNode {
        const node = new CMHeadingNode(serializedNode.tag, serializedNode.id || '')
        node.setFormat(serializedNode.format)
        node.setIndent(serializedNode.indent)
        node.setDirection(serializedNode.direction)
        return node
    }

    exportJSON(): SerializedHeadingNode {
        return {
            ...super.exportJSON(),
            tag: this.getTag(),
            type: 'heading',
            version: 1,
            ...(this.getId() && { id: this.getId() })
        }
    }

    // Mutation
    insertNewAfter(selection?: RangeSelection, restoreSelection = true): CMParagraphNode | CMHeadingNode {
        const anchorOffet = selection ? selection.anchor.offset : 0
        const lastDesc = this.getLastDescendant()
        const isAtEnd =
            !lastDesc ||
            (selection && selection.anchor.key === lastDesc.getKey() && anchorOffet === lastDesc.getTextContentSize())
        const newElement = isAtEnd || !selection ? $createParagraphNode() : $createCMHeadingNode(this.getTag())
        const direction = this.getDirection()
        newElement.setDirection(direction)
        this.insertAfter(newElement, restoreSelection)
        if (anchorOffet === 0 && !this.isEmpty() && selection) {
            const paragraph = $createParagraphNode()
            paragraph.select()
            this.replace(paragraph, true)
        }
        // @ts-ignore
        return newElement
    }

    collapseAtStart(): true {
        const newElement = !this.isEmpty() ? $createCMHeadingNode(this.getTag()) : $createParagraphNode()
        const children = this.getChildren()
        children.forEach((child) => newElement.append(child))
        this.replace(newElement)
        return true
    }

    extractWithChild(): boolean {
        return true
    }
}

function isGoogleDocsTitle(domNode: Node): boolean {
    if (domNode.nodeName.toLowerCase() === 'span') {
        return (domNode as HTMLSpanElement).style.fontSize === '26pt'
    }
    return false
}

export function $convertHeadingElement(element: HTMLElement): DOMConversionOutput {
    console.log('element', element)
    const nodeName = element.nodeName.toLowerCase()
    let node: CMHeadingNode | null = null
    if (
        nodeName === 'h1' ||
        nodeName === 'h2' ||
        nodeName === 'h3' ||
        nodeName === 'h4' ||
        nodeName === 'h5' ||
        nodeName === 'h6'
    ) {
        node = new CMHeadingNode(nodeName, element.id || '')
        if (element.style !== null) {
            node.setFormat(element.style.textAlign as ElementFormatType)
        }
    }
    return { node }
}

export function $createCMHeadingNode(headingTag: HeadingTagType): CMHeadingNode {
    return new CMHeadingNode(headingTag, '')
}

export function $isCMHeadingNode(node: LexicalNode | null | undefined): node is CMHeadingNode {
    return node instanceof CMHeadingNode
}
