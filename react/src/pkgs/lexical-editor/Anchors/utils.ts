import { $isNodeSelection, $isRangeSelection, BaseSelection, ElementNode, LexicalNode } from 'lexical'

export interface IAnchorNode {
    getId(): string | undefined

    setId(id: string): void
}

export function $getClosestAnchorNode(selection: BaseSelection | null): IAnchorNode | null {
    if (!selection) {
        return null
    }
    if ($isRangeSelection(selection)) {
        const selAnchor = getClosestAnchor(selection.anchor.getNode())
        const selFocus = getClosestAnchor(selection.focus.getNode())
        if (selAnchor && selFocus && selAnchor === selFocus) {
            return selAnchor
        }
    } else if ($isNodeSelection(selection)) {
        const selectedNodes = selection.getNodes()
        const anchors = selectedNodes.map(getClosestAnchor).filter(Boolean)
        if (anchors.length === 1) {
            return anchors[0]
        }
    }
    return null
}

function getClosestAnchor(node: LexicalNode | ElementNode | null | undefined): IAnchorNode | null {
    let current = node
    while (current) {
        if ($isAnchorNode(current)) {
            return current
        }
        current = current.getParent()
    }
    return null
}

export function $isAnchorNode(node: any): node is IAnchorNode {
    return node != null && typeof node.getId === 'function' && typeof node.setId === 'function'
}
