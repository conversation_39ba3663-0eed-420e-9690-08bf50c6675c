import { sanitizePathString } from './index'

const setParentByPathAndContent = (path, content, setParent) => {
    let pathArray = path ? path.split('.') : ''
    if (pathArray.length > 1) {
        let sanitizedPath = pathArray[pathArray.length - 2]
        for (const item of content) {
            if (sanitizedPath === sanitizePathString(item.id)) {
                setParent(item.id)
                return item.id
            }
        }
    }
}

export default setParentByPathAndContent
