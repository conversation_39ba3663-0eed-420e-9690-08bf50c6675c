import { SvgIcon } from '@mui/material'

export function FloatLeft({ ...props }) {
    return (
        <SvgIcon>
            <svg width='26' height='26' viewBox='0 0 30 26' fill='none' xmlns='http://www.w3.org/2000/svg'>
                <g filter='url(#filter0_d_2_41)'>
                    <path
                        d='M4 0.3C4 0.134314 4.13431 0 4.3 0H25.7C25.8657 0 26 0.134315 26 0.3V2.7C26 2.86569 25.8657 3 25.7 3H4.3C4.13431 3 4 2.86569 4 2.7V0.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M4 20.3C4 20.1343 4.13431 20 4.3 20H25.7C25.8657 20 26 20.1343 26 20.3V22.7C26 22.8657 25.8657 23 25.7 23H4.3C4.13431 23 4 22.8657 4 22.7V20.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M16 5.3C16 5.13431 16.1343 5 16.3 5H25.7C25.8657 5 26 5.13431 26 5.3V7.7C26 7.86569 25.8657 8 25.7 8H16.3C16.1343 8 16 7.86569 16 7.7V5.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M16 10.3C16 10.1343 16.1343 10 16.3 10H25.7C25.8657 10 26 10.1343 26 10.3V12.7C26 12.8657 25.8657 13 25.7 13H16.3C16.1343 13 16 12.8657 16 12.7V10.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M16 15.3C16 15.1343 16.1343 15 16.3 15H25.7C25.8657 15 26 15.1343 26 15.3V17.7C26 17.8657 25.8657 18 25.7 18H16.3C16.1343 18 16 17.8657 16 17.7V15.3Z'
                        fill='currentColor'
                    />
                    <path
                        fillRule='evenodd'
                        clipRule='evenodd'
                        d='M12.8889 6.62707C12.8889 6.5152 12.8889 6.45926 12.8763 6.4137C12.8474 6.30884 12.7716 6.2268 12.6748 6.19544C12.6328 6.18182 12.5812 6.18182 12.4779 6.18182H5.52211C5.41885 6.18182 5.36721 6.18182 5.32515 6.19544C5.22836 6.2268 5.15264 6.30884 5.12369 6.4137C5.11111 6.45926 5.11111 6.5152 5.11111 6.62707V16.3729C5.11111 16.4848 5.11111 16.5407 5.12369 16.5863C5.15264 16.6912 5.22836 16.7732 5.32515 16.8046C5.36721 16.8182 5.41885 16.8182 5.52211 16.8182H12.4779C12.5812 16.8182 12.6328 16.8182 12.6748 16.8046C12.7716 16.7732 12.8474 16.6912 12.8763 16.5863C12.8889 16.5407 12.8889 16.4848 12.8889 16.3729V6.62707ZM4.411 5C4.30773 5 4.2561 5 4.21404 5.01363C4.11725 5.04499 4.04153 5.12702 4.01258 5.23188C4 5.27744 4 5.33338 4 5.44525V17.5548C4 17.6666 4 17.7226 4.01258 17.7681C4.04153 17.873 4.11725 17.955 4.21404 17.9864C4.2561 18 4.30773 18 4.411 18H13.589C13.6923 18 13.7439 18 13.786 17.9864C13.8828 17.955 13.9585 17.873 13.9874 17.7681C14 17.7226 14 17.6666 14 17.5548V5.44525C14 5.33338 14 5.27744 13.9874 5.23188C13.9585 5.12702 13.8828 5.04499 13.786 5.01363C13.7439 5 13.6923 5 13.589 5H4.411Z'
                        fill='currentColor'
                    />
                </g>
            </svg>
        </SvgIcon>
    )
}
