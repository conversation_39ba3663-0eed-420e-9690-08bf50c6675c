import { SvgIcon } from '@mui/material'

export function FloatRight({ ...props }) {
    return (
        <SvgIcon>
            <svg width='26' height='26' viewBox='0 0 30 26' fill='none' xmlns='http://www.w3.org/2000/svg'>
                <g filter='url(#filter0_d_2_31)'>
                    <path
                        d='M26 0.3C26 0.134314 25.8657 0 25.7 0H4.3C4.13431 0 4 0.134315 4 0.3V2.7C4 2.86569 4.13431 3 4.3 3H25.7C25.8657 3 26 2.86569 26 2.7V0.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M26 20.3C26 20.1343 25.8657 20 25.7 20H4.3C4.13431 20 4 20.1343 4 20.3V22.7C4 22.8657 4.13431 23 4.3 23H25.7C25.8657 23 26 22.8657 26 22.7V20.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M14 5.3C14 5.13431 13.8657 5 13.7 5H4.3C4.13431 5 4 5.13431 4 5.3V7.7C4 7.86569 4.13431 8 4.3 8H13.7C13.8657 8 14 7.86569 14 7.7V5.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M14 10.3C14 10.1343 13.8657 10 13.7 10H4.3C4.13431 10 4 10.1343 4 10.3V12.7C4 12.8657 4.13431 13 4.3 13H13.7C13.8657 13 14 12.8657 14 12.7V10.3Z'
                        fill='currentColor'
                    />
                    <path
                        d='M14 15.3C14 15.1343 13.8657 15 13.7 15H4.3C4.13431 15 4 15.1343 4 15.3V17.7C4 17.8657 4.13431 18 4.3 18H13.7C13.8657 18 14 17.8657 14 17.7V15.3Z'
                        fill='currentColor'
                    />
                    <path
                        fillRule='evenodd'
                        clipRule='evenodd'
                        d='M17.1111 6.62707C17.1111 6.5152 17.1111 6.45926 17.1237 6.4137C17.1526 6.30884 17.2284 6.2268 17.3252 6.19544C17.3672 6.18182 17.4188 6.18182 17.5221 6.18182H24.4779C24.5812 6.18182 24.6328 6.18182 24.6748 6.19544C24.7716 6.2268 24.8474 6.30884 24.8763 6.4137C24.8889 6.45926 24.8889 6.5152 24.8889 6.62707V16.3729C24.8889 16.4848 24.8889 16.5407 24.8763 16.5863C24.8474 16.6912 24.7716 16.7732 24.6748 16.8046C24.6328 16.8182 24.5812 16.8182 24.4779 16.8182H17.5221C17.4188 16.8182 17.3672 16.8182 17.3252 16.8046C17.2284 16.7732 17.1526 16.6912 17.1237 16.5863C17.1111 16.5407 17.1111 16.4848 17.1111 16.3729V6.62707ZM25.589 5C25.6923 5 25.7439 5 25.786 5.01363C25.8828 5.04499 25.9585 5.12702 25.9874 5.23188C26 5.27744 26 5.33338 26 5.44525V17.5548C26 17.6666 26 17.7226 25.9874 17.7681C25.9585 17.873 25.8828 17.955 25.786 17.9864C25.7439 18 25.6923 18 25.589 18H16.411C16.3077 18 16.2561 18 16.214 17.9864C16.1172 17.955 16.0415 17.873 16.0126 17.7681C16 17.7226 16 17.6666 16 17.5548V5.44525C16 5.33338 16 5.27744 16.0126 5.23188C16.0415 5.12702 16.1172 5.04499 16.214 5.01363C16.2561 5 16.3077 5 16.411 5H25.589Z'
                        fill='currentColor'
                    />
                </g>
            </svg>
        </SvgIcon>
    )
}
