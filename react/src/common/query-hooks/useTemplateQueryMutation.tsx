import { useQuery } from '@tanstack/react-query'
import { contentService } from '../../pkgs/content/content.service'
import { templateQueryKey } from '../constants'

function getTemplateClassificationURLParams(classifications: string[] | undefined, type: string | undefined) {
    const p = new URLSearchParams()
    classifications?.forEach((c) => p.append('classification', c))

    if (type) p.append('type', type)
    return p
}

export default function useTemplateQueryMutation(classifications?: string[], type?: string) {
    const { data, error, refetch } = useQuery({
        queryKey: [templateQueryKey, classifications, type],
        queryFn: async () => {
            const templates = await contentService.getTemplates(classifications, type)
            return Array.isArray(templates) ? (templates as any[]) : []
        }
    })

    return { data, error, refetch }
}
