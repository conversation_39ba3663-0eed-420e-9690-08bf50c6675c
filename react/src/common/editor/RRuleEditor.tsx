import React, { useState, useEffect } from 'react'
import {
    <PERSON>alog,
    DialogTitle,
    DialogContent,
    DialogActions,
    Button,
    Select,
    MenuItem,
    FormControl,
    RadioGroup,
    Radio,
    FormControlLabel,
    TextField,
    ToggleButton,
    ToggleButtonGroup,
    Box,
    Typography,
    FormLabel,
    Stack
} from '@mui/material'
import { RRule, Frequency, Weekday } from 'rrule'
import moment from 'moment'
import { generateRRuleOptions, getOrdinalSuffix, getMomentDate, EventSchedule } from './rruleUtils'
import { useTimezone } from '@/common/editor/useTimezone'
import CMDialog from '../components/CMDialog'

interface RRuleEditorProps {
    open: boolean
    onClose: () => void
    value: EventSchedule
    onChange: (value: EventSchedule) => void
}

// "Custom" rrule editor
export function RRuleEditor({ open, onClose, value, onChange }: RRuleEditorProps) {
    const [frequency, setFrequency] = useState<Frequency>(Frequency.MONTHLY)
    const [interval, setInterval] = useState<number>(1)
    const [weekdays, setWeekdays] = useState<number[]>([])
    const [monthOption, setMonthOption] = useState<'dayOfMonth' | 'dayOfWeek'>('dayOfMonth')
    const [endType, setEndType] = useState<'never' | 'on' | 'after'>('never')
    const [endDate, setEndDate] = useState<string>(moment().add({ years: 1 }).format('YYYY-MM-DD'))
    const [occurrences, setOccurrences] = useState<number>(5)

    const timezone = useTimezone()

    useEffect(() => {
        if (value.rrule) {
            try {
                const rrule = RRule.fromString(value.rrule)
                setFrequency(rrule.options.freq || RRule.MONTHLY)
                setInterval(rrule.options.interval || 1)
                const weekdayValues =
                    rrule.options.byweekday?.map((d: number | Weekday) => {
                        if (typeof d === 'number') return d + 1
                        return (d as Weekday).weekday
                    }) || []
                setWeekdays(weekdayValues)

                if (rrule.options.until) {
                    setEndType('on')
                    setEndDate(moment(rrule.options.until).format('YYYY-MM-DD'))
                } else if (rrule.options.count) {
                    setEndType('after')
                    setOccurrences(rrule.options.count)
                } else {
                    setEndType('never')
                }
            } catch (e) {
                console.error('Error parsing RRule:', e)
            }
        }
    }, [value.rrule])

    const handleSave = () => {
        if (!value.startdate || !timezone) return

        const options = generateRRuleOptions(
            frequency,
            interval,
            weekdays,
            monthOption,
            endType,
            endDate,
            occurrences,
            value.startdate,
            value.isAllDay,
            timezone
        )

        const rrule = new RRule(options)
        onChange({
            ...value,
            rrule: rrule.toString()
        })
        onClose()
    }

    if (!value.startdate || !timezone) return null

    // Get properly timezone-adjusted moment date
    const momentDate = getMomentDate(value.startdate || '', timezone, value.isAllDay)

    return (
        <CMDialog open={open} onClose={onClose} maxWidth='sm' fullWidth title='Custom Recurrence' showCloseButton>
            <DialogContent>
                <Box>
                    {/* Repeat frequency */}
                    <Box display='flex' gap={2}>
                        <FormLabel>Repeat&nbsp;every</FormLabel>
                        <TextField
                            type='number'
                            value={interval}
                            onChange={(e) => setInterval(Number(e.target.value))}
                            inputProps={{ min: 1 }}
                            size='small'
                        />
                        <Select
                            value={frequency}
                            onChange={(e) => setFrequency(Number(e.target.value))}
                            size='small'
                            renderValue={(selected) => getFrequencyLabel(interval, selected as Frequency)}
                        >
                            <MenuItem value={Frequency.DAILY}>{getFrequencyLabel(interval, Frequency.DAILY)}</MenuItem>
                            <MenuItem value={Frequency.WEEKLY}>
                                {getFrequencyLabel(interval, Frequency.WEEKLY)}
                            </MenuItem>
                            <MenuItem value={Frequency.MONTHLY}>
                                {getFrequencyLabel(interval, Frequency.MONTHLY)}
                            </MenuItem>
                            <MenuItem value={Frequency.YEARLY}>
                                {getFrequencyLabel(interval, Frequency.YEARLY)}
                            </MenuItem>
                        </Select>
                    </Box>

                    {/* Weekly options */}
                    {frequency === RRule.WEEKLY && (
                        <Box>
                            <Typography gutterBottom>Repeat on</Typography>
                            <ToggleButtonGroup
                                color={'primary'}
                                value={weekdays}
                                onChange={(_, newWeekdays) => setWeekdays(newWeekdays)}
                                exclusive={false}
                            >
                                <ToggleButton value={0}>Sun</ToggleButton>
                                <ToggleButton value={1}>Mon</ToggleButton>
                                <ToggleButton value={2}>Tue</ToggleButton>
                                <ToggleButton value={3}>Wed</ToggleButton>
                                <ToggleButton value={4}>Thu</ToggleButton>
                                <ToggleButton value={5}>Fri</ToggleButton>
                                <ToggleButton value={6}>Sat</ToggleButton>
                            </ToggleButtonGroup>
                        </Box>
                    )}

                    {/* Monthly options */}
                    {frequency === RRule.MONTHLY && (
                        <Box>
                            <RadioGroup
                                value={monthOption}
                                onChange={(e) => setMonthOption(e.target.value as 'dayOfMonth' | 'dayOfWeek')}
                            >
                                <FormControlLabel
                                    value='dayOfMonth'
                                    control={<Radio />}
                                    label={`Monthly on day ${momentDate.date()}`}
                                />
                                <FormControlLabel
                                    value='dayOfWeek'
                                    control={<Radio />}
                                    label={`Monthly on the ${getOrdinalSuffix(
                                        Math.ceil(momentDate.date() / 7)
                                    )} ${momentDate.format('dddd')}`}
                                />
                            </RadioGroup>
                        </Box>
                    )}

                    {/* End options */}
                    <Box>
                        <Typography gutterBottom variant='h6' sx={{ py: 1 }}>
                            Ends
                        </Typography>
                        <RadioGroup
                            value={endType}
                            onChange={(e) => setEndType(e.target.value as 'never' | 'on' | 'after')}
                        >
                            <FormControlLabel value='never' control={<Radio />} label='Never' />
                            <FormControlLabel
                                value='on'
                                control={<Radio />}
                                label={
                                    <FormControl component='fieldset' sx={{ ml: 1 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                            <Typography>On</Typography>
                                            <TextField
                                                type='date'
                                                value={endDate}
                                                onChange={(e) => setEndDate(e.target.value)}
                                                size='small'
                                                disabled={endType !== 'on'}
                                                sx={{ minWidth: '200px' }}
                                            />
                                        </Box>
                                    </FormControl>
                                }
                            />
                            <FormControlLabel
                                value='after'
                                control={<Radio />}
                                label={
                                    <FormControl component='fieldset' sx={{ ml: 1 }}>
                                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                            <Typography>After</Typography>
                                            <TextField
                                                type='number'
                                                value={occurrences}
                                                onChange={(e) => setOccurrences(Number(e.target.value))}
                                                size='small'
                                                disabled={endType !== 'after'}
                                                inputProps={{ min: 1 }}
                                                sx={{ width: '80px' }}
                                            />
                                            <Typography>occurrences</Typography>
                                        </Box>
                                    </FormControl>
                                }
                            />
                        </RadioGroup>
                    </Box>
                </Box>
            </DialogContent>
            <DialogActions>
                <Button onClick={onClose}>Cancel</Button>
                <Button onClick={handleSave} variant='contained'>
                    Done
                </Button>
            </DialogActions>
        </CMDialog>
    )
}

function getFrequencyLabel(interval: number, frequency: Frequency) {
    switch (frequency) {
        case Frequency.DAILY:
            return `Day${interval > 1 ? 's' : ''}`
        case Frequency.WEEKLY:
            return `Week${interval > 1 ? 's' : ''}`
        case Frequency.MONTHLY:
            return `Month${interval > 1 ? 's' : ''}`
        case Frequency.YEARLY:
            return `Year${interval > 1 ? 's' : ''}`
        default:
            return ''
    }
}
