import { Frequency, Options, RRule, Weekday } from 'rrule'
import moment from 'moment'

export function getMomentDate(startdate: string, timezone: string, isAllDay: boolean) {
    return moment(startdate).tz(isAllDay ? 'UTC' : timezone)
}

export function getOrdinalSuffix(n: number): string {
    const s = ['th', 'st', 'nd', 'rd']
    const v = n % 100
    return n + (s[(v - 20) % 10] || s[v] || s[0])
}

export function mapWeekday(weekday: number): Weekday {
    const weekdayMap = {
        0: RRule.SU,
        1: RRule.MO,
        2: RRule.TU,
        3: RRule.WE,
        4: RRule.TH,
        5: RRule.FR,
        6: RRule.SA
    }
    return weekdayMap[weekday as keyof typeof weekdayMap] || RRule.SU
}

export function getDayConstant(day: number): string {
    const days = ['SU', 'MO', 'TU', 'WE', 'TH', 'FR', 'SA']
    return days[day]
}

export function generateRRuleOptions(
    frequency: Frequency,
    interval: number,
    weekdays: number[],
    monthOption: 'dayOfMonth' | 'dayOfWeek',
    endType: 'never' | 'on' | 'after',
    endDate: string,
    occurrences: number,
    startdate: string,
    isAllDay: boolean,
    timezone: string
) {
    const options: Partial<Options> = {
        freq: frequency,
        interval: interval === 1 ? undefined : interval
    }

    const momentDate = getMomentDate(startdate, timezone, isAllDay)

    if (frequency === RRule.WEEKLY && weekdays.length > 0) {
        options.byweekday = weekdays.map(mapWeekday)
    }

    if (frequency === RRule.MONTHLY) {
        if (monthOption === 'dayOfMonth') {
            options.bymonthday = [momentDate.date()]
        } else {
            const nthWeekday = Math.ceil(momentDate.date() / 7)
            options.byweekday = [mapWeekday(momentDate.day()).nth(nthWeekday)]
        }
    }

    if (endType === 'on') {
        options.until = moment(endDate).toDate()
    } else if (endType === 'after') {
        options.count = occurrences
    }

    return options
}

export function generateDefaultOptions(startdate: string, timezone: string, isAllDay: boolean) {
    const momentDate = getMomentDate(startdate, timezone, isAllDay)
    const weekday = momentDate.day()
    const dayOfMonth = momentDate.date()
    const month = momentDate.month()
    const nthWeekday = Math.ceil(dayOfMonth / 7)

    const weekdayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const monthNames = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December'
    ]

    const baseOptions = [
        {
            label: "Doesn't repeat",
            value: ''
        },
        {
            label: 'Daily',
            value: new RRule({ freq: RRule.DAILY }).toString()
        },
        {
            label: `Weekly on ${weekdayNames[weekday]}`,
            value: new RRule({
                freq: RRule.WEEKLY,
                byweekday: [mapWeekday(weekday)]
            }).toString()
        },
        {
            label: `Monthly on the ${getOrdinalSuffix(nthWeekday)} ${weekdayNames[weekday]}`,
            value: new RRule({
                freq: RRule.MONTHLY,
                byweekday: [mapWeekday(weekday).nth(nthWeekday)]
            }).toString()
        },
        {
            label: `Annually on ${monthNames[month]} ${dayOfMonth}`,
            value: new RRule({
                freq: RRule.YEARLY,
                bymonth: [month + 1],
                bymonthday: [dayOfMonth]
            }).toString()
        }
    ]

    if (weekday >= 1 && weekday <= 5) {
        baseOptions.push({
            label: 'Every weekday (Monday to Friday)',
            value: new RRule({
                freq: RRule.WEEKLY,
                byweekday: [RRule.MO, RRule.TU, RRule.WE, RRule.TH, RRule.FR]
            }).toString()
        })
    }

    return baseOptions
}

export interface EventSchedule {
    startdate: string | null
    enddate: string | null
    isAllDay: boolean
    rrule?: string
    exdate?: string[]
    rdate?: string[]
}
