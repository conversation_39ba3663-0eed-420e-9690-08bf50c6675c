import { Box, Typography } from '@mui/material'
import { colours } from '../colours'

export default function HighlightedText({ text }: { text: string }) {
    return (
        <Box
            display='flex'
            sx={{
                justifyContent: 'center',
                backgroundColor: colours.light_warning,
                borderWidth: '2px',
                borderStyle: 'solid',
                borderColor: colours.warning
            }}
        >
            <Typography padding='4px' color={colours.warning}>
                {text}
            </Typography>
        </Box>
    )
}
