import { Dialog, DialogProps, DialogTitle, SxProps, Typography } from '@mui/material'
import { forwardRef, ReactNode } from 'react'
import CustomIconButton from './CustomIconButton'
import CloseIcon from '@mui/icons-material/Close'
import { colours } from '../colours'

export interface CMDialogProps extends Omit<DialogProps, 'title'> {
    children: any | any[]
    title?: ReactNode | string
    width?: string
    height?: string
    showCloseButton?: boolean
}

// For <DialogTitle/> to be sticky, <DialogContent/> must also be used
const CMDialog = forwardRef(
    ({ title, width, height, showCloseButton, children, ...dialogProps }: CMDialogProps, ref) => {
        dialogProps.sx = {
            '& .MuiDialogActions-root': {
                borderTop: `1px solid ${colours.off_white_but_darker}`
            },
            ...dialogProps.sx
        }

        return (
            <Dialog
                PaperProps={{
                    sx: {
                        width: width,
                        height: height
                    }
                }}
                {...dialogProps}
            >
                {title && (
                    <DialogTitle sx={{ borderBottom: `1px solid ${colours.off_white_but_darker}` }}>
                        {title}
                    </DialogTitle>
                )}
                {showCloseButton && (
                    <CustomIconButton
                        aria-label='close'
                        // @ts-ignore
                        onClick={(event, reason) => {
                            dialogProps?.onClose?.(event, reason)
                        }}
                        sx={{
                            position: 'absolute',
                            right: 8,
                            top: 8,
                            color: (theme) => theme.palette.grey[500]
                        }}
                    >
                        <CloseIcon />
                    </CustomIconButton>
                )}
                {children}
            </Dialog>
        )
    }
)

export default CMDialog
