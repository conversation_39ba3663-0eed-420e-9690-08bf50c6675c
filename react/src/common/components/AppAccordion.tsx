import { Accordion, AccordionDetails, AccordionSummary, alpha, IconButton } from '@mui/material'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { CSSProperties, ReactNode, useRef, useState } from 'react'
import { colours } from '../colours'
import { CustomMenu, CustomMenuItem } from './custom-context-menu/CustomMenu'
import UnfoldLessIcon from '@mui/icons-material/UnfoldLess'
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore'
import CustomIconButton from './CustomIconButton'
import { mainBorderRadius } from '@/app/theme'

interface AppAccordion {
    summary: ReactNode
    details: JSX.Element
    expanded?: boolean // controller
    defaultExpanded?: boolean
    onChangeHandler?: (expanded: boolean) => void
    allOnChangeHandler?: (expanded: boolean) => void
    styles?: CSSProperties
    withoutPadding?: boolean
    unmountOnExit?: boolean
}

export function AppAccordion({
    expanded,
    defaultExpanded,
    onChangeHandler,
    allOnChangeHandler,
    summary,
    details,
    styles,
    withoutPadding,
    unmountOnExit
}: AppAccordion) {
    const accordionExpandIconButton = useRef(null)
    const [accordionMenuIsOpen, setAccordionMenuIsOpen] = useState(false)

    return (
        <Accordion
            TransitionProps={{
                unmountOnExit: unmountOnExit
            }}
            defaultExpanded={defaultExpanded === undefined ? true : defaultExpanded}
            expanded={expanded}
            onChange={(ev, expanded) => onChangeHandler?.(expanded)}
            sx={{
                '&.MuiAccordion-root': {
                    marginTop: '0.75em',
                    borderRadius: mainBorderRadius,
                    ...styles
                },
                marginBottom: '0.75em',
                border: `1px solid ${colours.topbar}`,
                borderRadius: mainBorderRadius,
                ...styles
            }}
        >
            <AccordionSummary
                data-testid='app-accordion-summary'
                expandIcon={
                    allOnChangeHandler ? (
                        <CustomIconButton
                            round
                            data-testid='expand-collapse-all-accordions'
                            ref={accordionExpandIconButton}
                            onClick={(e) => {
                                e.stopPropagation()
                                setAccordionMenuIsOpen(!accordionMenuIsOpen)
                            }}
                            sx={{
                                border: `1px solid ${alpha(colours.topbar, 0.1)}`
                            }}
                        >
                            <ExpandMoreIcon />
                        </CustomIconButton>
                    ) : (
                        <ExpandMoreIcon />
                    )
                }
                sx={{
                    '&:hover': {
                        backgroundColor: alpha(colours.base_blue, 0.25),
                        borderRadius: `${mainBorderRadius} ${mainBorderRadius} 0px 0px`
                    },
                    '&[aria-expanded=false]': {
                        borderBottom: 'none'
                    },
                    borderBottom: `1px solid ${colours.topbar}`
                }}
            >
                {summary}
                {accordionMenuIsOpen && (
                    <CustomMenu
                        data-testid='expand-collapse-all-accordions-menu'
                        anchorElement={accordionExpandIconButton?.current}
                        onClose={(e) => {
                            e.stopPropagation()
                            setAccordionMenuIsOpen(false)
                        }}
                    >
                        <CustomMenuItem
                            text='Collapse All'
                            onClick={(e) => {
                                e.stopPropagation()
                                allOnChangeHandler?.(false)
                                setAccordionMenuIsOpen(false)
                            }}
                        >
                            <UnfoldLessIcon />
                        </CustomMenuItem>
                        <CustomMenuItem
                            text='Expand All'
                            onClick={(e) => {
                                e.stopPropagation()
                                allOnChangeHandler?.(true)
                                setAccordionMenuIsOpen(false)
                            }}
                        >
                            <UnfoldMoreIcon />
                        </CustomMenuItem>
                    </CustomMenu>
                )}
            </AccordionSummary>
            <AccordionDetails
                sx={{
                    padding: withoutPadding ? 0 : undefined,
                    borderRadius: `${mainBorderRadius} ${mainBorderRadius} 0px 0px`,
                    '&[aria-expanded=false]': {
                        border: 'none'
                    }
                }}
            >
                {details}
            </AccordionDetails>
        </Accordion>
    )
}

export default AppAccordion
