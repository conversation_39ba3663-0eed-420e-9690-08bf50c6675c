{"name": "content-manager", "version": "2024.6.1", "private": true, "type": "module", "dependencies": {"@ckeditor/ckeditor5-build-classic": "^22.0.0", "@ckeditor/ckeditor5-react": "^5.0.5", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@floating-ui/react-dom": "^2.0.8", "@lexical/react": "^0.17.0", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^5.11.0", "@mui/lab": "^5.0.0-alpha.113", "@mui/material": "^5.11.1", "@mui/styles": "^5.11.1", "@mui/x-data-grid": "^6.16.2", "@mui/x-date-pickers": "^6.16.2", "@react-google-maps/api": "^2.20.6", "@tanstack/react-query": "^4.16.1", "axios": "^0.22.0", "browser-image-compression": "1.0.12", "cropperjs": "^1.6.2", "date-fns": "^2.28.0", "dompurify": "^3.1.6", "flexboxgrid": "^6.3.1", "frontend-collective-react-dnd-scrollzone": "1.0.2", "ie-ckeditor5": "^1.0.15", "jotai": "^2.1.1", "lexical": "^0.17.0", "lodash": "^4.17.21", "match-sorter": "^6.3.1", "moment": "^2.29.4", "moment-timezone": "^0.5.38", "monaco-editor": "^0.45.0", "ndanvers-react-sortable-tree": "^2021.11.4", "papaparse": "^5.4.1", "react": "^18.2.0", "react-cropper": "^2.3.3", "react-datepicker": "^3.3.0", "react-dom": "^18.2.0", "react-dropzone": "^11.0.1", "react-error-boundary": "^4.0.13", "react-lazyload": "^3.0.0", "react-markdown": "^9.0.0", "react-router-dom": "^6.6.1", "react-toastify": "^9.1.3", "react-virtualized": "^9.21.2", "rehype-raw": "^7.0.0", "rrule": "^2.8.1", "rxjs": "^6.5.5", "styled-components": "^5.2.0", "typescript": "^5.0.2", "uuid": "^8.2.0", "zod": "^3.19.1"}, "scripts": {"postinstall": "patch-package", "start": "vite", "format": "prettier --write ./src", "lint": "eslint --fix ./src", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ci": "vitest run --passWithNoTests"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@anatine/zod-mock": "^3.13.4", "@faker-js/faker": "^8.4.1", "@rollup/plugin-commonjs": "^26.0.1", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^7.2.1", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.0", "@types/lodash": "^4.14.192", "@types/node": "^18.15.3", "@types/react": "^18.3.2", "@types/react-dom": "^18.3.0", "@types/uuid": "^9.0.1", "@typescript-eslint/eslint-plugin": "^7.16.1", "@typescript-eslint/parser": "^7.16.1", "@vitejs/plugin-react": "^4.3.1", "clsx": "^1.1.1", "css-loader": "^4.1.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-react": "^7.34.4", "file-loader": "^6.0.0", "fork-ts-checker-webpack-plugin": "^8.0.0", "html-loader": "^1.1.0", "jsdom": "^24.1.0", "patch-package": "^6.5.0", "postcss": "^8.4.18", "prettier": "^3.1.0", "raw-loader": "^4.0.1", "vite": "^5.3.2", "vite-plugin-md": "^0.22.5", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.6.0", "webpack-cli": "^3.3.12"}, "homepage": "/"}