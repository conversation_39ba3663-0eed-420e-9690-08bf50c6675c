services:
  cm-admin-ui:
    image: 793554545599.dkr.ecr.ca-central-1.amazonaws.com/cm-admin-ui:${COMMIT_SHA}
    depends_on:
      - cm-api
    env_file:
      - e2e.env

  cm-api:
    image: 793554545599.dkr.ecr.ca-central-1.amazonaws.com/cm-api:${COMMIT_SHA}
    depends_on:
      migrator:
        condition: service_completed_successfully
    env_file:
      - e2e.env

  cm-public:
    image: 793554545599.dkr.ecr.ca-central-1.amazonaws.com/cm-public:${COMMIT_SHA}
    depends_on:
      migrator:
        condition: service_completed_successfully
    env_file:
      - e2e.env

  migrator:
    image: e2e-migrator:latest
    depends_on:
      postgres:
        condition: service_healthy
    env_file:
      - e2e.env
    deploy:
      restart_policy:
        condition: none

  postgres:
    restart: unless-stopped
    image: postgres:14.1-alpine
    environment:
      POSTGRES_DB: cm_multitenancy
      TZ: America/Vancouver
    ports:
      - "5432:5432"
    env_file:
      - e2e.env
    healthcheck:
      test: [ "CMD-SHELL", "pg_isready -U postgres" ]
      interval: 5s
      timeout: 5s
      retries: 10

  caddy:
    image: caddy:2.7.6-alpine
    restart: unless-stopped
    ports:
      - "80:80"
      - "2019:2019"
      - "443:443"
      - "443:443/udp"
    volumes:
      - ../e2e/Caddyfile:/etc/caddy/Caddyfile
