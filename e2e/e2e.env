CM_E2E=1

PGHOST=postgres
PGUSER=postgres
POSTGRES_USER=postgres
PGPASSWORD=4y7sV96vA9wv46VR
POSTGRES_PASSWORD=4y7sV96vA9wv46VR

CM_DBHOST=postgres
CM_DBPORT=5432
CM_DBSERVER=cm_multitenancy
CM_DBUSER=contentmanager_application_user
CM_DBPASSWORD=4y7sV96vA9wv46VR
CM_AdminSiteUrl=contentmanager.imagineeverything.ca.localhost
CM_COOKIENAME=contentmanager_app
CM_COOKIEKEY=61d1b480caec11e8a8d5f2801f1b9fd1
CM_COOKIEEXPIRY=3600
CM_DEBUG=true
CM_PutObjectInput_BUCKET=contentmanagement-production/
CM_BUCKET=contentmanagement-production/
CM_AWS_BUCKET=contentmanagement-production/
CM_AWSBUCKET=contentmanagement-production/
CM_MailUser=TBD
CM_MailPassword=TBD
CM_MailHost=TBD
CM_MSOAuthBaseUrl=https://login.microsoft.com
CM_MSOAuthOrgConsent=/organizations/v2.0/adminconsent
CM_MSOAuthOrgConsentRedirect=TBD
CM_MSOAuthRedirectLink=TBD
CM_MSOAuthScope=TBD
CM_MSOAuthTenantID=TBD
CM_MSOAuthApplicationID=TBD
CM_MSOAuthClientSecret=TBD
CM_FBClientId=TBD
CM_FBSecretKey=TBD
CM_FBRedirectURI=TBD


