#!/bin/bash

export PGHOST=${PGHOST:-postgres}
export PGUSER=${PGUSER}
export PGPASSWORD=${PGPASSWORD}

RETRIES=20
SLEEP_INTERVAL=3

echo "Waiting for database to be available..."

for ((i=1; i<=RETRIES; i++)); do
  if psql -c '\q'; then
    echo "Database is available!"
    break
  else
    echo "Database is unavailable - retrying in $SLEEP_INTERVAL seconds ($i/$RETRIES)"
    sleep $SLEEP_INTERVAL
  fi
done

if (( i > RETRIES )); then
  echo "Failed to connect to the database after $((RETRIES * SLEEP_INTERVAL)) seconds."
  exit 1
fi

sleep 5

# Creating user and databases
echo "Creating user and databases..."

ensure_user_exists() {
    local username=$1
    local password=$2
    # Check if the user exists
    USER_EXIST=$(psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='${username}'" | wc -l)

    # If the user does not exist, create it
    if [ "$USER_EXIST" != "1" ]; then
        echo "User ${username} does not exist. Creating it..."
        psql -c "CREATE USER ${username} WITH PASSWORD '${password}'"
    fi
}

ensure_user_exists "contentmanager_application_user" "$PGPASSWORD"

ensure_database_exists() {
    local db_name=$1
    # Check if the database exists
    DB_EXIST=$(psql -tAc "SELECT 1 FROM pg_database WHERE datname='${db_name}'" | wc -l)

    # If the database does not exist, create it
    if [ "$DB_EXIST" != "1" ]; then
        echo "Database ${db_name} does not exist. Creating it..."
        psql -c "CREATE DATABASE ${db_name}"
    fi
}

ensure_database_exists "cm_multitenancy"
ensure_database_exists "cm_tenant_1"
ensure_database_exists "cm_tenant_2"

echo "User and databases created successfully."

deploy_database() {
  local db_name=$1
  local deploy_dir=$2

  echo "Deploying to database: $db_name"
  sqitch --chdir "$deploy_dir" deploy --verify --target "db:pg://${PGUSER}:${PGPASSWORD}@postgres/$db_name"
  if [ $? -ne 0 ]; then
    echo "Deployment failed for database: $db_name"
    exit 1
  else
    echo "Deployment successful for database: $db_name"
  fi
}

deploy_database "cm_multitenancy" "./database/src/multitenancy/Deploy"

tenants='cm_tenant_1 cm_tenant_2'
for tenant in $tenants; do
  sleep 5
  deploy_database "$tenant" "./database/src/tenant/Deploy"
done

for tenant in $tenants; do
  sleep 5
  deploy_database "$tenant" "./database/src/tenant/Rebase"
done
