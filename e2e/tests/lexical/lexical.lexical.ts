import { expect, test } from '@playwright/test'
import { adminSiteURL } from '../../constants'

test('Lexical page contains YouTube video and Strategic Goals', async ({ page }) => {
    test.setTimeout(20000)

    const consoleErrors: string[] = []
    page.on('console', msg => {
        if (msg.type() === 'error' && !(msg.text().includes('401') || msg.text().includes('Failed to load resource'))) {
            consoleErrors.push(msg.text())
        }
    })

    await page.goto(adminSiteURL + '/lexical-testing')


    // Check for the YouTube video
    const youtubeVideo = await page.$(`iframe[src*="0daokK3spIg"]`)
    expect(youtubeVideo).not.toBeNull()

    // Check for the element with text "Strategic Goals"
    const strategicGoalsElement = await page.locator('text=Strategic Goals').first()
    expect(await strategicGoalsElement.isVisible()).toBeTruthy()

    // Check for there are no console errors (except 401 errors)
    expect(consoleErrors).toHaveLength(0)
})
