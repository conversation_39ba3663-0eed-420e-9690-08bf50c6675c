import { test } from '@playwright/test'
import { signInTenant1Admin } from '../utils/auth-utils'
import { addRole } from '../utils/add-role'
import { addGroup } from '../utils/add-group'
import { addUser } from '../utils/add-user'

test('Add Tenant User', async ({ page }) => {
    test.setTimeout(30000)

    await signInTenant1Admin(page)

    // Navigate to the Roles page
    await page.getByRole('button', { name: 'User Management' }).click()

    // Create Tenant Role
    await page.getByRole('button', { name: 'Roles' }).click()
    await addRole(page, {
        name: 'Tenant Role',
        type: 'tenant',
        scopes: ['cm.content.page/*', 'cm.fragment*/*', 'cm.structure*/*', 'cm.tag/*', 'cm.transportation*', 'cm.image/*', 'cm.document*/*', 'cm.navigation*', 'cm.list*', 'cm.settings*']
    })

    // Create Tenant Group
    await page.getByRole('button', { name: 'Security Groups' }).click()
    await addGroup(page, {
        name: 'Tenant Group',
        isDistrictWide: true,
        roleName: 'Tenant Role'
    })

    // Create Tenant Account
    await page.getByRole('button', { name: 'Accounts' }).click()
    await addUser(page, {
        domain: 'site-1.tenant-1.localhost',
        userName: 'cm_tenant',
        groupName: 'Tenant Group'
    })
})
