import {Page} from '@playwright/test'
import {closeAllToasts} from "./toasts";

/// <reference path="../../../react/src/pkgs/auth/scopes.ts" />
export async function addRole(page: Page, params: {
    name: string,
    type: 'site' | 'tenant',
    scopes: string[],
}) {
    const editSelector = `edit-role-${params.name.replace(/\s/g, '-')}`
    const scopes = params.scopes.map(scope => `scope-${scope.replace(/([.*/])/g, '_')}`)

    await page.getByRole('button', {name: 'ADD ROLE'}).click();

    await page.fill('[data-testid="create-role-name"] input', params.name);

    await page.getByLabel(params.type).check();

    await page.getByTestId('dialog-agree').click();
    // await page.getByRole('button', {name: 'Create'}).click();

    await page.getByTestId(editSelector).click();

    for (const scope of scopes) {
        await page.getByTestId(scope).getByRole('checkbox').check();
    }

    await page.getByRole('button', {name: 'Save'}).click();

    await closeAllToasts(page);
    await page.getByRole('button', {name: 'Back'}).click();
}
