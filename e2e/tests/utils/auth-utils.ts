import { Page } from '@playwright/test'
import { closeAllToasts } from './toasts'
import { adminSiteURL } from '../../constants'

export async function signIn(page: Page, email: string, password?: string) {
    password = password || '12qwaszx'
    await page.goto(adminSiteURL)

    await page.click('[data-testid="native-sign-in"]')
    await page.fill('[data-testid="email"] input', email)

    await page.fill('[data-testid="password"] input', password)

    await page.click('[data-testid="login"]')

    await closeAllToasts(page) // ??
    await page.waitForSelector('[data-testid="logout"]', { state: 'visible', timeout: 5000 })

    const openDrawerButton = page.locator('[data-testid="open-drawer"]')
    if (await openDrawerButton.isVisible()) {
        await openDrawerButton.click()
    }
}

export async function signInTenant1Admin(page: Page) {
    const email = '<EMAIL>'
    const password = '12qwaszx'
    for (let i = 0; i < 3; i++) {
        try {
            await signIn(page, email, password)
            return
        } catch (e) {
            console.log('Failed to sign in. Retrying...')
        }
    }
    await signIn(page, email, password)
}
