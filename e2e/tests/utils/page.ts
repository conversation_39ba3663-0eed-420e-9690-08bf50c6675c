import { expect, Page } from '@playwright/test'
import { closeAllToasts } from './toasts'
import {
    adminSiteURL,
    pageManagerActionMenuTestId,
    structureSelectorTestId,
    templateSelectorTestId
} from '../../constants'

export async function addPage(page: Page, params: {
    title: string,
    // sites: string[] | 'ALL', TODO: Handle site selector. Do it when site selector UI is updated.
    templateName: string,
    structureName: string
}) {
    await page.goto(`${adminSiteURL}/pages`)
    await page.getByRole('button', { name: 'ADD PAGE' }).click()
    await page.getByTestId('add-page-title').locator('input').fill(params.title)

    await page.getByTestId(templateSelectorTestId).click()
    await page.getByRole('option', { name: params.templateName }).click()
    await page.waitForTimeout(700)

    await page.getByTestId(structureSelectorTestId).click()
    await page.getByRole('option', { name: params.structureName }).click()
    await page.waitForTimeout(700)

    await page.getByRole('button', { name: 'CREATE' }).click()

    await closeAllToasts(page)
}

export async function deletePage(page: Page, params: {
    title: string,
}) {
    await page.goto(`${adminSiteURL}/pages`)
    await page.getByLabel('Search').fill(params.title)

    const rowDivXPath = `//a[contains(., '${params.title}')]/../../..`
    await page.waitForSelector(rowDivXPath)
    await page.locator(rowDivXPath).getByTestId('MoreHorizIcon').click()
    await page.getByTestId(pageManagerActionMenuTestId).getByText('Delete').click()
    await page.getByTestId('dialog-agree').click()
    await page.waitForTimeout(2000) // necessary
    expect(page.getByTestId('dialog-agree')).toHaveCount(0)
    expect(page.locator(rowDivXPath)).toHaveCount(0)
}
