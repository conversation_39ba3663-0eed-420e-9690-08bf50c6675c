import {Page} from '@playwright/test'
import {closeAllToasts} from "./toasts";

// Manual only, without any dependencies
export async function addGroup(page: Page, params: {
    name: string,
    roleName: string,
    isDistrictWide: boolean,
    siteName?: string
}) {
    await page.getByRole('button', {name: 'ADD GROUP'}).click();

    await page.getByLabel('Manual').check();

    await page.fill('[data-testid="group-name"] input', params.name);
    await page.fill('[data-testid="group-description"] input', `${params.name} description`);

    if (params.isDistrictWide) {
        await page.getByLabel('District Wide').check();
    } else {
        await page.getByTestId('single-site-select').click();
        await page.getByRole('option', {name: params.siteName}).click();
    }

    const roleName = `${params.roleName} (${params.isDistrictWide ? 'tenant' : 'site'})`
    await page.getByTestId('role-select').click();
    await page.getByRole('option', {name: roleName}).click();

    await page.getByLabel('Depends On Group IDs *').click();
    await page.getByLabel('Depends On Group IDs *').fill('some-random-sting');

    await page.getByRole('button', {name: 'Create'}).click();

    await closeAllToasts(page);
    await page.getByRole('button', {name: 'Back'}).click();
}
