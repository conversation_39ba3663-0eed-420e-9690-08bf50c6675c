import { defineConfig, devices, ViewportSize } from '@playwright/test'

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

const sharedUserAuthPath = '.auth/user.json'

const seconds = 1000
const minutes = 60 * seconds
const defaultViewport: ViewportSize = { width: 1800, height: 1000 }
/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
    timeout: 10 * minutes,
    testDir: '././',
    /* Run tests in files in parallel */
    fullyParallel: false,
    /* Fail the build on CI if you accidentally left test.only in the source code. */
    forbidOnly: !!process.env.CI,
    /* Retry on CI only */
    retries: process.env.CI ? 2 : 0,
    /* Opt out of parallel tests on CI. */
    workers: process.env.CI ? 1 : undefined,
    /* Reporter to use. See https://playwright.dev/docs/test-reporters */
    reporter: 'html',
    /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
    use: {
        /* Base URL to use in actions like `await page.goto('/')`. */
        // baseURL: 'http://127.0.0.1:3000',

        /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
        trace: 'on-first-retry',
        ignoreHTTPSErrors: true,
        viewport: defaultViewport,
        launchOptions: {
            slowMo: parseInt(process.env.SLOW_MO || '0')
        },
        screenshot: 'only-on-failure'
    },
    reportSlowTests: {
        max: 9999,
        threshold: 5 * minutes
    },

    /* Configure projects */
    projects: [
        { name: 'shared-account-setup', testMatch: '**/shared-account.setup.ts', use: { ignoreHTTPSErrors: true } },
        {
            name: 'lexical',
            use: { ...devices['Desktop Chrome'], viewport: defaultViewport },
            testMatch: '**/*.lexical.ts'
        },
        {
            name: 'lexical-shared',
            use: {
                ...devices['Desktop Chrome'],
                viewport: defaultViewport,
                storageState: sharedUserAuthPath,
                ignoreHTTPSErrors: true
            },
            testMatch: '**/*.lexical.shared.spec.ts',
            dependencies: ['shared-account-setup']
        },
        {
            name: 'admin',
            use: { ...devices['Desktop Chrome'], viewport: defaultViewport },
            testMatch: '**/*.admin.ts'
        },
        {
            name: 'cm_user',
            use: { ...devices['Desktop Chrome'], viewport: defaultViewport },
            testMatch: '**/*.cm_user.ts',
            dependencies: ['admin']
        }
    ]
})
