
FROM gcr.io/distroless/static-debian11:nonroot as go-server
ARG SERVER_NAME

COPY build_output/${SERVER_NAME} /start
COPY build_output/image_tags.txt /

USER nonroot:nonroot
CMD ["/start"]


FROM gcr.io/distroless/static-debian11:nonroot as spa-server

COPY build_output/spa-server /app/bin/spa-server
COPY build_output/react /app/static
COPY build_output/image_tags.txt /app/static

USER nonroot:nonroot
CMD ["/app/bin/spa-server"]
