package main

import (
	"github.com/aws/aws-lambda-go/events"
	"net/url"
	"sort"
	"strings"
	"testing"
)

type redirectTest struct {
	IncomingURL string
	RedirectTo  string
}

var tests = []redirectTest{
	{
		IncomingURL: "http://badlandsbaseballacademy.ca/about?foo=bar&baz=qux",
		RedirectTo:  "https://baseballacademy.myprps.com/about?foo=bar&baz=qux",
	},
	{
		IncomingURL: "http://www.ske.sd23.bc.ca",
		RedirectTo:  "https://ske.sd23.bc.ca",
	},
	{
		IncomingURL: "http://albertahomeeducation.ca/test/path",
		RedirectTo:  "https://centreforlearning.redeemer.ab.ca/test/path",
	},
	{
		IncomingURL: "http://www.albertahomeeducation.ca/test/path",
		RedirectTo:  "https://centreforlearning.redeemer.ab.ca/test/path",
	},
	{
		IncomingURL: "https://albertahomeeducation.ca",
		RedirectTo:  "https://centreforlearning.redeemer.ab.ca",
	},
	{
		IncomingURL: "https://sd23.bc.ca",
		RedirectTo:  "https://www.sd23.bc.ca",
	},
}

func Test_handleRequest(t *testing.T) {
	for incoming, redirectTo := range mappedHosts {
		tests = append(tests, redirectTest{
			IncomingURL: "https://" + incoming,
			RedirectTo:  "https://" + redirectTo,
		}, redirectTest{
			IncomingURL: "http://" + incoming,
			RedirectTo:  "https://" + redirectTo,
		})
	}
	for _, test := range tests {
		input := getRequest("GET", test.IncomingURL)
		result, err := handleRequest(nil, input)
		if err != nil {
			t.Fatal(err)
		}
		if result.StatusCode != 301 {
			t.Fatalf("Expected 301 but got %d", result.StatusCode)
		}

		if !equalsIgnoreOrder(result.Headers["Location"], test.RedirectTo) {
			t.Fatalf("Expected Location header to be %s but got %s", test.RedirectTo, result.Headers["Location"])
		}
		if result.Headers["X-Ie-App-Version"] != "lambda" {
			t.Fatalf("Expected X-Ie-App-Version header to be lambda but got %s", result.Headers["X-Ie-App-Version"])
		}
	}
}

func getRequest(method, link string) events.ALBTargetGroupRequest {
	u, _ := url.Parse(link)
	return events.ALBTargetGroupRequest{
		HTTPMethod:                      method,
		Path:                            u.Path,
		QueryStringParameters:           multiValueToParams(u.Query()),
		MultiValueQueryStringParameters: u.Query(),
		Headers: map[string]string{
			"host": u.Host,
		},
		MultiValueHeaders: map[string][]string{
			"host": {u.Host},
		},
	}
}

func multiValueToParams(value map[string][]string) map[string]string {
	params := make(map[string]string)
	for key, values := range value {
		for _, value := range values {
			params[key] = value
		}
	}
	return params
}

func sortString(s string) string {
	charArray := []rune(s)
	sort.Slice(charArray, func(i int, j int) bool {
		return charArray[i] < charArray[j]
	})
	return string(charArray)
}

func equalsIgnoreOrder(a, b string) bool {
	return strings.Compare(sortString(a), sortString(b)) == 0
}
