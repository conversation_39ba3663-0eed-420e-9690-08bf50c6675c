
variable "alb_names" {
   type = map
   default = { for alb_names in [ "CM-Redirect-ALB" ] : alb_names =>     alb_names }
}

variable "health_check" {
   type = map(string)
   default = {
      "timeout"  = "10"
      "interval" = "20"
      "path"     = "/"
      "port"     = "80"
      "unhealthy_threshold" = "2"
      "healthy_threshold" = "3"
    }
}

variable "security_grp" {
    type = list
    default = ["sg-2612244d", "sg-08bf3255660703c3e", "sg-0f599efdfc2022aa1"]
}

variable "subnets" {
    type = list
    default = ["subnet-04dc526c","subnet-b53de6cf"]
}