name: Docker Build and Push

on:
  workflow_dispatch:
    inputs:
      sha:
        required: true
        description: 'The SHA to build and push'

env:
  AWS_REGION: ca-central-1
  BUILD_ARGS_GOLANG: --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from type=local,src=/tmp/.buildx-cache --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max --push --build-arg IE_GIT_PAT=${{ secrets.IE_GIT_PAT }} -f build/docker/Dockerfile-golang
  BUILD_ARGS_REACT: --build-arg BUILDKIT_INLINE_CACHE=1 --cache-from type=local,src=/tmp/.buildx-cache --cache-to type=local,dest=/tmp/.buildx-cache-new,mode=max --push  -f build/docker/Dockerfile-react
  DOCKER_BUILDKIT: 1
  BUILDKIT_INLINE_CACHE: 1

jobs:
  prepare:
    runs-on: ubuntu-latest
    outputs:
      image_tags: ${{ steps.set_tags.outputs.image_tags }}
    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.sha }}
          fetch-depth: 0

      - name: Set image tags
        id: set_tags
        run: |
          IMAGE_TAG=$(git rev-parse HEAD)
          COMMIT_DATE=$(git log -1 --format=%cd --date=format:'%Y-%m-%d_%H.%M' ${{ github.event.inputs.sha }})
          BRANCH_NAME=$(git rev-parse --abbrev-ref HEAD)
          IMAGE_TAG_2="${BRANCH_NAME}__${COMMIT_DATE}"
          echo "image_tags=$IMAGE_TAG,$IMAGE_TAG_2" >> $GITHUB_OUTPUT              

  build-golang:
    needs: prepare
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.sha }}
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: image=moby/buildkit:latest

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ca-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Cache Docker layers for Golang
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-golang-v1-${{ hashFiles('build/docker/Dockerfile-golang', 'go/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-golang-v1-

      - name: Golang images
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          IFS=',' read -ra TAGS <<< "${{ needs.prepare.outputs.image_tags }}"
          echo -e "${TAGS[0]},${TAGS[1]}" > go/image_tags.txt
    
          TARGETS="cm-api cm-public cm-media cm-edsby cm-facebook cm-lookup cm-outlook cm-calendar cm-mailer"
          for TARGET in $TARGETS; do
          docker buildx build $BUILD_ARGS_GOLANG --target $TARGET -t $REGISTRY/$TARGET:${TAGS[0]} -t $REGISTRY/$TARGET:${TAGS[1]} .        
          done

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  build-react:
    needs: prepare
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.sha }}
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: image=moby/buildkit:latest

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ca-central-1

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Cache Docker layers for React
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-react-v1-${{ hashFiles('build/docker/Dockerfile-react', 'react/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-react-v1-

      - name: React image
        env:
          REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        run: |
          IFS=',' read -ra TAGS <<< "${{ needs.prepare.outputs.image_tags }}"
          echo -e "${TAGS[0]},${TAGS[1]}" > react/image_tags.txt
          
          docker buildx build $BUILD_ARGS_REACT --target cm-admin-ui -t $REGISTRY/cm-admin-ui:${TAGS[0]} -t $REGISTRY/cm-admin-ui:${TAGS[1]} .          

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache
  

  trigger-e2e:
    needs: [ build-golang, build-react ]
    runs-on: ubuntu-latest
    steps:
      - name: Trigger build and push workflow
        env:
          TOKEN: ${{ secrets.IE_GIT_PAT }}
        run: |
          if [ "${{ github.event_name }}" = "pull_request" ]; then
            REF="${{ github.event.pull_request.head.ref }}"
            SHA="${{ github.event.pull_request.head.sha }}"
          else
            REF="${{ github.ref }}"
            SHA="${{ github.sha }}"
          fi
          response=$(curl -s -o response.txt -w "%{http_code}" \
            -X POST \
            -H "Authorization: token $TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            https://api.github.com/repos/${{ github.repository }}/actions/workflows/e2e.yml/dispatches \
            -d "{\"ref\":\"$REF\", \"inputs\": {\"sha\":\"$SHA\"}}")
          cat response.txt
          if [ $response -ne 204 ]; then
            echo "Failed to trigger workflow: $response"
            exit 1
          fi         
