package azurexx

import (
	"contentmanager/library/utils/mapxx"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/msgraph/azure_ad"
	"gorm.io/gorm"
	"time"
)

const BatchSize = 100

// Sync
// 1) Get all Groups that overlap in SecurityGroups.ExternalIdLists Active Directory & their members
// 2) Map all users with their External Groups from AD as ExternalAccounts
// 3) Create or Update users in Content Manager based on ExternalAccounts.
// 4) Delete users that weren't synchronized
func Sync(db *gorm.DB) error {
	client, err := getClient(db)
	if err != nil {
		return err
	}
	//All accounts for tenant mapped by email to avoid any unique email conflicts etc.
	mapOfEmailToCMAccount, err := getMapOfEmailToAccounts(db)
	if err != nil {
		return err
	}
	externalIds, err := getExternalIdsFromSecurityGroups(db)
	if err != nil {
		return err
	}
	mapOfEmailToExternalAccounts, err := azure_ad.GetAllGroupMemberships(client, externalIds)
	if err != nil {
		return err
	}
	for _, externalAccounts := range mapxx.ToBatchesOf(mapOfEmailToExternalAccounts, BatchSize) {
		contentManagerAccounts := buildCMAccountsFromExternal(mapOfEmailToCMAccount, externalAccounts)
		if len(contentManagerAccounts) == 0 {
			continue
		}
		if err := db.Save(&contentManagerAccounts).Error; err != nil {
			return err
		}
	}
	return deactivateMissingUsers(db, mapOfEmailToCMAccount)
}

// deactivateMissingUsers
// 1. Get list of users that have overlap between their External Groups and SecurityGroups.ExternalIdList
// 2. Compare with the mapOfEmailToAccounts to see if those privileged users were seen during sync
// 3. If not seen, deactivate the user.
func deactivateMissingUsers(db *gorm.DB, mapOfEmailToAccount map[string]identity.Account) error {
	var emailsToSetInactive = []string{}
	for email, account := range mapOfEmailToAccount {
		if len(account.Password) == 0 && account.Active && !account.IsAdmin {
			emailsToSetInactive = append(emailsToSetInactive, email)
		}
	}
	if len(emailsToSetInactive) == 0 {
		return nil
	}
	for _, batch := range slicexx.ToBatchesOf(emailsToSetInactive, BatchSize) {
		db.
			Table(identity.Account{}.TableName()).
			Where("lower(email) in ?", batch).
			Where("length(password) = 0").
			Updates(map[string]interface{}{
				"active":     false,
				"updated_at": time.Now(),
				"updated_by": selfUpdated,
			})
		if db.Error != nil {
			return db.Error
		}
	}
	return nil
}
