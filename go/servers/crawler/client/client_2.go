package client

import (
	"context"
	"crypto/tls"
	"fmt"
	"golang.org/x/time/rate"
	"net"
	"net/http"
	"strings"
	"time"
)

// ClientConfig holds all HTTP client configuration
type ClientConfig struct {
	Timeout             time.Duration
	KeepAlive           time.Duration
	MaxIdleConns        int
	MaxIdleConnsPerHost int
	MaxConnsPerHost     int
	IdleConnTimeout     time.Duration
	TLSHandshakeTimeout time.Duration
}

// DefaultConfig returns recommended default configuration
func DefaultConfig() ClientConfig {
	return ClientConfig{
		Timeout:             30 * time.Second,
		KeepAlive:           30 * time.Second,
		MaxIdleConns:        100,
		MaxIdleConnsPerHost: 10,
		MaxConnsPerHost:     100,
		IdleConnTimeout:     90 * time.Second,
		TLSHandshakeTimeout: 10 * time.Second,
	}
}

type HTTPClient struct {
	client  *http.Client
	config  ClientConfig
	limiter *rate.Limiter
}

func NewHTTPClient(config ClientConfig) *HTTPClient {
	// Create a rate limiter: 100 requests per second with burst of 50
	limiter := rate.NewLimiter(rate.Limit(100), 50)

	transport := &http.Transport{
		Proxy: http.ProxyFromEnvironment,
		DialContext: func(ctx context.Context, network, addr string) (net.Conn, error) {
			host, port, err := net.SplitHostPort(addr)
			if err != nil {
				return nil, err
			}

			if strings.HasSuffix(host, ".localhost") {
				addr = net.JoinHostPort("127.0.0.1", port)
			}

			dialer := &net.Dialer{
				Timeout:   30 * time.Second,
				KeepAlive: config.KeepAlive,
				DualStack: true,
			}
			return dialer.DialContext(ctx, network, addr)
		},
		MaxIdleConns:        config.MaxIdleConns,
		MaxIdleConnsPerHost: config.MaxIdleConnsPerHost,
		MaxConnsPerHost:     config.MaxConnsPerHost,
		IdleConnTimeout:     config.IdleConnTimeout,
		TLSHandshakeTimeout: config.TLSHandshakeTimeout,
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true,
		},
		ExpectContinueTimeout: 1 * time.Second,
	}

	client := &http.Client{
		Transport: transport,
		Timeout:   config.Timeout,
	}

	return &HTTPClient{
		client:  client,
		config:  config,
		limiter: limiter,
	}
}

func (c *HTTPClient) Do(req *http.Request) (*http.Response, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}

	// Wait for rate limiter
	err := c.limiter.Wait(req.Context())
	if err != nil {
		return nil, fmt.Errorf("rate limiter error: %w", err)
	}

	// Implement retry logic
	var resp *http.Response
	backoff := time.Second
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		resp, err = c.client.Do(req)
		if err == nil {
			return resp, nil
		}

		if strings.Contains(err.Error(), "Only one usage of each socket address") {
			time.Sleep(backoff)
			backoff *= 2
			continue
		}

		return nil, err
	}

	return nil, fmt.Errorf("max retries reached: %w", err)
}

func (c *HTTPClient) DoWithContext(ctx context.Context, req *http.Request) (*http.Response, error) {
	if req == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}
	req = req.WithContext(ctx)
	return c.Do(req)
}
