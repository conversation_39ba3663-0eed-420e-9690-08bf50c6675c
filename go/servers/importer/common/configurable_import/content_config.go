package configurable_import

import (
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/library/utils/slicexx"
	"contentmanager/library/utils/timexx"
	"contentmanager/pkgs/content"
	"github.com/satori/go.uuid"
	"strconv"
	"time"
)

type (
	ContentConfigWithContext struct {
		ContentFieldConfig
		content            *content.Content
		isPerformingUpdate bool
	}
)

// ProcessPath - Expects the contextual Content to have a valid ID
func (ctx ContentConfigWithContext) ProcessPath() string {
	return utils.BuildPath(ctx.ComputedPath, ctx.content.ID)
}

// ProcessPrivacyLevel
// 2024-09-09: Edsby News can be set to Staff-only within Edsby - we support(ed) the ability to set the privacyLevel from
// the source (edsby) however, this doesn't seem to be the desired behaviour from the community. Testing with default-only.
func (ctx ContentConfigWithContext) ProcessPrivacyLevel() int {
	if !ctx.PrivacyLevel().CanUpdate(ctx.isPerformingUpdate) {
		return ctx.content.PrivacyLevel
	}
	if ctx.PrivacyLevel().UseDefault {
		return ctx.DefaultPrivacyLevel()
	}
	return 0
}

func (ctx ContentConfigWithContext) ProcessTags(toMerge []uuid.UUID) []uuid.UUID {
	if t := ctx.Tags(); t.CanUpdate(ctx.isPerformingUpdate) {
		// Field can be updated
		// Utilize user-provided default if it is present
		// Else default to our own default
		if t.UseDefault {
			return slicexx.Distinct(append(ctx.TagIds, toMerge...))
			//return importers.MergeTags(ctx.ComputedTags, toMerge)
		} else {
			return slicexx.Distinct(append(ctx.content.Tags, toMerge...))
			//return importers.MergeTags(ctx.content.Tags, toMerge)
		}
	}
	// Original value should be provided as a fallback
	return ctx.content.Tags
}

func (ctx ContentConfigWithContext) ProcessSites() []uuid.UUID {
	if ctx.Site().CanUpdate(ctx.isPerformingUpdate) {
		// Field can be updated
		// 2024-09-06: Since we're consolidating disparate Site.Settings configs, we don't need this
		// loose 'growing' of Sites - Settings.Sites is the value that will be used as Content.Sites
		return ctx.Sites
	}
	// Original value should be provided as a fallback
	return ctx.content.Sites
}

// ProcessPublishAt
// Determine the correct PublishAt date - Compares the Existing Value, the Default, the value from the Origin (e.g Edsby)
// Config.Default is a bool - It logically works according to the old paradigm of Draft or Published.
// If Default = False | Nil (draft) should be returned
// If Default = True  | The older value between the existing PublishAt & Now should be returned
func (ctx ContentConfigWithContext) ProcessPublishAt(updateValue *time.Time) *time.Time {
	if !ctx.Published().CanUpdate(ctx.isPerformingUpdate) {
		// Field cannot be updated due to configuration
		// Return the fallback, generally being the current value of the object
		return ctx.content.PublishAt
	}
	// Field can be Updated
	if ctx.Published().UseDefault {
		// Use the existing publish date or Now(), whichever is older.
		if ctx.DefaultPublished() {
			return timexx.FindOldest(ctx.content.PublishAt, converters.AsPointer(time.Now().UTC()))
		}
		// Set to Draft
		return nil
	}
	// UseDefault is false
	// check what the value to Update with is, fallback to Now if it's invalid.
	if updateValue != nil && !updateValue.IsZero() {
		return updateValue
	}
	return timexx.FindOldest(ctx.content.PublishAt, converters.AsPointer(time.Now().UTC()))
}

func (ctx ContentConfigWithContext) RequiresUpdate() bool {
	if ctx.ProcessPath() != ctx.content.Path {
		return true
	}
	if ctx.ProcessPrivacyLevel() != ctx.content.PrivacyLevel {
		return true
	}
	if ctx.ProcessPrivacyLevel() != ctx.content.PrivacyLevel {
		return true
	}
	if ctx.ProcessPublishAt(nil) != ctx.content.PublishAt {
		return true
	}
	if !slicexx.EqualComparable[uuid.UUID](ctx.content.Sites, ctx.ProcessSites()) {
		return true
	}
	if !slicexx.EqualComparable[uuid.UUID](ctx.content.Tags, ctx.ProcessTags(nil)) {
		return true
	}
	//if !slicexx.Equal[commonModels.Tag](ctx.content.Tags, ctx.ProcessTags(nil), func(t commonModels.Tag) string {
	//	return t.ID.String()
	//}) {
	//	return true
	//}

	return false
}

func (ctx ContentConfigWithContext) DefaultPrivacyLevel() int {
	switch v := ctx.PrivacyLevel().Default.(type) {
	case int:
		return v
	case float64:
		return int(v)
	case string:
		if toFloat, err := strconv.ParseFloat(v, 64); err == nil {
			return int(toFloat)
		}
		if toInt, err := strconv.Atoi(v); err == nil {
			return toInt
		}
	}
	return 0
}

func (ctx ContentConfigWithContext) DefaultPublished() bool {
	if v, ok := ctx.Published().Default.(bool); ok {
		return v
	}
	if s, ok := ctx.Published().Default.(string); ok {
		b, err := strconv.ParseBool(s)
		if err == nil {
			return b
		}
	}

	return true
}
