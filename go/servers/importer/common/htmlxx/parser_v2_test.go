package htmlxx

import (
	"strings"
	"testing"
)

var (
	teamsMeetingURL = "https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d"
	linkNodeTests   = map[string]string{
		"Plain-text link within <p>": "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<p class=\"editor-paragraph\">Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.\n</p>\n<p class=\"editor-paragraph\"></p>\n<br>\n<p class=\"editor-paragraph\">Open this link to join: https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d</p>\n</body>\n</html>\n",
		"Link within <a>":            "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<p class=\"editor-paragraph\">Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.\n</p>\n<p class=\"editor-paragraph\"></p>\n<br>\n<a href='https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d'> Open this link to join:\n        https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d\n    </a>\n</body>\n</html>\n",
	}
	htmlTranslationTests = map[string]string{
		"missingOpeningTags":          "<html><h1>test</h1> </div></html>",
		"missingClosingTags":          "<html><h1>test</h1> <div></html>",
		"ECSD Victoria Day Weekend":   "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\nThere are no classes for ECSD students Friday, May 17 and Monday, May 20 for May Long Weekend.</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<br/>\n</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">Visit</span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><a href=\"https://l.facebook.com/l.php?u=http%3A%2F%2Fecsd.net%2Fcalendar%3Ffbclid%3DIwAR0GG_3h59Xyml9QSJWcnLGHPEy2zV0MKfpCj7V3BnmD9FjqCrGduj0vC-k&amp;h=AT0n74QtBHdC8lZ3EOM4SyZFdioZzdC0tUKph67pNaMn8cl3T-Zhx-d9hpIZkPMozCs65F7TH0zudo542-VOuJJ-d3xRpDiAgQKHn44zXCw35cHjpH5SwzFgzCrLbQUNS3_7&amp;__tn__=-UK*F\" class=\"x1i10hfl xjbqb8w x6umtig x1b1mbwd xaqea5y xav7gou x9f619 x1ypdohk xt0psk2 xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz xt0b8zv x1fey0fg ContentPasted0\" tabindex=\"0\" data-loopstyle=\"link\" style=\"margin:0px; text-decoration:none; outline:none; list-style:none; box-sizing:border-box; display:inline\"><span style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">ecsd.net/calendar</span></a></span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">for\n important dates and holidays for the 2023-2024 school year.</span><br/>\n</div>\n</body>\n</html>\n",
		"ECSD Parent Council Meeting": "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n<meta name=\"ProgId\" content=\"Word.Document\">\n<meta name=\"Generator\" content=\"Microsoft Word 15\">\n<meta name=\"Originator\" content=\"Microsoft Word 15\">\n<style>\n<!--\n@font-face\n\t{font-family:\"Cambria Math\"}\n@font-face\n\t{font-family:Calibri}\np.MsoNormal, li.MsoNormal, div.MsoNormal\n\t{margin:0in;\n\tfont-size:11.0pt;\n\tfont-family:\"Calibri\",sans-serif}\na:link, span.MsoHyperlink\n\t{color:#0563C1;\n\ttext-decoration:underline}\na:visited, span.MsoHyperlinkFollowed\n\t{color:#954F72;\n\ttext-decoration:underline}\nspan.EmailStyle17\n\t{font-family:\"Calibri\",sans-serif;\n\tcolor:windowtext}\n.MsoChpDefault\n\t{font-family:\"Calibri\",sans-serif}\n@page WordSection1\n\t{margin:1.0in 1.0in 1.0in 1.0in}\ndiv.WordSection1\n\t{}\n-->\n</style>\n</head>\n<body lang=\"EN-US\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n<div class=\"WordSection1\">\n<p class=\"MsoNormal\">&nbsp;</p>\n</div>\n</body>\n</html>\n",
	}
)

func Test_ValidateAndSanitizeHTML(t *testing.T) {
	for name, test := range linkNodeTests {
		htmlContent := Sanitize(test)
		if !strings.Contains(htmlContent, teamsMeetingURL) {
			t.Errorf("%s : missing teamsMeetingURL in html", name)
		}
		if strings.Contains(htmlContent, "<html>") ||
			strings.Contains(htmlContent, "<head>") ||
			strings.Contains(htmlContent, "<body>") {
			t.Errorf("%s : html contains invalid elements", name)
		}
	}
	//for name, test := range htmlTranslationTests {
	//	htmlContent := Sanitize(test)
	//	log.Printf("\n[%s]\n [fromOutlook]:\n %s\n [next]:\n %s", name, test, htmlContent)
	//}
}
