package htmlxx

import (
	"bytes"
	"github.com/microcosm-cc/bluemonday"
	"golang.org/x/net/html"
	"golang.org/x/net/html/atom"
	"strings"
)

func createSanitizationPolicy() *bluemonday.Policy {
	p := bluemonday.NewPolicy()
	p.AllowStandardURLs()
	p.AllowImages()
	p.AllowLists()
	p.AllowTables()
	p.Allow<PERSON>("href").OnElements("a")
	p.AllowElements("h1", "h2", "h3", "h4", "h5", "h6", "p", "span", "a", "div", "ul", "li", "b", "strong", "i", "em")
	return p
}

var (
	policy = createSanitizationPolicy()
)

func Sanitize(body string) string {
	return policy.Sanitize(body)
}

// Validate
// Utilizes html.Parse method, it creates the html tree and will
// attempt to fix issues such as missing closing tag by creating them automatically.
func Validate(body string) (string, error) {
	var reader = strings.NewReader(body)
	var i, err = html.Parse(reader)
	if err != nil {
		return "", err
	}
	var builder strings.Builder
	if err := html.Render(&builder, i); err != nil {
		return "", err
	}
	return builder.String(), nil
}
func ParseNodeFragments(body string, mutateNode func(node *html.Node) *html.Node) (string, error) {
	n, err := html.ParseFragment(strings.NewReader(body), &html.Node{
		Type:     html.ElementNode,
		Data:     "body",
		DataAtom: atom.Body,
	})
	if err != nil {
		return "", err
	}
	var buf bytes.Buffer
	for _, node := range n {
		mutateNode(node)
		if err := html.Render(&buf, node); err != nil {
			return "", err
		}
	}
	return buf.String(), nil
}
func TranslateAndSanitize(htmlContent string) (string, error) {
	var err error
	if htmlContent, err = Validate(htmlContent); err != nil {
		return "", err
	}
	return Sanitize(htmlContent), nil
}
