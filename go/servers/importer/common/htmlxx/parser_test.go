package htmlxx

//
//import (
//	"golang.org/x/net/html"
//	"regexp"
//	"strings"
//	"testing"
//)
//
//var cases = map[string]struct {
//	fromOutlook       string
//	expectedResult    string
//	firstTagInnerHTML string
//}{
//	"missingOpeningTags": {
//		fromOutlook:       "<html><h1>test</h1> </div></html>",
//		expectedResult:    "<div><h1>test</h1> </div>",
//		firstTagInnerHTML: "<div><h1>test</h1> </div>",
//	},
//	"missingClosingTags": {
//		fromOutlook:       "<html><h1>test</h1> <div></html>",
//		expectedResult:    "<div><h1>test</h1> </div>",
//		firstTagInnerHTML: "<div><h1>test</h1> <div></div></div>",
//	},
//	"ECSD Victoria Day Weekend": {
//		fromOutlook:       "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\nThere are no classes for ECSD students Friday, May 17 and Monday, May 20 for May Long Weekend.</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<br/>\n</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">Visit</span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><a href=\"https://l.facebook.com/l.php?u=http%3A%2F%2Fecsd.net%2Fcalendar%3Ffbclid%3DIwAR0GG_3h59Xyml9QSJWcnLGHPEy2zV0MKfpCj7V3BnmD9FjqCrGduj0vC-k&amp;h=AT0n74QtBHdC8lZ3EOM4SyZFdioZzdC0tUKph67pNaMn8cl3T-Zhx-d9hpIZkPMozCs65F7TH0zudo542-VOuJJ-d3xRpDiAgQKHn44zXCw35cHjpH5SwzFgzCrLbQUNS3_7&amp;__tn__=-UK*F\" class=\"x1i10hfl xjbqb8w x6umtig x1b1mbwd xaqea5y xav7gou x9f619 x1ypdohk xt0psk2 xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz xt0b8zv x1fey0fg ContentPasted0\" tabindex=\"0\" data-loopstyle=\"link\" style=\"margin:0px; text-decoration:none; outline:none; list-style:none; box-sizing:border-box; display:inline\"><span style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">ecsd.net/calendar</span></a></span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">for\n important dates and holidays for the 2023-2024 school year.</span><br/>\n</div>\n</body>\n</html>\n",
//		firstTagInnerHTML: "<div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\nThere are no classes for ECSD students Friday, May 17 and Monday, May 20 for May Long Weekend.</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<br/>\n</div>\n<div class=\"elementToProof\" style=\"font-family:Calibri,Arial,Helvetica,sans-serif; font-size:12pt; color:rgb(0,0,0)\">\n<span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">Visit</span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><a href=\"https://l.facebook.com/l.php?u=http%3A%2F%2Fecsd.net%2Fcalendar%3Ffbclid%3DIwAR0GG_3h59Xyml9QSJWcnLGHPEy2zV0MKfpCj7V3BnmD9FjqCrGduj0vC-k&amp;h=AT0n74QtBHdC8lZ3EOM4SyZFdioZzdC0tUKph67pNaMn8cl3T-Zhx-d9hpIZkPMozCs65F7TH0zudo542-VOuJJ-d3xRpDiAgQKHn44zXCw35cHjpH5SwzFgzCrLbQUNS3_7&amp;__tn__=-UK*F\" class=\"x1i10hfl xjbqb8w x6umtig x1b1mbwd xaqea5y xav7gou x9f619 x1ypdohk xt0psk2 xe8uvvx xdj266r x11i5rnm xat24cr x1mh8g0r xexx8yu x4uap5 x18d9i69 xkhd6sd x16tdsg8 x1hl2dhg xggy1nq x1a2a7pz xt0b8zv x1fey0fg ContentPasted0\" tabindex=\"0\" data-loopstyle=\"link\" style=\"margin:0px; text-decoration:none; outline:none; list-style:none; box-sizing:border-box; display:inline\"><span style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">ecsd.net/calendar</span></a></span><span class=\"ContentPasted0\" style=\"font-size:15px; font-family:&#34;Segoe UI Historic&#34;,&#34;Segoe UI&#34;,Helvetica,Arial,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\"><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px\">&nbsp;</span></span><span class=\"ContentPasted0\" style=\"font-size:12pt; font-family:Calibri,Helvetica,sans-serif; margin:0px; text-align:start; display:inline!important; color:rgb(5,5,5); background-color:rgb(255,255,255)\">for\n important dates and holidays for the 2023-2024 school year.</span><br/>\n</div>\n</div>",
//		expectedResult:    "<div><div>There are no classes for ECSD students Friday, May 17 and Monday, May 20 for May Long Weekend.</div><div><span>Visit</span><span> <a href='https://l.facebook.com/l.php?u=http%3A%2F%2Fecsd.net%2Fcalendar%3Ffbclid%3DIwAR0GG_3h59Xyml9QSJWcnLGHPEy2zV0MKfpCj7V3BnmD9FjqCrGduj0vC-k&h=AT0n74QtBHdC8lZ3EOM4SyZFdioZzdC0tUKph67pNaMn8cl3T-Zhx-d9hpIZkPMozCs65F7TH0zudo542-VOuJJ-d3xRpDiAgQKHn44zXCw35cHjpH5SwzFgzCrLbQUNS3_7&__tn__=-UK*F' ><span>ecsd.net/calendar</span></a></span><span> for important dates and holidays for the 2023-2024 school year.</span></div></div>",
//	},
//	"ECSD Parent Council Meeting": {
//		fromOutlook:       "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n<meta name=\"ProgId\" content=\"Word.Document\">\n<meta name=\"Generator\" content=\"Microsoft Word 15\">\n<meta name=\"Originator\" content=\"Microsoft Word 15\">\n<style>\n<!--\n@font-face\n\t{font-family:\"Cambria Math\"}\n@font-face\n\t{font-family:Calibri}\np.MsoNormal, li.MsoNormal, div.MsoNormal\n\t{margin:0in;\n\tfont-size:11.0pt;\n\tfont-family:\"Calibri\",sans-serif}\na:link, span.MsoHyperlink\n\t{color:#0563C1;\n\ttext-decoration:underline}\na:visited, span.MsoHyperlinkFollowed\n\t{color:#954F72;\n\ttext-decoration:underline}\nspan.EmailStyle17\n\t{font-family:\"Calibri\",sans-serif;\n\tcolor:windowtext}\n.MsoChpDefault\n\t{font-family:\"Calibri\",sans-serif}\n@page WordSection1\n\t{margin:1.0in 1.0in 1.0in 1.0in}\ndiv.WordSection1\n\t{}\n-->\n</style>\n</head>\n<body lang=\"EN-US\" link=\"#0563C1\" vlink=\"#954F72\" style=\"word-wrap:break-word\">\n<div class=\"WordSection1\">\n<p class=\"MsoNormal\">&nbsp;</p>\n</div>\n</body>\n</html>\n",
//		firstTagInnerHTML: "<div>\n<div class=\"WordSection1\">\n<p class=\"MsoNormal\">&nbsp;</p>\n</div>\n</div>",
//		expectedResult:    "<div></div>",
//	},
//	"Plain-text link within <p>": {
//		fromOutlook:    "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<p class=\"editor-paragraph\">Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.\n</p>\n<p class=\"editor-paragraph\"></p>\n<br>\n<p class=\"editor-paragraph\">Open this link to join: https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d</p>\n</body>\n</html>\n",
//		expectedResult: "<div>\n    <p>Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign\n        activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.</p>\n    <p> Open this link to join:\n        https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d\n    </p>\n</div>\n",
//	},
//	"Link within <a>": {
//		fromOutlook:    "<html>\n<head>\n<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n</head>\n<body>\n<p class=\"editor-paragraph\">Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.\n</p>\n<p class=\"editor-paragraph\"></p>\n<br>\n<a href='https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d'> Open this link to join:\n        https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d\n    </a>\n</body>\n</html>\n",
//		expectedResult: "<div>\n    <p>Learn how to differnentiate between the different space types, invite families to SpacesEDU, create and assign\n        activites, assess student progress with the new GrowthBook and get your students started with SpacesEDU.</p>\n    <a href='https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d'> Open this link to join:\n        https://teams.microsoft.com/l/meetup-join/19%3ameeting_YTQ4ZjlhMGQtNmFhMS00ODhjLWFkYjItMGI2N2U1OGQwNjA5%40thread.v2/0?context=%7b%22Tid%22%3a%22b08d0ea5-01df-4053-adc8-4c2a90194428%22%2c%22Oid%22%3a%22de02efde-5479-49c6-b8e1-f4b13b9ae109%22%7d\n    </a>\n</div>\n",
//	},
//}
//var sep = "\n------------------------\n"
//
//var spacesRegex = regexp.MustCompile("[ ]|(&nbsp;)|([\\s\u00A0])")
//
//func isEqual(str1 string, str2 string) (bool, string, string) {
//	f := spacesRegex.ReplaceAllString(str1, "")
//	s := spacesRegex.ReplaceAllString(str2, "")
//	return f == s, f, s
//}
//func Test_OutlookBodyToContent(t *testing.T) {
//	for testName, testCase := range cases {
//		received := Parse(testCase.fromOutlook).Compile()
//		// Check Invalid Tokens
//		tokenizer := html.NewTokenizer(strings.NewReader(received))
//		for {
//			next := tokenizer.Next()
//			if next == html.ErrorToken {
//				break
//			}
//			token := tokenizer.Token()
//			if next == html.StartTagToken || next == html.EndTagToken {
//				if _, ok := acceptableTokens[token.Data]; !ok {
//					t.Errorf("%s: invalid token found\ntoken: [%s]", testName, token.Data)
//				}
//			}
//		}
//
//		// Check styles
//		if strings.Contains(received, "style=") {
//			t.Errorf("%s: invalid styles found\nfound: %s", testName, sep+testCase.fromOutlook+sep)
//		}
//
//		if len(testCase.expectedResult) > 0 {
//			if equal, expected, result := isEqual(testCase.expectedResult, received); !equal {
//				t.Errorf("%s: unexpected return value \nexpected:  %s \nreceived: %s", testName, sep+expected+sep, sep+result+sep)
//			}
//		}
//
//	}
//}
//
////func Test_getFirstTagInnerHTML(t *testing.T) {
////	t.Skip()
////	for testName, testCase := range cases {
////		sHtml, err := prepareHTML(testCase.fromOutlook)
////		if err != nil {
////			t.Error(err)
////		}
////		c := getFirstTagInnerHTML(sHtml, atom.Body.String())
////
////		if equal, expected, result := isEqual(testCase.firstTagInnerHTML, c); !equal {
////			t.Errorf("%s: unexpected return value \nexpected:  %s \nreceived: %s", testName, sep+expected+sep, sep+result+sep)
////		}
////	}
////}
