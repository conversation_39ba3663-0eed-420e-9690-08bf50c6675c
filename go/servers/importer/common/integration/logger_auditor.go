package integration

import (
	"contentmanager/logging"
	"contentmanager/pkgs/audit_logger"
	"contentmanager/pkgs/multitenancy"
	"fmt"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
)

type LoggerAuditor struct {
	Auditor    Auditor
	Logger     zerolog.Logger
	tenantName string
	tenantId   uuid.UUID
}

func NewLoggerAuditor(db *gorm.DB, tenant multitenancy.Tenant, subject string) *LoggerAuditor {
	var la = LoggerAuditor{
		Auditor: Auditor{
			db: db,
		},
		tenantName: tenant.Name,
		tenantId:   tenant.ID,
	}
	la.WithSubject(subject)
	return &la
}
func (a *LoggerAuditor) WithSubject(subject string) {
	a.Auditor.prefix = fmt.Sprintf("[%s]:", subject)
	a.Logger = logging.RootLogger().With().
		Str("tenant", a.tenantName).
		Str("tenantId", a.tenantId.String()).
		Str("subject", subject).
		Logger()

}

func (a LoggerAuditor) LogAndRecordInfo(message string, inf ...interface{}) {
	a.Auditor.RecordInfo(message, inf)
	a.Logger.Info().Msg(message)
}

func (a LoggerAuditor) LogAndRecordErr(message string, err error) {
	a.Auditor.RecordErr(message, err)
	a.Logger.Err(err).Msg(message)
}

type Auditor struct {
	db     *gorm.DB
	prefix string
}

func (a Auditor) RecordInfo(message string, inf ...interface{}) {
	audit_logger.LogAuditInfo(a.db, message, inf)
}

func (a Auditor) RecordErr(message string, err error) {
	audit_logger.LogAuditError(a.db, message, err)
}

func (a Auditor) withPrefix(message string) string {
	if strings.HasPrefix(message, a.prefix) {
		return message
	}
	return a.prefix + " " + message
}
