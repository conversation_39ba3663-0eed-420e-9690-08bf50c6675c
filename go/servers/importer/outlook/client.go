package outlook

import (
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/msgraph"
	"contentmanager/pkgs/settings"
	importers "contentmanager/servers/importer/common"
	"contentmanager/servers/importer/common/integration"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func NewAPIClient(db *gorm.DB) (*msgraph.GraphClient, error) {
	var secrets msgraph.ClientSecrets
	if err := integration.GetSecrets(db, settings.OutlookSecrets, &secrets); err != nil {
		return nil, err
	}
	if secrets.TenantId == uuid.Nil {
		return nil, importers.ErrInvalidSecrets
	}
	return GetGraphClient(secrets.TenantId)
}

func GetGraphClient(tenantId uuid.UUID) (*msgraph.GraphClient, error) {
	svc := config.GetAppConfig()
	return msgraph.NewGraphClient(tenantId.String(), svc.MSOAuthApplicationID, svc.MSOAuthClientSecret, false)
}
