package outlook

import (
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/msgraph"
	"contentmanager/pkgs/multitenancy"
	importers "contentmanager/servers/importer/common"
	"contentmanager/servers/importer/common/integration"
	"contentmanager/tests"
	"contentmanager/tests/db_seed"
	"contentmanager/tests/test_utils"
	"encoding/json"
	"errors"
	uuid "github.com/satori/go.uuid"
	"testing"
	"time"
)

var (
	resourcer TestResourcer
)

// Query for all calendars on a mailbox
func Test_getCalendarEvents_get_by_MailboxId(t *testing.T) {
	if events, err := getCalendarEvents(resourcer, mailboxID1); err != nil {
		t.Error(err)
	} else if len(events) != 3 {
		t.Errorf("expected 3 events, got %d", len(events))
	}
}

// Query a mailbox with a CalendarID
func Test_getCalendarEvents_get_by_CalendarId(t *testing.T) {
	if events, err := getCalendarEvents(resourcer, joinMailboxCalendarId(mailboxID1, calendarID1)); err != nil {
		t.Error(err)
	} else if len(events) != 1 {
		t.Errorf("expected one event, got %d", len(events))
	} else if events[0].ID != calendar1Event1 {
		t.Errorf("expected event with id [C1A] but got id [%v]", events[0].ID)
	}
}

// Query for a mailbox for a CalendarId that doesn't exist.
func Test_getCalendarEvents_get_non_existing_Calendar_from_Mailbox(t *testing.T) {
	events, err := getCalendarEvents(resourcer, joinMailboxCalendarId(mailboxID2, "doesnt-exist"))
	if err == nil {
		t.Errorf("expected error when querying for a mailboxes calendar that doesn't exist")
	} else if len(events) > 0 {
		t.Errorf("expected 0 events, got %d", len(events))
	}
}

// Test an existing event being cancelled in outlook & adding a newly created event.
func Test_TranslateEvents(t *testing.T) {
	var context = newImportContext()
	// Test only querying a single calendar on MailboxID1
	var onlyCalendar2 = joinMailboxCalendarId(mailboxID1, calendarID2)
	var config = db_seed.GetImportConfig(onlyCalendar2, uuid.Nil)

	var contentId = generateConsistentOutlookID(calendar2Event2)
	// Include 1 "existing" piece of content that should be deleted due to being `Cancelled` in outlook
	var list = importers.ContentList{
		contentId: {ID: contentId},
	}
	events, err := getEvents(resourcer, list, config.Id)
	if err != nil {
		t.Error(err)
	}
	if err := TranslateEvents(context, list, config, events); err != nil {
		t.Errorf("error translating events: [%v]", err)
	}
	if len(list) != 2 {
		t.Errorf("expected 2 items, got %d", len(list))
	}
	// Test if the `Cancelled` event is correctly set to Active False
	if cancelled, _ := list[contentId]; cancelled.Active != false {
		t.Error("expected cancelled event to be de-activated")
	}
	// Test computed fields for newly created content
	ID := generateConsistentOutlookID(calendar2Event1)
	imported, exists := list[ID]
	if !exists {
		t.Errorf("expected item [%s] to exist in ContentList", ID.String())
	}
	if imported.Active != true {
		t.Error("expected new event to be saved as active")
	}

	if len(imported.Tags) != 1 || imported.Tags[0] != AthleticsID {
		t.Error("expected new event to be saved with TagId based on msgraph.CalendarEvent.Categories")
	}

	// here
	if !slicexx.EqualComparable(imported.Sites, config.Sites) {
		t.Errorf("expected new event to be saved with sites from importConfig.Sites: [%v], but got [%v]", config.Sites, imported.Sites)
	}
	privacyLevelDefault := config.Events.PrivacyLevel().Default.(int)
	if imported.PrivacyLevel != privacyLevelDefault {
		t.Errorf("expected new event to be saved with default privacy level of [%v], but got [%v]", privacyLevelDefault, imported.PrivacyLevel)
	}

	today := time.Now().UTC()
	PublishedAt := imported.PublishAt.UTC()
	if !test_utils.IsSameDate(&PublishedAt, &today) {
		t.Errorf("expected new event to be published today, but got [%v]", imported.PublishAt)
	}
	if imported.Path != utils.BuildPath(config.Events.ComputedPath, imported.ID) {
		t.Errorf("expected new event to have path connected to the templateParent, but got [%v]", imported.Path)
	}
	settings := struct {
		StartDate      time.Time `json:"startdate"`
		EndDate        time.Time `json:"enddate"`
		IsAllDay       bool      `json:"isAllDay"`
		ImportInfoBase `json:"importInfo"`
	}{}
	if err := json.Unmarshal(imported.Settings, &settings); err != nil {
		t.Error(err)
	}
	if settings.ExternalId != onlyCalendar2 {
		t.Errorf("expected new event to be saved with settings.importInfo.externalId [%v], got [%v]", onlyCalendar2, settings.ExternalId)
	}
	//if !slicexx.Contains(settings.Ids, onlyCalendar2) {
	//	t.Errorf("expected new event to be saved with settings.importInfo.ids containing [%v], got [%v]", onlyCalendar2, settings.Ids)
	//}
}

// ------ Mock ------- //
type TestResourcer struct{}

func (tr TestResourcer) ListMailboxCalendars(mailboxId string) (msgraph.Calendars, error) {
	if mailboxId == mailboxID1 {
		return msgraph.Calendars{Mailbox1.Calendar1.Calendar, Mailbox1.Calendar2.Calendar}, nil
	} else if mailboxId == mailboxID2 {
		return msgraph.Calendars{Mailbox2.Calendar3.Calendar}, nil
	} else {
		return nil, errors.New("calendar not found")
	}
}
func (tr TestResourcer) ListCalendarEvents(calendars msgraph.Calendars) (msgraph.CalendarEvents, error) {
	var resp msgraph.CalendarEvents
	if _, ok := findCalendar(calendars, calendarID1); ok {
		resp = append(resp, Mailbox1.Calendar1.Events...)
	}
	if _, ok := findCalendar(calendars, calendarID2); ok {
		resp = append(resp, Mailbox1.Calendar2.Events...)
	}
	return resp, nil
}

// ------ Test Data ------- //

type calendarWithEvents struct {
	Calendar msgraph.Calendar
	Events   msgraph.CalendarEvents
}

const (
	mailboxID1      = "M1"
	mailboxID2      = "M2"
	calendarID1     = "C1"
	calendarID2     = "C2 With Spaces"
	calendarID3     = "C3"
	calendar1Event1 = calendarID1 + "A"
	calendar2Event1 = calendarID2 + "A"
	calendar2Event2 = calendarID2 + "B"
	calendar3Event1 = calendarID3 + "A"
)

var (
	AthleticsID   = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000033")
	AthleticsName = "Athletics"

	Mailbox1 = struct {
		MailboxId string
		Calendar1 calendarWithEvents
		Calendar2 calendarWithEvents
	}{
		MailboxId: "",
		Calendar1: calendarWithEvents{
			Calendar: msgraph.Calendar{ID: uuid.NewV4().String(), Name: calendarID1},
			Events: []msgraph.CalendarEvent{
				{
					ID:                   calendar1Event1,
					CreatedDateTime:      time.Now(),
					LastModifiedDateTime: time.Now(),
					Categories:           []string{"holiday"},
					Subject:              "Good Friday",
					IsAllDay:             true,
					StartTime:            test_utils.MustParseTime(time.DateTime, "2025-04-18 00:00:00"),
					EndTime:              test_utils.MustParseTime(time.DateTime, "2025-04-19 00:00:00"),
				},
			},
		},
		Calendar2: calendarWithEvents{
			Calendar: msgraph.Calendar{ID: uuid.NewV4().String(), Name: calendarID2},
			Events: []msgraph.CalendarEvent{
				{
					ID:                   calendar2Event1,
					Subject:              "Sports Day from Noon to 3pm",
					Categories:           []string{"Athletics"},
					CreatedDateTime:      time.Now(),
					LastModifiedDateTime: time.Now().AddDate(0, 1, 0),
					StartTime:            test_utils.MustParseTime(time.RFC3339, "2024-10-05T12:00:00Z"),
					EndTime:              test_utils.MustParseTime(time.RFC3339, "2024-10-05T15:04:05Z"),
				},
				{
					ID:                   calendar2Event2,
					Subject:              "Cancelled All Day Event",
					IsAllDay:             true,
					IsCancelled:          true,
					CreatedDateTime:      time.Now(),
					LastModifiedDateTime: time.Now().AddDate(0, 1, 0),
					StartTime:            test_utils.MustParseTime(time.DateTime, "2025-04-18 00:00:00"),
					EndTime:              test_utils.MustParseTime(time.DateTime, "2025-04-19 00:00:00"),
				},
			},
		},
	}
	Mailbox2 = struct {
		MailboxId string
		Calendar3 calendarWithEvents
	}{
		MailboxId: ",",
		Calendar3: calendarWithEvents{
			Calendar: msgraph.Calendar{ID: calendarID3},
			Events: []msgraph.CalendarEvent{
				{
					ID:                   calendar3Event1,
					CreatedDateTime:      time.Now(),
					LastModifiedDateTime: time.Now(),
					Subject:              "Pro-D Day",
					IsAllDay:             true,
					StartTime:            test_utils.MustParseTime(time.DateTime, "2025-01-18 00:00:00"),
					EndTime:              test_utils.MustParseTime(time.DateTime, "2025-01-19 00:00:00"),
				},
			},
		}}
)

// ------ Utils ------- //
func joinMailboxCalendarId(mailboxId, calendarId string) string {
	return mailboxId + "?calendarId=" + calendarId
}

func newImportContext() *integration.ImportContext {
	testDb, dispose := tests.InitTenantDB()
	defer dispose()
	AllTags := []commonModels.Tag{
		{
			ID:    AthleticsID,
			Name:  AthleticsName,
			Types: []string{"event"},
		},
	}

	if err := testDb.Save(&AllTags).Error; err != nil {
		panic(err)
	}
	testTenant := multitenancy.Tenant{
		Entity: commonModels.Entity{ID: uuid.NewV4()},
		Name:   "New Tenant 1",
	}
	return integration.NewImportContext(testDb, testTenant)
}
