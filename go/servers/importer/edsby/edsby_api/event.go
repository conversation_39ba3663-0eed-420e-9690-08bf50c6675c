package edsby_api

import (
	"contentmanager/library/utils"
	importers "contentmanager/servers/importer/common"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"strconv"
	"time"
)

type (
	Event struct {
		EventState
		Nid            int    `json:"nid"`
		RepeatType     int    `json:"RepeatType"`
		RepeatParam    string `json:"RepeatParam"`
		RepeatCustom   string `json:"RepeatCustom"`
		RepeatInterval int    `json:"RepeatInterval"`
		Nodetype       int    `json:"nodetype"`
		Nodesubtype    int    `json:"nodesubtype"`
		Rid            int    `json:"rid"`
	}
	EventState struct {
		EventName    string    `json:"EventName"`
		StartDate    time.Time `json:"StartDate"`
		EndDate      time.Time `json:"EndDate"`
		StartDateStr string    `json:"startDateStr"`
		Duration     int       `json:"Duration"`
		Location     string    `json:"Location"`
		Notes        string    `json:"Notes"`
	}
	EventInfo struct {
		Events []Event `json:"events"`
	}
)

func (e *Event) ID() uuid.UUID {
	return importers.GenerateConsistentID("events-" + strconv.Itoa(e.Nid))
}
func (e *Event) IsEqual(state EventState) bool {
	return e.EventName != state.EventName ||
		e.StartDate != state.StartDate ||
		e.EndDate != state.EndDate ||
		e.StartDateStr != state.StartDateStr ||
		e.Duration != state.Duration ||
		e.Location != state.Location ||
		e.Notes != state.Notes
}
func (e *Event) IsEqualJson(jsonState json.RawMessage) bool {
	return e.IsEqual(ParseJsonEventState(jsonState))
}
func (e *Event) GetStateMap() map[string]interface{} {
	return utils.InterfaceToMapStringInterface(e.EventState)
}
func ParseJsonEventState(bytes []byte) EventState {
	var temp = struct {
		State EventState `json:"importInfo"`
	}{}
	_ = json.Unmarshal(bytes, &temp)
	return temp.State
}
func (e *Event) UnmarshalJSON(b []byte) error {
	tmp := struct {
		Nid            int    `json:"nid"`
		EventName      string `json:"EventName"`
		Date           string `json:"Date"`
		Duration       int    `json:"Duration"`
		Location       string `json:"Location"`
		Notes          string `json:"Notes"`
		RepeatType     int    `json:"RepeatType"`
		RepeatParam    string `json:"RepeatParam"`
		RepeatCustom   string `json:"RepeatCustom"`
		RepeatInterval int    `json:"RepeatInterval"`
		Nodetype       int    `json:"nodetype"`
		Nodesubtype    int    `json:"nodesubtype"`
		Rid            int    `json:"rid"`
	}{}
	if err := json.Unmarshal(b, &tmp); err != nil {
		return err
	}
	t, err := time.Parse("2006-01-02  15:04:05", tmp.Date)
	if err == nil {
		e.StartDate = t
		e.EndDate = t.Add(time.Duration(tmp.Duration) * time.Second)
	} else {
		e.StartDateStr = tmp.Date
	}

	e.Nid = tmp.Nid
	e.EventName = tmp.EventName
	e.Duration = tmp.Duration
	e.Location = tmp.Location
	e.Notes = tmp.Notes
	e.RepeatType = tmp.RepeatType
	e.RepeatParam = tmp.RepeatParam
	e.RepeatCustom = tmp.RepeatCustom
	e.RepeatInterval = tmp.RepeatInterval
	e.Nodetype = tmp.Nodetype
	e.Nodesubtype = tmp.Nodesubtype
	e.Rid = tmp.Rid

	return nil
}
