package edsby_api

type (
	School struct {
		Nid           int      `json:"nid"`
		SchoolID      string   `json:"SchoolID"`
		SchoolName    string   `json:"SchoolName"`
		SchoolType    string   `json:"SchoolType"`
		SchoolFocus   string   `json:"SchoolFocus"`
		SchoolURL     string   `json:"SchoolURL"`
		GradeLevels   []string `json:"GradeLevels"`
		Email         string   `json:"Email"`
		StreetAddress string   `json:"StreetAddress"`
		City          string   `json:"City"`
		StateProvince string   `json:"StateProvince"`
		TimeZone      string   `json:"timeZone"`
		Country       string   `json:"Country"`
		PostalCode    string   `json:"PostalCode"`
		GridLocation  []string `json:"GridLocation"`
		Telephone     string   `json:"Telephone"`
		FaxNumber     string   `json:"FaxNumber"`
		Nodetype      int      `json:"nodetype"`
		Nodesubtype   int      `json:"nodesubtype"`
		Rid           int      `json:"rid"`
		client        *Client
	}
	SchoolInfo struct {
		Schools Schools `json:"schools"`
	}
	Schools []School
)

//
//func (s School) News() ([]News, error) {
//	var info NewsInfo
//	resource := s.client.getEndpoint(fmt.Sprintf(SchoolNewsEndpointFormat, s.Nid))
//	err := s.client.performGetRequest(resource, &info)
//	info.News.setClient(s.client)
//	return info.News, err
//}
//
//func (s School) Events() ([]Event, error) {
//	var info EventInfo
//	resource := s.client.getEndpoint(fmt.Sprintf(SchoolEventsEndpointFormat, s.Nid))
//	err := s.client.performGetRequest(resource, &info)
//	return info.Events, err
//}
//
//func (s *School) setClient(c *Client) {
//	s.client = c
//}
//
//func (s Schools) setClient(c *Client) Schools {
//	for i := range s {
//		s[i].setClient(c)
//	}
//	return s
//}
