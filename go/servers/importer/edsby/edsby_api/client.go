package edsby_api

import (
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/common/utils"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"
	"time"
)

type Client struct {
	Url      string         `json:"url"`
	Timezone *time.Location `json:"timezone"`
	commonModels.Credentials
}

var (
	oauth2Endpoint             = "/core/oauth2/token"
	SchoolInfoEndpoint         = "/api/schoolinfo"
	SchoolNewsEndpointFormat   = SchoolInfoEndpoint + "/%v/news"
	SchoolEventsEndpointFormat = SchoolInfoEndpoint + "/%v/event"
	oneDayDuration             = int(24 * time.Hour)
	allDayEventDuration        = (23 * time.Hour) + (59 * time.Minute) + (59 * time.Second)
)

func NewClient(clientId, clientSecret, clientUrl, timezone string) (*Client, error) {
	var c Client
	if len(clientSecret) == 0 || len(clientId) == 0 || len(clientUrl) == 0 {
		return &c, errors.New("invalid credentials")
	}

	c.Url = strings.TrimRight(clientUrl, "/")
	c.Timezone, _ = time.LoadLocation(timezone)
	c.Credentials = commonModels.Credentials{
		ClientID:     clientId,
		ClientSecret: clientSecret,
		BearerURL:    clientUrl + oauth2Endpoint,
	}

	return &c, c.RefreshToken(c.Url)
}
func (c *Client) performGetRequest(resource string, marshalTo interface{}) error {
	if err := c.RefreshToken(c.Url); err != nil {
		return err
	}
	req, err := http.NewRequest("GET", resource, nil)
	if err != nil {
		return err
	}

	req.Header.Add("Authorization", c.Token.GetAuthorizationHeader())

	return c.performRequest(req, marshalTo)
}
func (c *Client) performRequest(req *http.Request, marshalTo interface{}) error {
	r, err := http.DefaultClient.Do(req)
	if err != nil {
		return commonUtils.ErrorFmt("request failed", err, req.URL)
	}

	defer r.Body.Close()
	body, err := ioutil.ReadAll(r.Body)
	if r.StatusCode < 200 || r.StatusCode > 299 {
		return commonUtils.ErrorFmt("response returned with unexpected status code", r.StatusCode, string(body))
	}
	if err != nil {
		return commonUtils.ErrorFmt("response read error", r.StatusCode, req.URL)
	}
	if string(body) == "[]" {
		return nil
	}
	return json.Unmarshal(body, &marshalTo)
}
func (c *Client) performExplicitRequest(resource string) (*http.Response, error) {
	if err := c.RefreshToken(c.Url); err != nil {
		return &http.Response{}, err
	}
	req, err := http.NewRequest("GET", resource, nil)
	if err != nil {
		return &http.Response{}, err
	}
	req.Header.Add("Authorization", c.Token.GetAuthorizationHeader())

	r, err := http.DefaultClient.Do(req)
	if err != nil {
		return &http.Response{}, commonUtils.ErrorFmt("explicit request failed", err, req.URL)
	}
	return r, nil
}
func (c *Client) Schools() (Schools, error) {
	var info SchoolInfo
	err := c.performGetRequest(c.getEndpoint(SchoolInfoEndpoint), &info)
	return info.Schools, err
}
func (c *Client) SchoolById(schoolId string) (School, error) {
	var info SchoolInfo
	err := c.performGetRequest(c.getEndpoint(SchoolInfoEndpoint+"/"+schoolId), &info)
	if len(info.Schools) > 0 {
		return info.Schools[0], nil
	}
	return School{}, err
}
func (c *Client) News(schoolId string) ([]News, error) {
	var info NewsInfo
	resource := c.getEndpoint(fmt.Sprintf(SchoolNewsEndpointFormat, schoolId))
	err := c.performGetRequest(resource, &info)
	return info.News, err
}
func (c *Client) Events(schoolId string) ([]Event, error) {
	var info EventInfo
	resource := c.getEndpoint(fmt.Sprintf(SchoolEventsEndpointFormat, schoolId))
	err := c.performGetRequest(resource, &info)
	// Ensure that all-day events are given proper values
	// Edsby reuses fields and re-contextualizes them based on the event type
	//  - Duration [AllDay] number of days the event spans (duration of 1 meaning the event starts on startDateStr and ends on the same day)
	//	- Duration [NotAllDay] number of seconds the event is scheduled for
	for i, event := range info.Events {
		if !event.StartDate.IsZero() || len(event.StartDateStr) == 0 {
			continue
		}
		startDate, err := time.Parse("2006-01-02", event.StartDateStr)
		if err != nil {
			continue
		}
		info.Events[i].StartDate = startDate
		info.Events[i].EndDate = startDate.Add(time.Duration(oneDayDuration * event.Duration))
	}
	return info.Events, err
}
func (c *Client) Image(url string) (Image, error) {
	if url == "" {
		return Image{}, errors.New("invalid url")
	}
	i, e := c.getBase64(url)
	i.PictureId = url
	return i, e
}
func (c *Client) Definitions() (map[string]interface{}, error) {
	var m map[string]interface{}
	return m, c.performGetRequest(c.getEndpoint("/api/defs"), &m)
}
func (c *Client) getEndpoint(endpoint string) string {
	return c.Url + endpoint
}
func (c *Client) getBase64(resource string) (i Image, e error) {
	response, e := c.performExplicitRequest(c.getEndpoint(resource))
	if e != nil {
		return
	}
	defer response.Body.Close()
	body, e := ioutil.ReadAll(response.Body)
	if e != nil {
		return
	}
	i.ContentType = response.Header.Get("Content-Type")
	i.Data, e = toBase64(body, i.ContentType)
	return
}
