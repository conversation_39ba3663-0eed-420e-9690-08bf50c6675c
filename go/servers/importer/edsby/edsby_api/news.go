package edsby_api

import (
	"contentmanager/library/utils"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"strconv"
	"time"
)

type (
	News struct {
		Nid         int       `json:"nid"`
		Title       string    `json:"Title"`
		Picture     string    `json:"Picture"`
		PictureUrl  string    `json:"PictureUrl"`
		Body        string    `json:"Body"`
		Nodetype    int       `json:"nodetype"`
		Nodesubtype int       `json:"nodesubtype"`
		Rid         int       `json:"rid"`
		Date        time.Time `json:"date"`
		ExpireDate  time.Time `json:"ExpireDate"`
		ShowDate    time.Time `json:"ShowDate"`
		ShowTo      string    `json:"ShowTo"`
	}
	NewsState struct {
		Title      string    `json:"Title"`
		Picture    string    `json:"Picture"`
		Body       string    `json:"Body"`
		Date       time.Time `json:"date"`
		ExpireDate time.Time `json:"ExpireDate"`
		ShowDate   time.Time `json:"ShowDate"`
		ShowTo     string    `json:"ShowTo"`
	}
	NewsInfo struct {
		News NewsChain `json:"news"`
	}
	NewsChain []News
)

func (n News) ID() uuid.UUID {
	return utils.GenerateSatoriUUIDFromString("news-" + strconv.Itoa(n.Nid))
}
func (n News) PrivacyLevel() int {
	if n.ShowTo == "Authenticated" {
		return 2
	}
	return 0
}
func (n News) MediaID() uuid.UUID {
	if len(n.PictureUrl) == 0 {
		return uuid.Nil
	}
	return utils.GenerateSatoriUUIDFromString(n.PictureUrl)
}
func (n News) Src() string {
	return n.PictureUrl
}
func (n News) Alt() string {
	return ""
}
func (n News) IsEqual(state NewsState) bool {
	return n.ShowTo == state.ShowTo &&
		n.ShowDate == state.ShowDate &&
		n.Body == state.Body &&
		n.PictureUrl == state.Picture &&
		n.Date == state.Date &&
		n.ExpireDate == state.ExpireDate &&
		n.Title == state.Title
}
func (n News) IsEqualJson(jsonState json.RawMessage) bool {
	return n.IsEqual(ParseJsonNewsState(jsonState))
}
func (n News) GetStateMap() map[string]interface{} {
	return map[string]interface{}{
		"Title":      n.Title,
		"Picture":    n.Picture,
		"Body":       n.Body,
		"Date":       n.Date,
		"ExpireDate": n.ExpireDate,
		"ShowDate":   n.ShowDate,
		"ShowTo":     n.ShowTo,
	}
}
func ParseJsonNewsState(bytes []byte) NewsState {
	var temp = struct {
		State NewsState `json:"importInfo"`
	}{}
	_ = json.Unmarshal(bytes, &temp)
	return temp.State
}
func (n *News) UnmarshalJSON(b []byte) error {
	tmp := struct {
		Nid         int    `json:"nid"`
		Title       string `json:"Title"`
		Picture     string `json:"Picture"`
		PictureUrl  string `json:"PictureUrl"`
		Body        string `json:"Body"`
		Nodetype    int    `json:"nodetype"`
		Nodesubtype int    `json:"nodesubtype"`
		Rid         int    `json:"rid"`
		Date        string `json:"date"`
		ExpireDate  string `json:"ExpireDate"`
		ShowDate    string `json:"ShowDate"`
		ShowTo      string `json:"ShowTo"`
	}{}

	if err := json.Unmarshal(b, &tmp); err != nil {
		return err
	}
	if t, err := time.Parse("2006-01-02  15:04:05", tmp.Date); err == nil {
		n.Date = t
	}
	if t, err := time.Parse("2006-01-02", tmp.ExpireDate); err == nil {
		n.ExpireDate = t
	}
	if t, err := time.Parse("2006-01-02", tmp.ShowDate); err == nil {
		n.ShowDate = t
	}
	n.Nid = tmp.Nid
	n.Title = tmp.Title
	n.Picture = tmp.Picture
	n.PictureUrl = tmp.PictureUrl
	n.Body = tmp.Body
	n.Nodetype = tmp.Nodetype
	n.Nodesubtype = tmp.Nodesubtype
	n.Rid = tmp.Rid
	n.ShowTo = tmp.ShowTo

	return nil
}
