package edsby

import (
	adminServices "contentmanager/library/tenant/admin/services"
	commonModels "contentmanager/library/tenant/common/models"
	commonServices "contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/servers/importer/common/integration"
	"contentmanager/servers/importer/edsby/edsby_api"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/net/html"
	"net/url"
	"strings"
)

type (
	imageAttributes struct {
		alt string
		src string
	}
)

func (ia imageAttributes) MediaID() uuid.UUID {
	return utils.GenerateSatoriUUIDFromString(getNormalizedPictureUrl(ia.src))
}
func (ia imageAttributes) Alt() string {
	return ia.alt
}
func (ia imageAttributes) Src() string {
	return ia.src
}

type Imager interface {
	MediaID() uuid.UUID
	Alt() string
	Src() string
}

func NewImage(importer *integration.ImportContext, client *edsby_api.Client, img Imager) (commonModels.Media, error) {
	potentialMedia := commonModels.NewMedia(img.MediaID(), "", []uuid.UUID{})
	potentialMedia.Alt = img.Alt()
	potentialMedia.Origin = &ImportSource
	return uploadAndCreateMedia(importer, client, potentialMedia, img.Src())
}

// func uploadAndCreateMedia(importer integration.ImportContext, client *edsby_api.Client, media commonModels.Media, pictureUrl string) (n uuid.NullUUID, b bool) {
func uploadAndCreateMedia(importer *integration.ImportContext, client *edsby_api.Client, media commonModels.Media, pictureUrl string) (m commonModels.Media, err error) {
	exists, err := adminServices.GetMediaById(importer.DB, media.ID, false)
	if err == nil && exists.IsValid() {
		return exists, nil
	}
	image, err := client.Image(pictureUrl)
	if err != nil || !image.IsValid() {
		return m, err
	}
	if media.Filename, err = image.Filename(); err != nil {
		return m, err
	}
	if err = commonServices.UploadMedia(commonModels.UploadMedia{
		media.ID,
		image.Data,
		image.Data,
	}, importer.TenantID); err != nil {
		return m, err
	}
	media, err = adminServices.CreateMedia(importer.DB, media)
	if err != nil || !media.IsValid() {
		return m, err
	}
	return media, nil
	//return uuid.NullUUID{UUID: media.ID, Valid: true}, true
}
func getNormalizedPictureUrl(raw string) (normalized string) {
	parsed, err := url.Parse(raw)
	if err != nil {
		return
	}
	normalized = parsed.Path
	if q := parsed.Query(); q != nil {
		normalized = normalized + q.Get("size")
	}
	return
}
func findImagesInContent(htmlBody string) map[string]imageAttributes {
	var images = map[string]imageAttributes{}
	var token = html.NewTokenizer(strings.NewReader(htmlBody))
	for {
		tn := token.Next()
		if tn == html.ErrorToken {
			break
		}
		if tn == html.StartTagToken {
			if element := token.Token(); element.Data == "img" {
				attr := imageAttributes{}
				for _, v := range element.Attr {
					if v.Key == "src" {
						attr.src = v.Val
					}
					if v.Key == "alt" {
						attr.alt = v.Val
					}
				}
				images[element.String()] = attr
			}
		}
	}
	return images
}
func translateImageAttributes(importer *integration.ImportContext, client *edsby_api.Client, htmlBody string, images map[string]imageAttributes) string {
	for imgTag, attr := range images {
		if strings.Contains(htmlBody, imgTag) {
			media, err := NewImage(importer, client, attr)
			if err != nil {
				continue
			}
			newImage := fmt.Sprintf("<img src=\"/images/%s\" alt=\"%s\" />", media.ID.String(), media.Alt)
			htmlBody = strings.Replace(htmlBody, imgTag, newImage, -1)
		}
	}
	return htmlBody
}
