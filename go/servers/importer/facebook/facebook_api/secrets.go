package facebook_api

import (
	"encoding/json"
	"errors"
)

type Client struct {
	UserAccessToken string `json:"user_access_token"`
}

var (
	ErrInvalidUserAccessToken = errors.New("required field [user_access_token] is either missing or invalid")
)

func ValidateClientSecrets(data json.RawMessage) (json.RawMessage, error) {
	var client Client
	if err := json.Unmarshal(data, &client); err != nil {
		return data, err
	}
	if client.UserAccessToken == "" {
		return data, ErrInvalidUserAccessToken
	}
	_, err := client.GetAccounts()
	return data, err
}
