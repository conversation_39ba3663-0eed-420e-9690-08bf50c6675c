package google_calendar

import (
	"contentmanager/etc/conf"
	"contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"google.golang.org/api/calendar/v3"
	"time"
)

func TranslateEventToContent(event calendar.Event, calendarId string, sites []uuid.UUID) content.Content {
	ID := GCalendarConsistentId(event.Id)
	PublishAt := parseTime(event.Created)
	Settings := getImportedContentSettings(calendarId, event)
	return content.Content{
		ID: ID,
		Base: content.Base{
			PublishPeriod: content.PublishPeriod{
				PublishAt: &PublishAt,
			},
			SharableBase: auth.SharableBase{
				Sites: sites,
			},
		},
		Title:      event.Summary,
		Content:    event.Description,
		Route:      event.HtmlLink,
		Created:    PublishAt,
		Updated:    parseTime(event.Updated),
		Path:       utils.SanitizeLTree(ID.String()),
		Type:       commonModels.Event,
		Owner:      conf.ImporterAccountId,
		Publisher:  conf.ImporterAccountId,
		PageLayout: commonModels.Wysiwyg,
		Active:     true,
		Settings:   Settings,
		Tags:       driver.PgUUIDArray{},
	}

}
func parseTime(t string) time.Time {
	val, err := time.Parse(time.RFC3339, t)
	if err != nil {
		return time.Now()
	}
	return val
}
func getEventInfo(event calendar.Event) (start time.Time, end time.Time, isAllDay bool, err error) {
	if event.Start == nil || event.End == nil {
		return
	}
	// All Day events in GCal have no Datetime values, and use Date instead.
	if (len(event.Start.DateTime) == 0 || len(event.End.DateTime) == 0) &&
		len(event.Start.Date) > 0 && len(event.End.Date) > 0 {
		start, err = time.Parse("2006-1-2", event.Start.Date)
		end, err = time.Parse("2006-1-2", event.End.Date)
		isAllDay = true
		return start, end, isAllDay, err
	}
	timezone, err := time.LoadLocation(event.Start.TimeZone)
	if err != nil {
		return
	}
	start, err = time.ParseInLocation(time.RFC3339, event.Start.DateTime, timezone)
	if err != nil {
		return
	}
	end, err = time.ParseInLocation(time.RFC3339, event.End.DateTime, timezone)
	if err != nil {
		return
	}
	return start, end, isAllDay, err
}

func getImportedContentSettings(calendarId string, event calendar.Event) json.RawMessage {
	start, end, isAllDay, _ := getEventInfo(event)
	return utils.InterfaceToJsonb(map[string]interface{}{
		"startdate": start.UTC(),
		"enddate":   end.UTC(),
		"isAllDay":  isAllDay,
		"location": map[string]interface{}{
			"displayName": event.Location,
		},
		"imported": true,
		"importInfo": map[string]interface{}{
			"source":       ImportSource,
			"id":           event.Id,
			"updated_time": event.Updated,
			"externalId":   calendarId,
		},
	})
}
