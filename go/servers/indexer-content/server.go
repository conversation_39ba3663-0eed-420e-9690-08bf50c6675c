package main

import (
	"contentmanager/pkgs/search_v2/jobs/content"
	"contentmanager/pkgs/service_context"
	_ "go.uber.org/automaxprocs"
	"time"
)

func main() {
	serviceCtx := service_context.NewServiceContext("indexer")
	serviceCtx.Logger().Info().Msg("--------- Starting ---------")

	content.RunIndexContentJob(serviceCtx)

	serviceCtx.Logger().Info().Str("duration", time.Since(serviceCtx.StartedAt()).String()).Msg("--------- Finished ---------")

	serviceCtx.Exit()
	<-serviceCtx.Context().Done()

	// Provide some time for jobs finishing
	serviceCtx.Logger().Info().Msg("Stopping server in 5 seconds...")
	time.Sleep(5 * time.Second)

	serviceCtx.Logger().Info().Msg("Server stopped")
}
