package instagram

/* Deprecated (2025-01-07) No longer offer instagram support.

import (
	"fmt"
	"net/http"
	"strings"
	"testing"
)

func Test_Instagram(t *testing.T) {
	t.Skip("For manual testing only")

	// Arrange
	accessToken := ""
	requestURL := fmt.Sprintf("https://graph.instagram.com/me/media?fields=id,media_type,media_url,thumbnail_url,username,timestamp,permalink,children{media_url,thumbnail_url,timestamp,permalink}&access_token=%s", accessToken)
	request, err := http.NewRequest("GET", requestURL, strings.NewReader(``))
	if err != nil {
		t.Error(err)
		return
	}
	response, err := http.DefaultClient.Do(request)
	if err != nil {
		t.Error(err)
		return
	}
	t.Log(response)
	// Act
	// Assert
}
*/
