package instagram

/* Deprecated (2025-01-07) No longer offer instagram support.

import (
	"contentmanager/library/utils/converters"
	"encoding/json"
	"errors"
	"time"
)

type InstagramSettings struct {
	InstagramAccessToken string         `json:"instagramAccessToken"`
	InstagramExtended    *ExtendedToken `json:"instagramExtended,omitEmpty"`
}

func ProcessInstagramData(data json.RawMessage) (json.RawMessage, error) {
	var d InstagramSettings
	if err := json.Unmarshal(data, &d); err != nil {
		return []byte{}, err
	}
	next, err := processInstagramToken(d)
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(next)
}
func processInstagramToken(settings InstagramSettings) (InstagramSettings, error) {
	// Removing Instagram config if no access token is provided
	if len(settings.InstagramAccessToken) == 0 {
		settings.InstagramExtended = nil
		return settings, nil
	}
	// if Instagram config changed
	if settings.InstagramExtended == nil || settings.InstagramAccessToken != settings.InstagramExtended.Token {
		extendedToken := RenewTokenIfNecessary(ExtendedToken{
			Token:      settings.InstagramAccessToken,
			ExpiresAt:  time.Now(),
			LastError:  nil,
			LastUsedAt: nil,
		})
		if extendedToken.LastError != nil {
			return settings, errors.New("instagramAccessToken is invalid: " + converters.FromPointer(extendedToken.LastError))
		}
		settings.InstagramExtended = &extendedToken
		settings.InstagramAccessToken = extendedToken.Token
		return settings, nil
	}
	// if Instagram config is not changed but token is invalid
	if settings.InstagramExtended.LastError != nil {
		return settings, errors.New("instagramAccessToken is invalid: " + converters.FromPointer(settings.InstagramExtended.LastError))
	}

	return settings, nil
}
*/
