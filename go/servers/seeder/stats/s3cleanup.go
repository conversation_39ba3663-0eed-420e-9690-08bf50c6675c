package stats

import (
	"context"
	"fmt"
	"log"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/s3/types"
)

// DeleteAllObjectVersionsWithPrefix deletes all objects and their versions (including delete markers)
// from a versioned S3 bucket that match the given prefix
func DeleteAllObjectVersionsWithPrefix(ctx context.Context, bucketName, prefix string) error {
	// Load the AWS SDK configuration
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return fmt.Errorf("failed to load AWS configuration: %w", err)
	}

	// Create an S3 client
	client := s3.NewFromConfig(cfg)

	// Create a paginator for listing object versions
	paginator := s3.NewListObjectVersionsPaginator(client, &s3.ListObjectVersionsInput{
		Bucket: aws.String(bucketName),
		Prefix: aws.String(prefix),
	})

	// Store the objects to delete
	var objectsToDelete []types.ObjectIdentifier

	// Process each page of results
	for paginator.HasMorePages() {
		page, err := paginator.NextPage(ctx)
		if err != nil {
			return fmt.Errorf("failed to list object versions: %w", err)
		}

		// Process all versions (including delete markers)
		for _, version := range page.Versions {
			objectsToDelete = append(objectsToDelete, types.ObjectIdentifier{
				Key:       version.Key,
				VersionId: version.VersionId,
			})
		}

		// Process all delete markers
		for _, marker := range page.DeleteMarkers {
			objectsToDelete = append(objectsToDelete, types.ObjectIdentifier{
				Key:       marker.Key,
				VersionId: marker.VersionId,
			})
		}

		// If we've accumulated enough objects, delete them in batches
		// S3 DeleteObjects API allows up to 1000 objects per request
		if len(objectsToDelete) >= 1000 {
			if err := deleteObjectsBatch(ctx, client, bucketName, objectsToDelete); err != nil {
				return err
			}
			objectsToDelete = nil
		}
	}

	// Delete any remaining objects
	if len(objectsToDelete) > 0 {
		if err := deleteObjectsBatch(ctx, client, bucketName, objectsToDelete); err != nil {
			return err
		}
	}

	log.Printf("Successfully deleted all object versions with prefix '%s' from bucket '%s'", prefix, bucketName)
	return nil
}

// deleteObjectsBatch deletes a batch of objects from an S3 bucket
func deleteObjectsBatch(ctx context.Context, client *s3.Client, bucketName string, objects []types.ObjectIdentifier) error {
	fmt.Println(fmt.Sprintf("Deleting a batch of objects: %s - %s", *objects[0].Key, *objects[len(objects)-1].Key))
	_, err := client.DeleteObjects(ctx, &s3.DeleteObjectsInput{
		Bucket: aws.String(bucketName),
		Delete: &types.Delete{
			Objects: objects,
			// Set Quiet to true to minimize the response size
			Quiet: aws.Bool(true),
		},
	})

	if err != nil {
		return fmt.Errorf("failed to delete objects: %w", err)
	}

	return nil
}
