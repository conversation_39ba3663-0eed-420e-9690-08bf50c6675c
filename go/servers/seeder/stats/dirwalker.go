package stats

import (
	"fmt"
	"io/fs"
	"log"
	"os"
	"path/filepath"
	"strings"
)

func Walk(rootDir, outputFileName string) {

	// Ensure the provided path is a directory
	info, err := os.Stat(rootDir)
	if err != nil {
		log.Fatalf("Error accessing path %q: %v", rootDir, err)
	}
	if !info.IsDir() {
		log.Fatalf("Provided path %q is not a directory.", rootDir)
	}

	// 2. Create the output file
	outputFile, err := os.Create(outputFileName)
	if err != nil {
		log.Fatalf("Failed to create output file %q: %v", outputFileName, err)
	}
	defer outputFile.Close() // Ensure file is closed even on error

	fmt.Printf("Scanning directory: %s\n", rootDir)
	fmt.Printf("Writing output to: %s\n", outputFileName)

	// 3. Walk the directory recursively
	err = filepath.WalkDir(rootDir, func(path string, d fs.DirEntry, err error) error {
		// Handle potential errors during walk (e.g., permission issues)
		if err != nil {
			log.Printf("Warning: Error accessing path %q: %v. Skipping.", path, err)
			// Decide if you want to stop the walk entirely or just skip the problematic path.
			// Returning nil skips the problematic entry but continues the walk.
			// Returning the error would stop the entire walk.
			return nil // Continue walking
		}

		// Skip directories, process only files
		if d.IsDir() {
			return nil // Continue walking
		}

		// Check if it's a .go file
		if strings.HasSuffix(strings.ToLower(d.Name()), ".go") {
			fmt.Printf("  Adding: %s\n", path)

			// 4. Write file path header to output
			header := fmt.Sprintf("\n--- FILE: %s ---\n", path)
			_, err = outputFile.WriteString(header)
			if err != nil {
				log.Printf("Warning: Failed to write header for %q: %v", path, err)
				return nil // Continue walking, but log the error
			}

			// 5. Read file content
			content, err := os.ReadFile(path)
			if err != nil {
				log.Printf("Warning: Failed to read file %q: %v", path, err)
				// Write an error message into the output file for this specific file
				_, _ = outputFile.WriteString(fmt.Sprintf("[Error reading file: %v]\n", err))
				return nil // Continue walking
			}

			// 6. Write file content to output
			_, err = outputFile.Write(content)
			if err != nil {
				log.Printf("Warning: Failed to write content for %q: %v", path, err)
				return nil // Continue walking
			}

			// Add a newline after the content for better separation (optional)
			_, err = outputFile.WriteString("\n")
			if err != nil {
				log.Printf("Warning: Failed to write trailing newline for %q: %v", path, err)
				return nil // Continue walking
			}
		}

		return nil // Continue walking
	})

	// Handle errors returned by WalkDir itself (e.g., rootDir doesn't exist initially - though we checked)
	if err != nil {
		log.Fatalf("Error walking directory %q: %v", rootDir, err)
	}

	fmt.Println("\nFinished creating", outputFileName)
}
