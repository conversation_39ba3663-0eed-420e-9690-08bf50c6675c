package stats

import (
	"context"
	"encoding/csv"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"os"
	"time"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type BucketStats struct {
	BucketName    string
	TotalObjects  int64
	TotalSize     int64
	LastModified  time.Time
	Prefix        string
	PrefixObjects int64
	PrefixSize    int64
}

func GetS3Stats() {
	// Load AWS configuration
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		fmt.Printf("Unable to load SDK config: %v\n", err)
		return
	}

	// Create S3 client
	client := s3.NewFromConfig(cfg)

	// List all buckets
	buckets, err := client.ListBuckets(context.TODO(), &s3.ListBucketsInput{})
	if err != nil {
		fmt.Printf("Unable to list buckets: %v\n", err)
		return
	}

	// Create CSV file
	file, err := os.Create("s3_stats.csv")
	if err != nil {
		fmt.Printf("Could not create CSV file: %v\n", err)
		return
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	// Write CSV header
	headers := []string{
		"Bucket Name",
		"Total Objects",
		"Total Size (Bytes)",
		"Last Modified",
		"Prefix",
		"Prefix Objects",
		"Prefix Size (Bytes)",
	}
	if err := writer.Write(headers); err != nil {
		fmt.Printf("Error writing headers: %v\n", err)
		return
	}

	// Process each bucket
	for _, bucket := range buckets.Buckets {
		fmt.Printf("Processing bucket: %s\n", *bucket.Name)

		// Get first-level prefixes
		prefixes, err := getFirstLevelPrefixes(client, *bucket.Name)
		if err != nil {
			fmt.Printf("Error getting prefixes for bucket %s: %v\n", *bucket.Name, err)
			continue
		}

		// Get total bucket stats
		totalStats, err := getBucketStats(client, *bucket.Name, "")
		if err != nil {
			fmt.Printf("Error getting stats for bucket %s: %v\n", *bucket.Name, err)
			continue
		}

		// Write total bucket stats
		if err := writer.Write([]string{
			*bucket.Name,
			fmt.Sprintf("%d", totalStats.TotalObjects),
			fmt.Sprintf("%d", totalStats.TotalSize),
			bucket.CreationDate.Format(time.RFC3339),
			"",
			"",
			"",
		}); err != nil {
			fmt.Printf("Error writing bucket stats: %v\n", err)
			continue
		}

		// Process each prefix
		for _, prefix := range prefixes {
			prefixStats, err := getBucketStats(client, *bucket.Name, prefix)
			if err != nil {
				fmt.Printf("Error getting stats for prefix %s: %v\n", prefix, err)
				continue
			}

			if err := writer.Write([]string{
				*bucket.Name,
				"",
				"",
				"",
				prefix,
				fmt.Sprintf("%d", prefixStats.TotalObjects),
				fmt.Sprintf("%d", prefixStats.TotalSize),
			}); err != nil {
				fmt.Printf("Error writing prefix stats: %v\n", err)
				continue
			}
		}
	}

	fmt.Println("Stats have been written to s3_stats.csv")
}

func getFirstLevelPrefixes(client *s3.Client, bucketName string) ([]string, error) {
	input := &s3.ListObjectsV2Input{
		Bucket:    &bucketName,
		Delimiter: aws.String("/"),
	}

	var prefixes []string
	paginator := s3.NewListObjectsV2Paginator(client, input)
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, err
		}

		for _, prefix := range output.CommonPrefixes {
			prefixes = append(prefixes, *prefix.Prefix)
		}
	}

	return prefixes, nil
}

func getBucketStats(client *s3.Client, bucketName, prefix string) (BucketStats, error) {
	var stats BucketStats
	input := &s3.ListObjectsV2Input{
		Bucket: &bucketName,
		Prefix: &prefix,
	}

	paginator := s3.NewListObjectsV2Paginator(client, input)
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(context.TODO())
		if err != nil {
			return stats, err
		}

		for _, object := range output.Contents {
			stats.TotalObjects++
			stats.TotalSize += *object.Size
			if object.LastModified.After(stats.LastModified) {
				stats.LastModified = *object.LastModified
			}
		}
	}

	return stats, nil
}
