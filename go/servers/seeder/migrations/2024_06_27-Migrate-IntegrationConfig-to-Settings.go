package migrations

//
//type (
//	tenantConfMap struct {
//		Edsby               map[string]interface{} `json:"edsby"`
//		Outlook             map[string]interface{} `json:"outlook"`
//		FBUserAccessToken   string                 `json:"FBUserAccessToken"`
//		FBUserID            string                 `json:"FBUserID"`
//		IdentityCertificate string                 `json:"identityCertificate"`
//		AdminUser           string                 `json:"adminUser"`
//	}
//	siteConfMap struct {
//		Edsby            []integrationModels.Config `json:"edsby"`
//		Outlook          []integrationModels.Config `json:"outlook"`
//		FacebookPageId   string
//		GoogleCalendarID string
//	}
//)
//
//func MigrateIntegrationConfig() {
//	tenancyDB := db.GetConnection(db.ProdExternalIP, db.TenancyDBName)
//	tenancyDBInstance, _ := tenancyDB.DB()
//	defer tenancyDBInstance.Close()
//
//	var tenants = make([]tenancyModels.Tenant, 0)
//	if err := tenancyDB.Where("active = true ").Find(&tenants).Error; err != nil {
//		log.Println("error: failed to get tenants")
//		panic(err)
//	}
//	tenantSiteMap := getTenantSiteMap(tenancyDB)
//
//	for _, tenant := range tenants {
//		values := getTenantSettingsRecords(tenant.Settings)
//		if len(values) == 0 {
//			continue
//		}
//		tenantSites := tenantSiteMap[tenant.ID]
//		accumulatedConfig := accumulateSiteConfig(tenantSites)
//		newSettingsFromConfig := createSettingsFromAccumulatedConfig(accumulatedConfig)
//		values = append(values, newSettingsFromConfig...)
//
//		fmt.Printf("tenant: [%s]\nedsby: [%v]\noutlook: [%v]\nfacebook: [%v]\ngooglecalendar: [%v]\n", tenant.Name, len(accumulatedConfig.edsby), len(accumulatedConfig.outlook), len(accumulatedConfig.facebook), len(accumulatedConfig.googleCalendar))
//		tenantDB := db.GetConnection(db.ProdExternalIP, tenant.Server)
//		if err := tenantDB.Save(&values).Error; err != nil {
//			panic(err)
//		}
//	}
//
//	log.Println("\n--- Completed ----")
//}
//
//type (
//	standardizedConfigAcc map[string]struct {
//		config integrationModels.Config
//		sites  driver.PgUUIDArray
//	}
//	stringConfigAcc map[string]driver.PgUUIDArray
//	configAcc       struct {
//		edsby          standardizedConfigAcc
//		outlook        standardizedConfigAcc
//		facebook       stringConfigAcc
//		googleCalendar stringConfigAcc
//	}
//)
//
//func getConfigAccumulator() configAcc {
//	return struct {
//		edsby          standardizedConfigAcc
//		outlook        standardizedConfigAcc
//		facebook       stringConfigAcc
//		googleCalendar stringConfigAcc
//	}{
//		edsby:          make(standardizedConfigAcc),
//		outlook:        make(standardizedConfigAcc),
//		facebook:       make(stringConfigAcc),
//		googleCalendar: make(stringConfigAcc),
//	}
//}
//func createSettingsFromAccumulatedConfig(configAccumulator configAcc) []settings.Settings {
//	var values = make([]settings.Settings, 0)
//	if len(configAccumulator.facebook) > 0 {
//		for facebookPageId, sites := range configAccumulator.facebook {
//			values = append(values, settings.Settings{
//				Name:        "Facebook",
//				Description: "Facebook Page ID",
//				Type:        "facebook",
//				Sites:       sites,
//				Data: utils.MapStringInterfaceToBytes(map[string]interface{}{
//					"FacebookPageId": facebookPageId,
//				}),
//				Public: false,
//				Active: true,
//			})
//		}
//	}
//	if len(configAccumulator.googleCalendar) > 0 {
//		for googleCalendarID, sites := range configAccumulator.googleCalendar {
//			values = append(values, settings.Settings{
//				Name:        "Google-Calendar",
//				Description: "Google Calendar ID",
//				Type:        "google-calendar",
//				Sites:       sites,
//				Data: utils.MapStringInterfaceToBytes(map[string]interface{}{
//					"GoogleCalendarID": googleCalendarID,
//				}),
//				Public: false,
//				Active: true,
//			})
//		}
//	}
//	if len(configAccumulator.edsby) > 0 {
//		for configId, rest := range configAccumulator.edsby {
//			values = append(values, settings.Settings{
//				Name:        "Edsby-" + configId,
//				Description: "Edsby configuration for EdsbyID:" + configId,
//				Type:        "edsby",
//				Sites:       rest.sites,
//				Data:        utils.InterfaceToJsonb(rest.config),
//				Public:      false,
//				Active:      true,
//			})
//		}
//	}
//	if len(configAccumulator.outlook) > 0 {
//		for configId, rest := range configAccumulator.outlook {
//			values = append(values, settings.Settings{
//				Name:        "Outlook-" + configId,
//				Description: "Outlook configuration for CalendarID:" + configId,
//				Type:        "outlook",
//				Sites:       rest.sites,
//				Data:        utils.InterfaceToJsonb(rest.config),
//				Public:      false,
//				Active:      true,
//			})
//		}
//	}
//	return values
//}
//func accumulateSiteConfig(sites []tenancyModels.Site) configAcc {
//	configAccumulator := getConfigAccumulator()
//	for _, site := range sites {
//		confMap := siteConfMap{}
//		json.Unmarshal(site.Settings, &confMap)
//		if len(confMap.FacebookPageId) > 0 {
//			sl := configAccumulator.facebook[confMap.FacebookPageId]
//			configAccumulator.facebook[confMap.FacebookPageId] = append(sl, site.ID)
//		}
//		if len(confMap.GoogleCalendarID) > 0 {
//			sl := configAccumulator.googleCalendar[confMap.GoogleCalendarID]
//			configAccumulator.googleCalendar[confMap.GoogleCalendarID] = append(sl, site.ID)
//		}
//		if len(confMap.Outlook) > 0 {
//			for _, config := range confMap.Outlook {
//				accumulatedConfig := configAccumulator.outlook[config.Id]
//				accumulatedConfig.config = config
//				accumulatedConfig.sites = append(accumulatedConfig.sites, site.ID)
//				configAccumulator.outlook[config.Id] = accumulatedConfig
//			}
//		}
//		if len(confMap.Edsby) > 0 {
//			for _, config := range confMap.Edsby {
//				accumulatedConfig := configAccumulator.edsby[config.Id]
//				accumulatedConfig.config = config
//				accumulatedConfig.sites = append(accumulatedConfig.sites, site.ID)
//				configAccumulator.edsby[config.Id] = accumulatedConfig
//			}
//		}
//	}
//	return configAccumulator
//}
//func getTenantSettingsRecords(tenantSettings []byte) []settings.Settings {
//	var confMap = tenantConfMap{}
//	json.Unmarshal(tenantSettings, &confMap)
//
//	var values []settings.Settings
//	if confMap.Edsby != nil {
//		b, _ := json.Marshal(confMap.Edsby)
//		values = append(values, settings.Settings{
//			Name:        "Edsby-Secrets",
//			Description: "Edsby Secrets",
//			Type:        settings.SettingsType("edsby-secrets"),
//			Sites:       nil,
//			Data:        b,
//			Public:      false,
//			Active:      true,
//		})
//	}
//	if confMap.Outlook != nil {
//		b, _ := json.Marshal(confMap.Outlook)
//		values = append(values, settings.Settings{
//			Name:        "Outlook-Secrets",
//			Description: "Outlook Secrets",
//			Type:        settings.SettingsType("outlook-secrets"),
//			Sites:       nil,
//			Data:        b,
//			Public:      false,
//			Active:      true,
//		})
//	}
//	if confMap.FBUserAccessToken != "" {
//		var x = map[string]string{
//			"FBUserId":          confMap.FBUserID,
//			"FBUserAccessToken": confMap.FBUserAccessToken,
//		}
//		b, _ := json.Marshal(x)
//		values = append(values, settings.Settings{
//			Name:        "Facebook-Secrets",
//			Description: "Facebook Secrets",
//			Type:        settings.SettingsType("facebook-secrets"),
//			Sites:       nil,
//			Data:        b,
//			Public:      false,
//			Active:      true,
//		})
//	}
//	if confMap.IdentityCertificate != "" {
//		var x = map[string]string{
//			//"identityCertificate": confMap.IdentityCertificate,
//			"adminUser": confMap.AdminUser,
//		}
//		b, _ := json.Marshal(x)
//		values = append(values, settings.Settings{
//			Name:        "Google-Calendar-Secrets",
//			Description: "Google Calendar Secrets",
//			Type:        settings.SettingsType("google-calendar-secrets"),
//			Sites:       nil,
//			Data:        b,
//			Public:      false,
//			Active:      true,
//		})
//	}
//	return values
//}
//func getTenantSiteMap(tenancyDB *gorm.DB) map[uuid.UUID][]tenancyModels.Site {
//	var tenantSites = make([]tenancyModels.Site, 0)
//	if err := tenancyDB.Find(&tenantSites).Error; err != nil {
//		panic(err)
//	}
//	var tenantSiteMap = map[uuid.UUID][]tenancyModels.Site{}
//	for _, site := range tenantSites {
//		tenantSiteMap[site.TenantID] = append(tenantSiteMap[site.TenantID], site)
//	}
//	return tenantSiteMap
//}
