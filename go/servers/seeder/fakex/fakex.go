package fakex

import (
	"github.com/brianvoe/gofakeit/v6"
	"math/rand"
	"strings"
)

func EmailAmazon() string {
	return `success+` + strings.Split(gofakeit.Email(), "@")[0] + `@simulator.amazonses.com`
}

func RandomSample[T any](slice []T, min, max, empty int) []T {
	if max <= 0 || max > len(slice) {
		max = len(slice)
	}

	if min < 0 {
		min = 0
	}

	if min > max {
		min = max
	}

	// Check if we should return an empty slice based on the empty probability
	if empty > 0 && rand.Intn(100) < empty {
		return make([]T, 0)
	}

	// Determine the size of the resulting slice
	size := min
	if max > min {
		size += rand.Intn(max - min + 1)
	}

	// Create a copy of the original slice to shuffle
	tempSlice := make([]T, len(slice))
	copy(tempSlice, slice)

	// Shuffle the temporary slice
	rand.Shuffle(len(tempSlice), func(i, j int) {
		tempSlice[i], tempSlice[j] = tempSlice[j], tempSlice[i]
	})

	// Return a slice of the first 'size' elements from the shuffled slice
	return tempSlice[:size]

}

func RandomElement[T any](slice []T) T {
	return slice[rand.Intn(len(slice))]
}

func PrivacyLevel(staffOnlyProbability int) int {
	if staffOnlyProbability <= 0 {
		return 0
	}
	if staffOnlyProbability >= 100 {
		return 2
	}
	randomNum := rand.Intn(100)
	if randomNum < staffOnlyProbability {
		return 2
	}

	return 0
}
