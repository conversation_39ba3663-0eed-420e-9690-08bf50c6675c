package report

import (
	"encoding/csv"
	"log"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// SiteContents
// Creates a report of Documents, Pages, News, Events for a district.
// dbHost is either db.LocalExternalIP | db.TestExternalIP | db.ProdExternalIP
func SiteContents(saveToDir, dbHost, tenantId string) {
	rows := GetSiteContent(dbHost, tenantId)
	write(saveToDir, rows)
}
func write(dir string, rows [][]string) {
	path := getPath(dir)
	if err := os.MkdirAll(filepath.Dir(path), 0770); err != nil {
		panic(err)
	}
	f, err := os.Create(path)
	if err != nil {
		panic(err)
	}
	defer f.Close()
	writer := csv.NewWriter(f)
	defer writer.Flush()
	for _, row := range rows {
		if err := writer.Write(row); err != nil {
			log.Println(err)
		}
	}
}
func getPath(dir string) string {
	filename := time.Now().Format("2006-01-02-15-04-05") + "_siteContents.csv"
	if strings.HasSuffix(dir, "/") || strings.HasSuffix(dir, "\\") {
		return dir + filename
	}
	if strings.Contains(dir, "/") {
		return dir + "/" + filename
	} else {
		return dir + "\\" + filename
	}
}
func (sc siteContent) CSVHeaders() []string {
	return []string{"Title", "Type", "Site", "EditLink", "PublicLink", "PrivacyLevel", "PublishAt", "ExpireAt"}
}
func (sc siteContent) CSVRow() []string {
	return []string{sc.Title, sc.Type, sc.Site, sc.EditLink, sc.PublicLink, sc.PrivacyLevel, sc.PublishAt, sc.ExpireAt}
}
