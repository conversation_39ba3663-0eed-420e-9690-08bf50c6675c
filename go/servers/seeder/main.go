package main

import (
	"contentmanager/pkgs/service_context"
	"contentmanager/servers/seeder/seed_notifications"
	"contentmanager/servers/seeder/stats"
	uuid "github.com/satori/go.uuid"
)

func init() {
	/*
		CM_DBHOST=***********
		CM_DBPORT=5432
		CM_DBSERVER=cm_multitenancy
		CM_DBUSER=contentmanager_application_user
		CM_DBPASSWORD=***
	*/
	//os.Setenv("CM_DBHOST", "***********")
	//os.Setenv("CM_DBUSER", "contentmanager_application_user")
	//os.Setenv("CM_DBPASSWORD", "***")
}

// imagine_everything_forms_

func main() {
	stats.Walk("D:\\projects\\azure\\contentmanager\\go\\pkgs\\queries", "output.txt")
	//serviceCtx := service_context.NewServiceContext("indexer-documents")
	//misc.SeedFragments(serviceCtx)

	// jobs.ProcessResourcesForAllTenants()
	// stats.GetS3Stats() // long running
	//if err := stats.DeleteAllObjectVersionsWithPrefix(context.Background(), "imagineeverything", "db_backup/logs/contentmanager-test/"); err != nil {
	//	panic(err)
	//}
	//
	//if _, err := stats.CalculateDeletedVersionsSize(context.Background(), "imagineeverything", 5, `D:\deleted-keys.txt`); err != nil {
	//	panic(err)
	//}

	// perf.RunMediaLoad()
	//sc := service_context.NewServiceContext("seeder")
	//migrations.MigrateGSignin2ToSocialLogin(sc)
	// new_tenancy.Main()
	//documents.RunMigrationOnceFromLocal()
	// perf.RunLoadTest()
	// tpls_exp_imp.Export()
	//peel_csv_events.Load()
	//cbe_csv_securitygroup.Load()
	//cbe_csv_securitygroup.UpdateEmails()
	//cbe_site_settings.Update()
	//migrations.MigrateRoles()
	//cbe_calendar.Main()
	//migrations.MigrateIntegrationConfig()
	return

	//gofakeit.Seed(0)
	//
	//logging.ServiceName = "SEEDER"
	//log := logging.RootLogger()
	//log.Info().Msg("Starting")
	//ctx := logging.InjectLoggerInContext(context.Background())
	//
	//tenancyServiceConfig := shared.IParseConfigAdapter().ParseConfig()
	//cache.ICacheAdapter().CacheObject(conf.TenancySiteUrlCachekey, tenancyServiceConfig.AdminSiteUrl, false)
	//cache.ICacheAdapter().CacheObject(conf.TenancyConfigCacheKey, tenancyServiceConfig, false)
	//database.ITenancyDBAdapter().CacheTenancyDatabaseConnection(tenancyServiceConfig)
	//tenantDBAccessor := database2.ITenantDBAdapter()
	//
	//Seed(ctx, database.ITenancyDBAdapter().GetTenancyDatabaseConnection(), tenantDBAccessor)
}

func Seed(sc service_context.ServiceContext) {
	tenantID := uuid.FromStringOrNil("08077abf-4aa2-4bfa-ac81-9d31c60fcfe4")
	db := sc.TenantDB(tenantID)

	// seed_notifications.SeedTopics(sc.Context(), tenantID, db, sc.TenancyDB())
	// seed_notifications.SeedSubscribers(sc.Context(), db)
	// seed_notifications.SeedSubscriptions(sc.Context(), db)
	seed_notifications.SeedAuditRecords(sc.Context(), db)
}
