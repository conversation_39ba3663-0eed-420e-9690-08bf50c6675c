package fs

import "testing"

// Example test cases
func Test_ExampleUsage(t *testing.T) {
	testCases := []string{
		"My Document.pdf",             // Normal case
		"COM1.txt",                    // Windows reserved name
		"../../../etc/passwd",         // Path injection attempt
		"File:*?.txt",                 // Invalid characters
		"My...Long...File...Name.txt", // Multiple dots
		" Spaces Everywhere ",         // Spaces
		"日本語のファイル名.txt",               // Unicode
		"",                            // Empty string
		".",                           // Single dot
		"..",                          // Double dot
		"Very long filename that exceeds the maximum length allowed by most filesystems and should be truncated automatically Very long filename that exceeds the maximum length allowed by most filesystems and should be truncated automatically.txt",
	}

	for _, test := range testCases {
		safe := SanitizeFilename(test)
		t.Logf("Original: \n%s, Safe: \n%s", test, safe)
	}
}
