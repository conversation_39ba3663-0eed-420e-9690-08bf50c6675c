package seed_notifications

import (
	models2 "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	models3 "contentmanager/pkgs/notifications/models"
	"context"
	"github.com/brianvoe/gofakeit/v6"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

func SeedTopics(ctx context.Context, tenantID uuid.UUID, db, tenancyDB *gorm.DB) {
	var count int64
	db.Table("topic").Count(&count)
	if count > 0 {
		return
	}

	var accounts []identity.Account
	if err := db.Limit(100).Find(&accounts).Error; err != nil || len(accounts) == 0 {
		panic("No account found in TenantDB")
	}

	var relay models3.Relay
	if err := db.First(&relay); err != nil {
		// TODO: insert correct config if required
		relay = models3.Relay{
			Type: "plain_smtp",
			XData: []byte(`{
				"Host": "email-smtp.ca-central-1.amazonaws.com",
				"Port": 587,
				"Sender": "<EMAIL>",
				"Password": "<Password>",
				"ReplyTo": "<EMAIL>",
				"Username": "AKIA3RQ4JNO7XSU73BZ5"
			}`),
		}
		db.Save(&relay)
	}

	var sites []models2.Site
	tenancyDB.Where("tenant_id = ?", tenantID).Find(&sites)

	var busRoutes []commonModels.BusRoute
	db.Find(&busRoutes)

	var topics []models3.Topic
	for _, b := range busRoutes {
		id := b.ID
		topic := models3.Topic{
			TopicType:    models3.BusAlert,
			DeliveryType: models3.Email,
			RelayID:      relay.ID,
			BusRouteID:   &id,
			Active:       true,
		}
		topics = append(topics, topic)
	}

	for _, s := range sites {
		id := s.ID
		topic := models3.Topic{
			TopicType:    models3.Alert,
			DeliveryType: models3.Email,
			RelayID:      relay.ID,
			SiteID:       &id,
			Active:       true,
		}
		topic2 := models3.Topic{
			TopicType:    models3.News,
			DeliveryType: models3.Email,
			RelayID:      relay.ID,
			SiteID:       &id,
			Active:       true,
		}
		topics = append(topics, topic, topic2)
	}

	now := gofakeit.DateRange(time.Now().Add(-12*30*24*time.Hour), time.Now())
	for i := 0; i < len(topics); i++ {
		topics[i].Track(now, accounts[gofakeit.IntRange(0, len(accounts)-1)].ID)
	}

	if err := db.Create(&topics).Error; err != nil {
		logging.FromContext(ctx).Error().Err(err).Msg("seed topics")
		panic(err)
	}
}
