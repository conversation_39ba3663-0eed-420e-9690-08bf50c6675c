package new_tenancy

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/servers/seeder/db"
	"errors"
	"fmt"
	"github.com/gocarina/gocsv"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"os"
	"regexp"
	"strings"
)

type EnvironmentConfig struct {
	InternalDatabaseIP string
	ExternalDatabaseIP string
	TenantFullName     string
	TenantShortName    string
	TenantDatabaseName string
	PathToCsvFile      string

	AdminURLSubdomain string
	IEDomainSuffix    string
	EmailDomain       string
	TenantID          uuid.UUID
	Timezone          string
}

var (
	ErrInvalidEnvironmentType = errors.New("invalid environment type")
	ErrInvalidShortName       = errors.New("invalid tenant short name")
)

const (
	Test       = "test"
	Production = "production"
	Local      = "local"
)

type EnvironmentConfigParams struct {
	EnvironmentType   string
	TenantShortName   string
	TenantFullName    string
	Timezone          string
	PathToCSV         string
	EmailDomain       string
	AdminURLSubdomain string
}

func NewEnvironmentConfig(p EnvironmentConfigParams) (EnvironmentConfig, error) {
	if p.EnvironmentType != Test && p.EnvironmentType != Local && p.EnvironmentType != Production {
		return EnvironmentConfig{}, ErrInvalidEnvironmentType
	}
	if len(p.TenantShortName) == 0 {
		return EnvironmentConfig{}, ErrInvalidShortName
	}
	var env = EnvironmentConfig{
		TenantShortName:    p.TenantShortName,
		TenantFullName:     p.TenantFullName,
		TenantDatabaseName: strings.ToLower("cm_" + p.TenantShortName),
		TenantID:           utils.GenerateSatoriUUIDFromString(p.TenantShortName),
		Timezone:           p.Timezone,
		PathToCsvFile:      p.PathToCSV,
		EmailDomain:        p.EmailDomain,
		AdminURLSubdomain:  p.AdminURLSubdomain,
	}
	switch p.EnvironmentType {
	case Local:
		env.InternalDatabaseIP = db.LocalInternalHost
		env.ExternalDatabaseIP = db.LocalExternalIP
		env.IEDomainSuffix = "cmdesign.imagineeverything.com.localhost"
		env.AdminURLSubdomain = env.AdminURLSubdomain + ".contentmanager.imagineeverything.com"
	case Test:
		env.InternalDatabaseIP = db.TestInternalHost
		env.ExternalDatabaseIP = db.TestExternalIP
		env.IEDomainSuffix = "test.imagineeverything.ca"
		env.AdminURLSubdomain = env.AdminURLSubdomain + ".contentmanager.imagineeverything.ca"
	case Production:
		env.InternalDatabaseIP = db.ProdInternalHost
		env.ExternalDatabaseIP = db.ProdExternalIP
		env.IEDomainSuffix = "cmdesign.imagineeverything.com"
		env.AdminURLSubdomain = env.AdminURLSubdomain + ".contentmanager.imagineeverything.com"
	}
	return env, nil
}
func (e EnvironmentConfig) GetExistingOrNewTenant(db *gorm.DB) (multitenancy.Tenant, bool) {
	var tenant multitenancy.Tenant
	if err := db.Where("id = ?", e.TenantID).First(&tenant).Error; err != nil {
		return e.NewTenant(), false
	}
	return tenant, true
}
func (e EnvironmentConfig) NewTenant() multitenancy.Tenant {
	return multitenancy.Tenant{
		Entity:      commonModels.Entity{ID: e.TenantID},
		Host:        e.InternalDatabaseIP,
		Server:      e.TenantDatabaseName,
		Name:        e.TenantShortName,
		Description: e.TenantFullName,
		DBUser:      "contentmanager_application_user",
		DBPassword:  "4CZucQBCt6wzr7v3QoHu",
		Settings:    []byte(fmt.Sprintf("{\"timezone\": \"%s\"}", e.Timezone)),
		Active:      true,
		AdminURL:    e.AdminURLSubdomain,
	}
}
func (e EnvironmentConfig) GetMultiTenancyDB() *gorm.DB {
	return db.GetConnection(e.ExternalDatabaseIP, db.TenancyDBName)
}
func (e EnvironmentConfig) GetTenantDB() *gorm.DB {
	if len(e.TenantDatabaseName) <= 3 || !strings.HasPrefix(e.TenantDatabaseName, "cm_") {
		return nil
	}
	return db.GetConnection(e.ExternalDatabaseIP, e.TenantDatabaseName)
}

func (e EnvironmentConfig) GetSchoolsFromCSV() ([]*School, error) {
	var schools []*School
	schoolsFile, err := os.OpenFile(e.PathToCsvFile, os.O_RDWR|os.O_CREATE, os.ModePerm)
	if err != nil {
		return schools, err
	}
	defer schoolsFile.Close()

	if err := gocsv.UnmarshalFile(schoolsFile, &schools); err != nil { // Load clients from file
		return schools, err
	}
	return schools, nil
}

var (
	rxp = regexp.MustCompile("[.'\" \\s]")
)

func (e EnvironmentConfig) getDesignDomainName(schoolDomain string) string {
	var prefix string
	withoutWWW := strings.Replace(schoolDomain, "www.", "", 1)
	if firstDotIndex := strings.Index(withoutWWW, "."); len(withoutWWW) <= firstDotIndex {
		prefix = rxp.ReplaceAllString(withoutWWW, "")
	} else {
		prefix = rxp.ReplaceAllString(withoutWWW[0:firstDotIndex], "")
	}
	var subdomain string
	if strings.ToLower(prefix) == strings.ToLower(e.TenantShortName) {
		subdomain = e.TenantShortName
	} else {
		subdomain = prefix + "-" + e.TenantShortName
	}
	return strings.ToLower(subdomain + "." + e.IEDomainSuffix)
}
