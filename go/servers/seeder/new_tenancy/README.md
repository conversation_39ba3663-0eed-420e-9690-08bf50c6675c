## Creating New Tenancy in Content Manager #
============================================
* This does not include the networking / DNS side of on-boarding a new tenant.


1. Download Project overview
   1. Project Overview should be a google sheets or excel spreadsheet that follows a generally standard template.
2. Deploy Database
   (*) in contentmanager/database/scripts/<environment>/[local|test|production]Databases.conf
   1. Copy an existing line and paste below
   *: !important: The roll-out script expects tab separated columns
   *: !important: it's very hard to notice if something is tab separated or not, as the spacing is not consistent
   *: !important: There should be a trailing/empty new line at the bottom of the file.

       [Example - database/scripts/development/localDatabases.conf]

       (Before Change)
      ```
           1| [MULTITENANCY]
           2| cm_multitenancy	postgres	apppass!
           3|
           4| [TENANT]
           5| CSSD	Calgary Catholic School District	cssd.imagineeverything.com	localhost	5432	contentmanager_application_user	4CZucQBCt6wzr7v3QoHu
           6|
      ```
       (After Change)
      ```
           1| [MULTITENANCY]
           2| cm_multitenancy	postgres	apppass!
           3|
           4| [TENANT]
           5| CSSD	Calgary Catholic School District	cssd.imagineeverything.com	localhost	5432	contentmanager_application_user	4CZucQBCt6wzr7v3QoHu
           6| CTT	Christ The Teacher	ctt.imagineeverything.com	localhost	5432	contentmanager_application_user	4CZucQBCt6wzr7v3QoHu
           7|
      ```
   2. Run db_deploy in the location you're creating the tenancy for
   *) [local] - ./database/scripts/development/pushToLocal.sh localDatabases.conf deploy_only
   *) [test]  - ./database/scripts/test/pushToTest.sh testDatabases.conf
   *) [prod]  - ./database/scripts/production/pushToProduction.sh productionDatabases_primary.conf
   3. Notices will appear in the deployment process, if no errors are returned, the process will return OK and commit the transaction.
3. Update csv tag field names as needed in the School model (new_tenancy/school.go)
4. Update Parameters of NewEnvironmentConfig
   1. environmentType	: Where the tenancy will be created - ["local" | "test" | "production"]
   2. tenantShortName	: The shorthand name for a District (e.g) CSSD for Calgary Catholic School District
   3. tenantFullName		: The full name of the tenant (e.g) Calgary Catholic School District
   4. timezone			: Timezone in the format of "America/Edmonton" - Should be consumable by golang time.loadLocation
   5. pathToCSV			: Absolute filepath to the CSV file downloaded in step 1
   6. emailDomain       : the expected domain used in emails for this tenant (e.g "cssd.ab.ca" for emails like "<EMAIL>")
5. Start the program
6. Accept or decline the confirmation prompt
   1. The planned output will be printed to the console, showing Site (Name and Type) and the domains attached to them individually, visually confirm, and type yes | y | ok if you'd like to proceed.
7. The program will roll out the Tenant, Sites, Domains, and a support account within the tenant database
