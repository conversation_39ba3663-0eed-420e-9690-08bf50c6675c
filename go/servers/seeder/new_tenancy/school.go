package new_tenancy

// School is the structure of the provided data in google sheets etc.
// Modify as needed and use csv tag to map fields
type School struct {
	Type            string `csv:"Type"`
	Name            string `csv:"Name"`
	LegacyURL       string `csv:"Current URL"`
	NewDomain       string `csv:"New Website"`
	TemporaryDomain string `csv:"Temporary URL"`
	SchoolCode      string `csv:"School Code" json:"schoolCode"`
	Settings        struct {
		MainContact     string `csv:"Main Contact" json:"mainContact"`
		SchoolMotto     string `csv:"School Motto" json:"schoolMotto"`
		Address         string `csv:"address" json:"address"`
		City            string `csv:"city" json:"city"`
		Province        string `csv:"province" json:"province"`
		Phone           string `csv:"phone" json:"phone"`
		Fax             string `csv:"fax" json:"fax"`
		PostalCode      string `csv:"postal" json:"postalCode"`
		FacebookURL     string `csv:"Facebook URL" json:"facebookURL"`
		TwitterURL      string `csv:"twitterURL" json:"twitterURL"`
		InstagramURL    string `csv:"instagramURL" json:"instagramURL"`
		YoutubeURL      string `csv:"youtubeURL" json:"youtubeURL"`
		PrimaryBrand    string `csv:"PrimaryBrand" json:"PrimaryBrand"`
		SecondaryBrand  string `csv:"SecondaryBrand" json:"SecondaryBrand"`
		PrimaryAccent   string `csv:"PrimaryAccent" json:"PrimaryAccent"`
		SecondaryAccent string `csv:"SecondaryAccent" json:"SecondaryAccent"`
		Longitude       string `csv:"Longitude" json:"Longitude"`
		Latitude        string `csv:"Lattitude" json:"Lattitude"`
	}
}

func (s School) IsEmpty() bool {
	return len(s.Type) == 0 && len(s.Name) == 0 && len(s.LegacyURL) == 0
}
