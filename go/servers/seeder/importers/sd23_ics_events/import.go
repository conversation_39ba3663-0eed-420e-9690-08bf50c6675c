package sd23_ics_events

import (
	"contentmanager/servers/seeder/fs"
	"contentmanager/servers/seeder/ics"
	uuid "github.com/satori/go.uuid"
	"os"
	"strings"
)

const (
	directory             = "/home/<USER>/Documents/SD23/migration/downloads"
	DbName                = "cm_sd23"
	icsParentTemplatePath = "06669da763e8491eba2d10300209490b.8ec9229da7a748a9b5faaeb2c2faf292"
)

func Import() {
	ics.Translate(DbName, getFiles(), icsParentTemplatePath)
}

func getFiles() map[uuid.UUID][]ics.IcsModel {
	var SiteFileMap = map[uuid.UUID][]ics.IcsModel{}
	files := fs.ListFiles(directory)
	for _, file := range files {
		//calendar-www.rve.sd23.bc.ca-Home_-_Calendar_(SharePoint_-_Web).ics
		withoutPrefix := file[len("calendar-"):]
		withoutPrefix = strings.Replace(withoutPrefix, "www.", "", 1)
		withoutSuffix := withoutPrefix[:strings.Index(withoutPrefix, "-")]
		sid := schoolsMap[withoutSuffix]
		siteId, err := uuid.FromString(sid)
		if err != nil {
			continue
		}
		openedFile, err := os.Open(directory + "/" + file)
		if err != nil {
			panic(err)
		}
		icsData, err := ics.ParseIcs(openedFile)
		if err != nil {
			panic(err)
		}
		SiteFileMap[siteId] = icsData
	}
	return SiteFileMap
}

var schoolsMap = map[string]string{
	"eschool23.sd23.bc.ca":     "a159021a-4c10-4181-895f-eac25f863512",
	"ske.sd23.bc.ca":           "3cf0d460-0c0c-4187-9c95-f93ccdbe7874",
	"gre.sd23.bc.ca":           "d35c87c7-2929-430a-9240-18969c0ce5e4",
	"sd23.bc.ca":               "94d48ecb-a16d-470a-81bb-a4c859d5e1e2",
	"sms.sd23.bc.ca":           "ec0afdbb-8ec0-4a7c-abd3-e7af4712a826",
	"sle.sd23.bc.ca":           "d239e99f-c6e5-4398-a97e-eb4d93da560b",
	"sre.sd23.bc.ca":           "9d60a196-22de-4926-b607-dace17b62a26",
	"ame.sd23.bc.ca":           "018968b8-640a-43df-8c17-08e2c6dad190",
	"klo.sd23.bc.ca":           "2976e6fd-e83e-4070-aceb-7f51305f055d",
	"cle.sd23.bc.ca":           "8cd21055-7267-4af6-8d29-dc05a35606ac",
	"cnb.sd23.bc.ca":           "453f9926-5bdb-419c-bdbc-5ab6f735cb89",
	"bme.sd23.bc.ca":           "b8ac1a73-22e3-4ded-89b6-f978f9c4a6f2",
	"okm.sd23.bc.ca":           "f4f8467e-af1d-4cb8-bf35-5aa8d40d93db",
	"wat.sd23.bc.ca":           "46e13c57-880f-4add-8c15-3959100e2467",
	"kss.sd23.bc.ca":           "d315bd42-d39e-452e-b2e3-ae95b25fbc73",
	"sve.sd23.bc.ca":           "066342bd-2968-42d3-97f8-ce9fe46bcbd0",
	"dwe.sd23.bc.ca":           "b304f5ae-7aa2-4ba6-a94d-89f86e735a60",
	"dre.sd23.bc.ca":           "fe600d4c-191d-4dca-969c-15733bcf95a2",
	"nge.sd23.bc.ca":           "f03958b3-8798-4b13-8ce6-473493504bb3",
	"rms.sd23.bc.ca":           "c13188ff-4298-47b4-a975-e530706e7886",
	"cms.sd23.bc.ca":           "cb88cbbb-79df-435a-821d-417d66f633bb",
	"cps.sd23.bc.ca":           "fb3b2b9a-cf2c-4d3e-97e3-9decf9f36c7f",
	"bce.sd23.bc.ca":           "9e4d6211-4bce-4bb8-8523-424c359fe4fa",
	"gms.sd23.bc.ca":           "ad56440b-217f-419b-8532-173716779b98",
	"mbs.sd23.bc.ca":           "c9e81064-e39d-4d1d-86ee-d40a192fc76c",
	"ray.sd23.bc.ca":           "5bf8134e-bd94-4366-ad72-ebe8c2fb5c77",
	"gme.sd23.bc.ca":           "71037b0f-4c91-42b7-a0b4-8925c812f369",
	"ges.sd23.bc.ca":           "1f583b66-c53a-48e6-a69d-26cac2f1a51a",
	"rve.sd23.bc.ca":           "eb44bde7-1b85-4164-8226-1c00bc438fdd",
	"rss.sd23.bc.ca":           "fa805acb-509f-4fa0-bdbb-ba870d261b64",
	"asm.sd23.bc.ca":           "e6093305-6005-4354-9fd2-7bfed96b8c9c",
	"cte.sd23.bc.ca":           "99694fa2-e7b0-421e-a62a-f1bd13303ac9",
	"drk.sd23.bc.ca":           "6c8dafcb-3396-41fc-876e-e1d9892504fe",
	"qge.sd23.bc.ca":           "3e8c4bbd-fdab-48a0-9b44-1df9838bf612",
	"ele.sd23.bc.ca":           "e7aaeb7d-a8d1-4e64-b9a4-dafec36f7c82",
	"rle.sd23.bc.ca":           "49f14cf4-a536-4b2b-b8ae-8054e84280d5",
	"cas.sd23.bc.ca":           "6725e87f-0eb1-4c9a-9cc6-e4568b6b5e5c",
	"wre.sd23.bc.ca":           "fc537e2b-6aa4-40e4-8ac6-86d948cbec8a",
	"bge.sd23.bc.ca":           "6f880181-4e4a-4409-b58c-6f9855e1251d",
	"hms.sd23.bc.ca":           "fc5a9fe8-2505-4bec-8c31-b16daaf975ee",
	"ots.sd23.bc.ca":           "76b399bc-b384-4f77-8a6b-d52a1882db21",
	"hre.sd23.bc.ca":           "134e6deb-1bc4-4413-9b31-271d989c8124",
	"hge.sd23.bc.ca":           "1a6fb431-e0c0-437a-ac19-972489cc0380",
	"mje.sd23.bc.ca":           "43f582b0-6c55-4bc2-9163-abebbe96aca5",
	"ple.sd23.bc.ca":           "0fd75469-e685-4909-8696-f5d9eaa08194",
	"pge.sd23.bc.ca":           "b955c016-c52b-4369-a760-0d71ac6aa685",
	"pse.sd23.bc.ca":           "e7c7f6e2-85e9-4627-ab87-2cd262643260",
	"bhe.sd23.bc.ca":           "e382fc8c-6f21-4742-9568-32c7f816fc18",
	"welcomecentre.sd23.bc.ca": "674fb712-719c-47ed-9cb0-c7fa0e3b8d70",
	"international.sd23.bc.ca": "f6c8a2c2-2d20-45cb-8082-491658228ff4",
}
