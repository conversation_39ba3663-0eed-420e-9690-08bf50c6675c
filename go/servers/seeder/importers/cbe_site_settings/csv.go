package cbe_site_settings

import (
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/library/utils"
	"contentmanager/servers/seeder/csv"
	"contentmanager/servers/seeder/db"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type colors struct {
	PrimaryColor1    string `json:"primaryColor1"`
	PrimaryColor2    string `json:"primaryColor2"`
	TopNavColor      string `json:"topNavColor"`
	HighlightColor   string `json:"highlightColor"`
	TitleColors      string `json:"titleColors"`
	SubtitleColors   string `json:"subtitleColors"`
	LinkColours      string `json:"linkColours"`
	LinkHoverColours string `json:"linkHoverColours"`
	TopNavHoverColor string `json:"topNavHoverColor"`
	Button1          string `json:"button1"`
	Button2          string `json:"button2"`
	PrimaryColor3    string `json:"primaryColor3"`
	PrimaryColor4    string `json:"primaryColor4"`
	HighlightColor2  string `json:"highlightColor2"`
	FooterButton     string `json:"footerButton"`
	LinkColor2       string `json:"linkColor2"`
	LinkHoverColor2  string `json:"linkHoverColor2"`
	LinkHoverColor3  string `json:"linkHoverColor3"`
	QuickLinkHover   string `json:"quickLinkHover"`
}

func Load() map[string]map[string]string {
	records := cm_csv.ReadCSV("/home/<USER>/Documents/migrations/CBE Migration/cbe-colors.csv")
	var m = map[string]map[string]string{}
	for _, record := range records[1:] { // Skipping the header row
		schoolCode := record[0]
		var mp = map[string]string{}
		var s colors = colors{
			PrimaryColor1:    record[1],
			PrimaryColor2:    record[2],
			TopNavColor:      record[3],
			HighlightColor:   record[4],
			TitleColors:      record[5],
			SubtitleColors:   record[6],
			LinkColours:      record[7],
			LinkHoverColours: record[8],
			TopNavHoverColor: record[9],
			Button1:          record[10],
			Button2:          record[11],
			PrimaryColor3:    record[12],
			PrimaryColor4:    record[13],
			HighlightColor2:  record[14],
			FooterButton:     record[15],
			LinkColor2:       record[16],
			LinkHoverColor2:  record[17],
			LinkHoverColor3:  record[18],
			QuickLinkHover:   record[19],
		}
		b, err := json.Marshal(&s)
		if err != nil {
			panic(err)
		}
		if err := json.Unmarshal(b, &mp); err != nil {
			panic(err)
		}
		m[schoolCode] = mp
	}
	return m
}

func Update() {
	ProdTenantID := uuid.FromStringOrNil("")

	tenancyDB := db.GetConnection(db.LocalExternalIP, db.TenancyDBName)
	tenancyDBInstance, _ := tenancyDB.DB()
	defer tenancyDBInstance.Close()

	var sites = []tenancyModels.Site{}

	if err := tenancyDB.
		Where("active = true").
		Where("tenant_id = ?", ProdTenantID).
		Where("(settings->>'schoolCode') is not null").
		Find(&sites).
		Error; err != nil {
		panic(err)
	}
	var siteColorMap = Load()

	var updateSites = []tenancyModels.Site{}

	for _, site := range sites {
		settings := site.GetAllSettings()
		code, ok := settings["schoolCode"].(string)
		if !ok {
			continue
		}
		data, ok := siteColorMap[code]
		if !ok {
			continue
		}
		var m = utils.BytesToMapStringInterface(site.Settings)
		for key, value := range data {
			m[key] = value
		}
		siteSettings, err := json.Marshal(&m)
		if err != nil {
			panic(err)
		}
		site.Settings = siteSettings
		updateSites = append(updateSites, site)
	}
	if err := tenancyDB.Transaction(func(tx *gorm.DB) error {
		for _, site := range updateSites {
			if err := tenancyDB.Model(&site).Update("settings", site.Settings).Error; err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		panic(err)
	}

}
