package cbe_settings

//import (
//	"contentmanager/library/tenant/common/models"
//	"contentmanager/library/utils"
//	settings2 "contentmanager/pkgs/settings"
//	"contentmanager/servers/seeder/db"
//	"fmt"
//	uuid "github.com/satori/go.uuid"
//	"strings"
//	"testing"
//	"time"
//)
//
//func Test_ImportCBESettings(t *testing.T) {
//	t.Skip() /// DO NOT RUN THIS TEST
//	panic("DO NOT RUN THIS TEST")
//
//	data, err := ReadCSVs("C:\\temp\\CBE-CSV-Import")
//	if err != nil {
//		panic(err)
//	}
//	log.Println(len(data))
//
//	userID := uuid.FromStringOrNil("45f06f48-a93c-414e-b9a0-7582e0abc085")
//	now := time.Now().UTC()
//
//	db := db.GetConnection(db.Production, "cm_cbe")
//	var settings []settings2.Settings
//	for k, v := range data {
//		name := strings.ReplaceAll(k, ".csv", "")
//		settings = append(settings, settings2.Settings{
//			Tracking: commonModels.Tracking{
//				CreatedAt: now,
//				CreatedBy: userID,
//				UpdatedAt: now,
//				UpdatedBy: userID,
//			},
//			Name:        name,
//			Description: name,
//			Type:        "site-settings-custom",
//			SiteID:      nil,
//			Data:        utils.InterfaceToJsonb(v),
//			Public:      true,
//			Active:      true,
//		})
//	}
//	if err := db.Create(&settings).Error; err != nil {
//		panic(err)
//	}
//	log.Println("done")
//}
