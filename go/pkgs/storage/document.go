package storage

import (
	"contentmanager/etc/conf"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/config"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
)

func GetDocument(r *shared.AppContext, id uuid.UUID) (*File, error) {
	doc, err := getDocumentByNameOrID(r, id.String())
	if err != nil {
		return nil, err
	}

	if doc.Type != "document" {
		return nil, gorm.ErrRecordNotFound
	}

	if doc.PrivacyLevel > r.PublicAccount().PrivacyLevel {
		return nil, utils.PermissionRequiredErrorMsg
	}

	//config := cache.ICacheAdapter().GetObject(conf.TenancyConfigCacheKey).(config.AppConfig)
	appConfig := config.GetAppConfig()
	key := fmt.Sprintf("%s/documents/%s", r.TenantID(), doc.ID.String())
	attachment := &File{
		FileName: doc.Filename,
	}
	segments := strings.Split(strings.ToLower(doc.Filename), ".")
	var extension = segments[len(segments)-1]
	if mime, ok := conf.DocumentMimeTypes[extension]; ok {
		attachment.ContentType = mime
	} else {
		return nil, fmt.Errorf("invalid mime type or file extension")
	}

	rc, err := Download(conf.AwsS3Client, appConfig.AwsBucket, key)
	if err != nil {
		return nil, err
	}

	attachment.Reader = rc

	return attachment, nil
}

func getDocumentByNameOrID(r *shared.AppContext, filename string) (commonModels.Document, error) {
	dbQuery := r.TenantDatabase()

	id := uuid.FromStringOrNil(filename)
	if id == uuid.Nil {
		dbQuery = dbQuery.
			Where(" lower(filename) = lower(?) ", filename)
	} else {
		dbQuery = dbQuery.Where(" id = ? ", id)
	}

	var mm []commonModels.Document
	if err := dbQuery.
		Where(" active = true ").
		Where(" type = 'document' ").
		Find(&mm).
		Error; err != nil {
		return commonModels.Document{}, err
	}

	if len(mm) == 0 {
		return commonModels.Document{}, gorm.ErrRecordNotFound
	}
	if len(mm) == 1 {
		return mm[0], nil
	}

	for _, m := range mm {
		if slicexx.Contains(m.Sites, r.CurrentSiteID()) {
			return m, nil
		}
	}

	return mm[0], nil
}
