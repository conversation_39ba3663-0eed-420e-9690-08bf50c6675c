package storage

import (
	"bufio"
	"encoding/base64"
	"fmt"
	"io"
	"strings"
)

// Base64Decoder implements io.Reader for streaming base64 decoding
type Base64Decoder struct {
	reader  *bufio.Reader
	decoder io.Reader
	prefix  string
	closer  io.Closer
}

func NewBase64Decoder(reader io.ReadCloser) *Base64Decoder {
	return &Base64Decoder{
		closer: reader,
		reader: bufio.NewReader(reader),
	}
}

func (d *Base64Decoder) Close() error {
	if d.closer != nil {
		return d.closer.Close()
	}
	return nil
}

func (d *Base64Decoder) Read(p []byte) (int, error) {
	// Initialize decoder if not done yet
	if d.decoder == nil {
		// Read until we find the base64 data
		line, err := d.reader.ReadString(',')
		if err != nil {
			return 0, fmt.Errorf("[Base64Decoder] failed to read data URL prefix: %w", err)
		}

		// Store the prefix for debugging if needed
		d.prefix = line

		// Verify it's a data URL
		if !strings.HasPrefix(line, "data:") {
			return 0, fmt.<PERSON><PERSON><PERSON>("[Base64Decoder] content is not in data URL format")
		}

		// Create the decoder for the remaining content
		d.decoder = base64.NewDecoder(base64.StdEncoding, d.reader)
	}

	return d.decoder.Read(p)
}
