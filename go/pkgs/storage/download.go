package storage

import (
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"io"
)

func Download(s3Client *s3.S3, bucket, key string) (io.ReadCloser, error) {
	input := &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	}

	result, err := s3Client.GetObject(input)
	if err != nil {
		return nil, fmt.Errorf("[Download] failed to get object from S3: %w", err)
	}

	decoder := NewBase64Decoder(result.Body)
	return decoder, nil
}
