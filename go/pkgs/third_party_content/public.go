package third_party_content

import (
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetPublicThirdPartyContent(db *gorm.DB, siteId uuid.UUID, service string) (interface{}, error) {
	var tpc ThirdPartyContent
	db.
		Where(" site_id = ? ", siteId).
		Where(" service = ? ", service).
		First(&tpc)
	if db.Error != nil {
		return nil, db.Error
	}
	return bytesMustMarshal(tpc.Data), nil
}

// bytesMustMarshal will return b in whatever form that it will marshal to first.
// On failure to marshal in all cases, bytesMustMarshal will default to an array.
func bytesMustMarshal(b []byte) interface{} {
	var array []interface{}
	var object map[string]interface{}

	json.Unmarshal(b, &array)
	if array == nil {
		err := json.Unmarshal(b, &object)
		if err == nil {
			if tpc, ok := object["default"]; ok {
				if str, ok := tpc.(string); ok {
					return str
				}
			} else {
				return object
			}
		} else {
			return string(b)
		}
	}
	return array
}
