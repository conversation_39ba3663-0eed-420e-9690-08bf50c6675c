package third_party_content

import (
	"encoding/json"
	"github.com/satori/go.uuid"
	"time"
)

type (
	ThirdPartyContent struct {
		SiteID       uuid.UUID       `json:"siteId" gorm:"column:site_id;type:uuid;primary_key; NOT NULL;"`
		Service      string          `json:"service" gorm:"column:service;type:text;primary_key; NOT NULL" `
		Data         json.RawMessage `json:"data" gorm:"column:data;type:jsonb; default '{}', NOT NULL;"`
		LastModified time.Time       `json:"lastModified" gorm:"column:last_modified;type:timestamp with time zone;DEFAULT:now()"`
	}
)

func (tpc ThirdPartyContent) TableName() string {
	return "third_party_content"
}
