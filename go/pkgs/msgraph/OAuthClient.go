package msgraph

import (
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"
)

// OAuthClient represents a Azure OAuth API connection instance.
//

type OAuthClient struct {
	apiCall       sync.Mutex // lock it when performing an API-call to synchronize it
	TenantID      string     // See https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-group-create-service-principal-portal#get-tenant-id
	ApplicationID string     // See https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-group-create-service-principal-portal#get-application-id-and-authentication-key
	ClientSecret  string     // See https://docs.microsoft.com/en-us/azure/azure-resource-manager/resource-group-create-service-principal-portal#get-application-id-and-authentication-key
	Code          string
	RedirectLink  string
	Scope         string
	IsMultiTenant bool
	Token         Token
}

func (o *OAuthClient) String() string {
	var firstPart, lastPart string
	if len(o.ClientSecret) > 4 { // if ClientSecret is not initialized prevent a panic slice out of bounds
		firstPart = o.ClientSecret[0:3]
		lastPart = o.ClientSecret[len(o.ClientSecret)-3:]
	}
	return fmt.Sprintf("OAuthClient(ApplicationID: %v, ClientSecret: %v...%v, Token validity: [%v - %v])",
		o.ApplicationID, firstPart, lastPart, o.Token.NotBefore, o.Token.ExpiresOn)
}

func NewOAuthClient(tenantID, applicationID, clientSecret, code, redirectLink, scope string, isMultiTenant bool) (*OAuthClient, error) {
	o := OAuthClient{
		TenantID:      tenantID,
		ApplicationID: applicationID,
		ClientSecret:  clientSecret,
		Code:          code,
		RedirectLink:  redirectLink,
		Scope:         scope,
		IsMultiTenant: isMultiTenant,
		Token:         Token{},
	}
	o.apiCall.Lock()         // lock because we will get the Token
	defer o.apiCall.Unlock() // unlock after Token get
	return &o, o.getToken()
}

func (o *OAuthClient) GetToken() (Token, error) {
	if o.Token.IsValid() {
		return o.Token, nil
	}
	return Token{}, errors.New("Access Token Expired: " + o.Token.ExpiresOn.String())
}

// getToken gets the current Token from the OAuth Code.
func (o *OAuthClient) getToken() error {
	resource := map[bool]string{true: fmt.Sprintf("/%v/oauth2/Token", "common"), false: fmt.Sprintf("/%v/oauth2/Token", o.TenantID)}[o.IsMultiTenant]

	data := url.Values{}
	data.Add("grant_type", "authorization_code")
	data.Add("client_id", o.ApplicationID)
	data.Add("scope", o.Scope)
	data.Add("code", o.Code)
	data.Add("redirect_uri", o.RedirectLink)
	data.Add("client_secret", o.ClientSecret)

	u, err := url.ParseRequestURI(LoginBaseURL)
	if err != nil {
		return fmt.Errorf("Unable to parse URI: %v", err)
	}

	u.Path = resource
	req, err := http.NewRequest("POST", u.String(), bytes.NewBufferString(data.Encode()))

	if err != nil {
		return fmt.Errorf("HTTP Request Error: %v", err)
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Content-Length", strconv.Itoa(len(data.Encode())))

	var newToken Token
	err = o.performRequest(req, &newToken) // perform the prepared request
	if err != nil {
		return fmt.Errorf("Error on getting msgraph Token: %v", err)
	}
	o.Token = newToken

	return err
}

// makeGETAPICall performs an API-Call to the azure oauth API. This func uses sync.Mutex to synchronize all API-calls
func (o *OAuthClient) makeGETAPICall(apicall string, getParams url.Values, v interface{}) error {
	o.apiCall.Lock()
	defer o.apiCall.Unlock() // unlock when the func returns

	reqURL, err := url.ParseRequestURI(BaseURL)
	if err != nil {
		return fmt.Errorf("Unable to parse URI %v: %v", BaseURL, err)
	}

	// Add Version to API-Call, the leading slash is always added by the calling func
	reqURL.Path = "/" + APIVersion + apicall

	req, err := http.NewRequest("GET", reqURL.String(), nil)
	if err != nil {
		return fmt.Errorf("HTTP request error: %v", err)
	}

	req.Header.Add("Content-Type", "application/json")

	if getParams == nil { // initialize getParams if it's nil
		getParams = url.Values{}
	}

	req.URL.RawQuery = getParams.Encode() // set query parameters
	return o.performRequest(req, v)
}

// performRequest performs a pre-prepared http.Request and does the proper error-handling for it.
// does a json.Unmarshal into the v interface{} and returns the error of it if everything went well so far.
func (o *OAuthClient) performRequest(req *http.Request, v interface{}) error {
	httpClient := &http.Client{
		Timeout: time.Second * 10,
	}
	resp, err := httpClient.Do(req)
	if err != nil {
		return fmt.Errorf("HTTP response error: %v of http.Request: %v", err, req.URL)
	}
	defer resp.Body.Close() // close body when func returns

	body, err := ioutil.ReadAll(resp.Body) // read body first to append it to the error (if any)
	if resp.StatusCode < 200 || resp.StatusCode > 299 {
		// Hint: this will mostly be the case if the tenant ID can not be found, the Application ID can not be found or the clientSecret is incorrect.
		// The cause will be described in the body, hence we have to return the body too for proper error-analysis
		return fmt.Errorf("StatusCode is not OK: %v. Body: %v ", resp.StatusCode, string(body))
	}

	//fmt.Println("Body: ", string(body))

	if err != nil {
		return fmt.Errorf("HTTP response read error: %v of http.Request: %v", err, req.URL)
	}

	return json.Unmarshal(body, &v) // return the error of the json unmarshal
}
