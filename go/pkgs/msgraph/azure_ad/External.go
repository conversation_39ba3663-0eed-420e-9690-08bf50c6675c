package azure_ad

import (
	"contentmanager/pkgs/msgraph"
)

type (
	ExternalAccount struct {
		Email          string
		FirstName      string
		LastName       string
		ExternalGroups []string
	}
)

func GetAllGroupMemberships(client *msgraph.GraphClient, externalIds []string) (map[string]ExternalAccount, error) {
	matchingGroups, err := GetMatchingGroupMemberships(client, externalIds)
	if err != nil {
		return nil, err
	}
	// TODO: How to handle errors in refreshing group memberships
	//  Audit Records?
	matchingGroups = RefreshMembershipSync(matchingGroups)
	return ProcessGroupMembershipSync(matchingGroups), nil
}

// ProcessGroupMembershipSync
// Create a map of external account from the accumulated GroupMemberships and all of their members.
// values in `mapOfEmailToExternal` have the intersection between ExternalID's in CM, and their own Groups in Azure
func ProcessGroupMembershipSync(groups []GroupMembership) map[string]ExternalAccount {
	mapOfEmailToExternal := map[string]ExternalAccount{}
	for _, group := range groups {
		for _, member := range group.Members {
			email := member.Email()
			externalAccount, ok := mapOfEmailToExternal[email]
			if !ok {
				externalAccount = ExternalAccount{
					Email:     email,
					FirstName: member.GivenName,
					LastName:  member.Surname,
				}
			}
			externalAccount.ExternalGroups = append(externalAccount.ExternalGroups, group.ID)
			mapOfEmailToExternal[email] = externalAccount
		}

	}
	return mapOfEmailToExternal
}
