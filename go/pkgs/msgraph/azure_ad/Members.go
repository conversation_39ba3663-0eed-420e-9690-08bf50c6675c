package azure_ad

import (
	"contentmanager/pkgs/msgraph"
	"errors"
)

type (
	Member struct {
		GivenName         string `json:"givenName"`
		Surname           string `json:"surname"`
		Mail              string `json:"mail"`
		UserPrincipalName string `json:"userPrincipalName"`
	}
	MemberHandler struct {
		Members []Member `json:"value"`
		msgraph.Pager
	}
)

func (gm *MemberHandler) getAll() ([]Member, error) {
	var acc []Member
	for gm.CanPaginate() {
		if err := gm.Next(); err != nil {
			if !errors.Is(msgraph.ErrNoNextToken, err) {
				return nil, err
			}
			break
		}
		acc = append(acc, gm.Members...)
	}
	if len(acc) == 0 {
		return gm.Members, nil
	}
	return acc, nil
}

// Next
// Page Size & selected fields are set by func (*GraphClient) GetGroupMembershipHandler
func (gm *MemberHandler) Next() error {
	err := gm.Pager.NextPage(&gm)
	if err != nil {
		gm.Members = make([]Member, 0)
		return err
	}
	return nil
}
func (gm Member) Email() string {
	if len(gm.Mail) > 0 {
		return gm.Mail
	}
	return gm.UserPrincipalName
}
