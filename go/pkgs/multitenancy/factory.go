package multitenancy

import (
	"contentmanager/pkgs/config"
	"golang.org/x/net/context"
	"sync"
)

type (
	AccessorFactory interface {
		Accessor(ctx context.Context) Accessor
	}

	factory struct{}
)

var (
	accessor    Accessor
	initFactory sync.Once
)

func (f factory) Accessor(ctx context.Context) Accessor {
	return accessor.WithContext(ctx)
}

func AccessorFactoryCached(ctx context.Context, cfg config.AppConfig) AccessorFactory {
	if accessor != nil {
		panic("AccessorFactoryCached called more than once")
	}

	initFactory.Do(func() {
		initWithCache(ctx, cfg)
	})

	accessor = &accessorCached{&accessorCommon{ctx: ctx}}

	return &factory{}
}

func AccessorFactoryLive(ctx context.Context, cfg config.AppConfig) AccessorFactory {
	if accessor != nil {
		panic("AccessorFactoryLive called more than once")
	}

	initFactory.Do(func() {
		initNoCache(ctx, cfg)
	})

	accessor = &accessorLive{&accessorCommon{ctx: ctx}}

	return &factory{}
}

var _ AccessorFactory = &factory{}
