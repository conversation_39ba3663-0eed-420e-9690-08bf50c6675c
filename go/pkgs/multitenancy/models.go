package multitenancy

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"time"
)

type (
	Tenant struct {
		commonModels.Entity
		Name          string
		Description   string
		Address       string
		Postal        string
		City          string
		Province      string
		Country       string
		Created       time.Time
		Host          string
		Server        string
		DBUser        string          `gorm:"column:dbuser"`
		DBPassword    string          `gorm:"column:dbpassword"`
		Settings      json.RawMessage `gorm:"type:jsonb"`
		Active        bool
		AdminURL      string
		Distributions dbDriver.PgStringArray `gorm:"type:text[]"`
	}

	// // TODO: implement Site\.(?!(?:name|type|Settings|SiteSettings|Type|Name|settings)\b)
	Site struct {
		commonModels.Entity
		TenantID      uuid.UUID
		Name          string
		PrimaryDomain string
		Description   string
		Type          string
		Created       time.Time
		Tags          dbDriver.PgUUIDArray `gorm:"type:uuid[]"`
		Hosts         dbDriver.PgUUIDArray `gorm:"type:uuid[]"`
		Settings      json.RawMessage      `gorm:"type:jsonb"`
		Active        bool
	}

	Domain struct {
		TenantID uuid.UUID
		SiteID   uuid.UUID
		Domain   string
	}
)

func (Tenant) TableName() string {
	return "tenant"
}

func (Site) TableName() string {
	return "site"
}

func (Domain) TableName() string {
	return "domain"
}

func (s Site) IsDepartment() bool {
	return s.Type == "department"
}
