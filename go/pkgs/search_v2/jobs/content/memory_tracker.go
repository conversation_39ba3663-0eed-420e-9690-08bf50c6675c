package content

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"sync/atomic"
	"time"
)

// MemoryTracker tracks memory usage during a process
type MemoryTracker struct {
	ctx           context.Context
	cancel        context.CancelFunc
	maxMemoryUsed uint64 // in bytes
	interval      time.Duration
	wg            sync.WaitGroup
	logThreshold  float64      // percentage increase threshold for logging
	loggerFn      func(uint64) // function to call when new max is detected
	lastLogged    uint64       // last logged maximum
}

// NewMemoryTracker creates a new memory tracker that measures memory usage
// at the specified interval
// loggerFn is called when a new maximum memory usage is detected that exceeds the threshold
// threshold is the percentage increase required to trigger a log (0.05 = 5%)
func NewMemoryTracker(parentCtx context.Context, interval time.Duration, loggerFn func(uint64), threshold float64) *MemoryTracker {
	ctx, cancel := context.WithCancel(parentCtx)
	return &MemoryTracker{
		ctx:          ctx,
		cancel:       cancel,
		interval:     interval,
		loggerFn:     loggerFn,
		logThreshold: threshold,
	}
}

// Start begins tracking memory usage
func (mt *MemoryTracker) Start() {
	mt.wg.Add(1)
	go func() {
		defer mt.wg.Done()
		ticker := time.NewTicker(mt.interval)
		defer ticker.Stop()

		for {
			select {
			case <-mt.ctx.Done():
				return
			case <-ticker.C:
				currentUsage := mt.getCurrentMemoryUsage()
				mt.updateMaxMemory(currentUsage)
			}
		}
	}()
}

// Stop stops the memory tracking and returns the maximum memory used in bytes
func (mt *MemoryTracker) Stop() uint64 {
	// Take one final measurement before stopping
	currentUsage := mt.getCurrentMemoryUsage()
	mt.updateMaxMemory(currentUsage)

	mt.cancel()
	mt.wg.Wait()
	return atomic.LoadUint64(&mt.maxMemoryUsed)
}

// GetMaxMemoryUsed returns the current maximum memory used in bytes
func (mt *MemoryTracker) GetMaxMemoryUsed() uint64 {
	return atomic.LoadUint64(&mt.maxMemoryUsed)
}

// getCurrentMemoryUsage returns the current memory usage in bytes
func (mt *MemoryTracker) getCurrentMemoryUsage() uint64 {
	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)
	return memStats.Alloc
}

// updateMaxMemory updates the maximum memory used if the current usage is higher
// and logs if the increase exceeds the threshold
func (mt *MemoryTracker) updateMaxMemory(currentUsage uint64) {
	for {
		maxMem := atomic.LoadUint64(&mt.maxMemoryUsed)
		if currentUsage <= maxMem {
			break
		}
		if atomic.CompareAndSwapUint64(&mt.maxMemoryUsed, maxMem, currentUsage) {
			// Check if we should log this new maximum
			if mt.loggerFn != nil && mt.shouldLogNewMaximum(maxMem, currentUsage) {
				// Update last logged value
				atomic.StoreUint64(&mt.lastLogged, currentUsage)
				// Call the logger function with the new maximum
				mt.loggerFn(currentUsage)
			}
			break
		}
	}
}

// shouldLogNewMaximum determines if a new maximum should be logged based on the threshold
func (mt *MemoryTracker) shouldLogNewMaximum(oldMax, newMax uint64) bool {
	// If this is the first maximum (oldMax is 0), always log
	if oldMax == 0 {
		return true
	}

	// If we haven't logged anything yet, log the first maximum
	lastLogged := atomic.LoadUint64(&mt.lastLogged)
	if lastLogged == 0 {
		return true
	}

	// Calculate the percentage increase from the last logged value
	increase := float64(newMax-lastLogged) / float64(lastLogged)

	// Log if the increase exceeds the threshold
	return increase >= mt.logThreshold
}

// FormatBytes formats bytes into a human-readable string
func FormatBytes(bytes uint64) string {
	const (
		_          = iota
		KB float64 = 1 << (10 * iota)
		MB
		GB
	)

	var size string
	var value float64

	switch {
	case bytes >= uint64(GB):
		size = "%.2f GB"
		value = float64(bytes) / GB
	case bytes >= uint64(MB):
		size = "%.2f MB"
		value = float64(bytes) / MB
	case bytes >= uint64(KB):
		size = "%.2f KB"
		value = float64(bytes) / KB
	default:
		size = "%d bytes"
		value = float64(bytes)
	}

	return fmt.Sprintf(size, value)
}
