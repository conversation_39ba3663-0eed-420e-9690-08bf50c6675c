package documents

import (
	"contentmanager/library/utils"
	"contentmanager/pkgs/search_v2/index"
	"contentmanager/pkgs/service_context"
)

const MAX_CONCURRENCY = 1

// RunRetryDocumentsJob is a job that retries indexing of documents if Tika server was down during the initial indexing
func RunRetryDocumentsJob(serviceCtx service_context.ServiceContext) {
	if err := serviceCtx.Context().Err(); err != nil {
		serviceCtx.Logger().Warn().Err(err).Msg("Context cancelled")
		return
	}

	limiter := utils.NewConcurrencyLimiter(serviceCtx.Context(), MAX_CONCURRENCY)
	defer limiter.Stop()

	for _, tenant := range serviceCtx.Tenants() {

		indexingContext, err := index.ContextFromServiceContextForTenant(serviceCtx, tenant)
		if err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to create indexing context")
			continue
		}

		if err := utils.ExecuteWithArg(limiter, index.RetryTenantDocuments, *indexingContext); err != nil {
			serviceCtx.Logger().Error().Err(err).Str("tenant", tenant.Name).Msg("Failed to index tenant content")
		}
	}

	limiter.Wait()
}
