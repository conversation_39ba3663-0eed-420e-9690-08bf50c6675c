package search_v2

import (
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/sortx"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"time"
)

type (
	Query struct {
		pagx.Query
		sortx.SortingQuery
		Types []string `json:"Types[]"` // Types can be null, in that case, all types will be considered

		SearchText        string
		IgnoreCurrentSite bool
		Sites             []uuid.UUID `json:"Sites[]"` // Sites if provided, then CurrentSite and IgnoreCurrentSite will be ignored
		AuthorID          *uuid.UUID
		Tags              []uuid.UUID `json:"Tags[]"`
		AllTags           bool
		UpdatedAfter      *time.Time
		UpdatedBefore     *time.Time
		IncludeSites      bool
		PrivacyLevel      *int
	}

	Params struct {
		Query
		CurrentSite  uuid.UUID
		PrivacyLevel int
	}
)

var validTypes = map[string]struct{}{
	"document": {},
	"page":     {},
	"news":     {},
	"event":    {},
}

func (q Query) Validate() error {
	for _, t := range q.Types {
		if _, ok := validTypes[t]; !ok {
			return fmt.Errorf("invalid type: %s", t)
		}
	}

	return nil
}
