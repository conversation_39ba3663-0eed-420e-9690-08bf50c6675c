package search_v2

import (
	"contentmanager/tests"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"testing"
	"time"
)

var (
	site1 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000001")
	site2 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000002")
	site3 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000003")

	tag1 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000010")
	tag2 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000020")
	tag3 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000030")

	user1 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000100")
	user2 = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000200")

	now    = time.Now()
	past   = now.Add(-time.Hour)
	future = now.Add(time.Hour)

	title   = "Multi-Year Strategic Plan 2024-2028"
	content = "Peel District School Board’s (PDSB) Multi-Year Strategic Plan for 2024-2028 offers a visionary roadmap to prepare students for a dynamic future, equipping them with the skills, knowledge and attributes necessary to support well-being and to excel in their academic pathways and future careers. We are dedicated to creating equitable educational opportunities across the system that foster curiosity, creativity, innovation and inspiration.  \n\nDeveloped with extensive input from students, staff, families, and community members, the plan builds on the transformative years of the Ministry Directives. Grounded in equity, anti-oppression and human rights, it reflects the priorities and expectations of the Ministry of Education and is designed to better serve all students.\n\n"

	searchDataForProtected = []SearchData{
		{
			Common: Common{
				ExtID:        uuid.FromStringOrNil("00000000-0000-0000-0000-000000000001"),
				GeneratedAt:  now,
				Sites:        []uuid.UUID{site1, site2},
				Route:        "/has-secured",
				Title:        "The content that has both: secured and public content",
				Keywords:     "mixed, content",
				Type:         "page",
				PrivacyLevel: 0,
				Tags:         []uuid.UUID{tag1, tag2},
				PublishAt:    past,
				ExpireAt:     future,
				CreatedAt:    now,
				CreatedBy:    user1,
				UpdatedAt:    now,
				UpdatedBy:    user1,
			},
			Content: "content",
		},
		{
			Common: Common{
				ExtID:        uuid.FromStringOrNil("00000000-0000-0000-0000-000000000001"),
				GeneratedAt:  now,
				Sites:        []uuid.UUID{site1, site2},
				Route:        "/has-secured",
				Title:        "The content that has both: secured and public content",
				Keywords:     "mixed, content",
				Type:         "page",
				PrivacyLevel: 2,
				Tags:         []uuid.UUID{tag1, tag2},
				PublishAt:    past,
				ExpireAt:     future,
				CreatedAt:    now,
				CreatedBy:    user1,
				UpdatedAt:    now,
				UpdatedBy:    user1,
			},
			Content: "a secret content",
		},
	}
)

func Test_ValidTypes(t *testing.T) {
	q1 := Params{
		Query: Query{
			Types: nil,
		},
	}
	if q1.Validate() != nil {
		t.Error("Validation failed")
	}

	q2 := Params{
		Query: Query{
			Types: []string{"page", "event", "news", "document"},
		},
	}
	if q2.Validate() != nil {
		t.Error("Validation failed")
	}

	q3 := Params{
		Query: Query{
			Types: []string{"page", "event", "news", "document", "invalid-value"},
		},
	}
	if q3.Validate() == nil {
		t.Error("Validation failed")
	}
}

func Test_ProtectedContent(t *testing.T) {
	ctx := tests.InitLogging("Test_ProtectedContent")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	db = db.WithContext(ctx)
	if err := db.Save(&searchDataForProtected).Error; err != nil {
		t.Fatal("Failed to save search data")
	}

	// Case 1: both public and secured content are returned
	resPublic := getSearchResults(db, Params{
		Query: Query{
			SearchText:        "content",
			IgnoreCurrentSite: true,
		},
		CurrentSite:  site1,
		PrivacyLevel: 0,
	})

	resSecured := getSearchResults(db, Params{
		Query: Query{
			SearchText:        "content",
			IgnoreCurrentSite: true,
		},
		CurrentSite:  site1,
		PrivacyLevel: 2,
	})

	if !resPublic.Success || !resSecured.Success {
		t.Error("Search failed")
	}

	// see a comment in the search.go file (// For performance reasons ... )
	if len(resPublic.Data.Rows) != 1 /*  || len(resSecured.Data.Rows) != 1  || resPublic.Data.Rows[0].ID == resSecured.Data.Rows[0].ID */ {
		t.Error("Search failed")
	}

	// Case 2: only secured content is returned  (no "secret" in public content)
	resPublicSecret := getSearchResults(db, Params{
		Query: Query{
			SearchText:        "secret",
			IgnoreCurrentSite: true,
		},
		CurrentSite:  site1,
		PrivacyLevel: 0,
	})

	resSecuredSecret := getSearchResults(db, Params{
		Query: Query{
			SearchText:        "secret",
			IgnoreCurrentSite: true,
		},
		CurrentSite:  site1,
		PrivacyLevel: 2,
	})
	if !resPublicSecret.Success || !resSecuredSecret.Success {
		t.Error("Search failed")
	}
	if len(resPublicSecret.Data.Rows) != 0 || len(resSecuredSecret.Data.Rows) != 1 {
		t.Error("Search failed")
	}
}

func Test_Search(t *testing.T) {
	ctx := tests.InitLogging("Test_Search")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	db = db.WithContext(ctx)
	seedData(db)

	baseQuery := Params{
		Query: Query{
			Types:             nil,
			SearchText:        "keyword",
			IgnoreCurrentSite: false,
			Sites:             nil,
			AuthorID:          nil,
			Tags:              nil,
			AllTags:           false,
			UpdatedAfter:      nil,
			UpdatedBefore:     nil,
		},
		CurrentSite:  site1,
		PrivacyLevel: 0,
	}

	// Case 1: all types
	queryAllTypes := baseQuery
	resAllTypes := getSearchResults(db, queryAllTypes)
	if !resAllTypes.Success || resAllTypes.Data.TotalRecords != 3 {
		t.Error("Search `resAllTypes` failed")
	}

	// Case 2: no events/news/documents found
	queryNoEvents := baseQuery
	queryNoEvents.Types = []string{"event", "news", "document"}
	resNoEvents := getSearchResults(db, queryNoEvents)
	if !resNoEvents.Success || resNoEvents.Data.TotalRecords != 0 {
		t.Error("Search `resNoEvents` failed")
	}

	// Case 3: only site3
	querySite3 := baseQuery
	querySite3.CurrentSite = site3
	resSite3 := getSearchResults(db, querySite3)
	if !resSite3.Success || resSite3.Data.TotalRecords != 1 {
		t.Error("Search `resSite3` failed")
	}

	// Case 4: user2
	queryUser2 := baseQuery
	queryUser2.AuthorID = &user2
	resUser2 := getSearchResults(db, queryUser2)
	if !resUser2.Success || resUser2.Data.TotalRecords != 1 {
		t.Error("Search `resUser2` failed")
	}

	// Case 5: any tags
	queryAnyTags := baseQuery
	queryAnyTags.Tags = []uuid.UUID{tag1, tag2, tag3}
	resAnyTags := getSearchResults(db, queryAnyTags)
	if !resAnyTags.Success || resAnyTags.Data.TotalRecords != 3 {
		t.Error("Search `resAnyTags` failed")
	}

	// Case 6: all tags
	queryAllTags := baseQuery
	queryAllTags.Tags = []uuid.UUID{tag1, tag2, tag3}
	queryAllTags.AllTags = true
	resAllTags := getSearchResults(db, queryAllTags)
	if !resAllTags.Success || resAllTags.Data.TotalRecords != 1 {
		t.Error("Search `resAllTags` failed")
	}

	// Case 7: updated after
	queryUpdatedAfter := baseQuery
	queryUpdatedAfter.UpdatedAfter = &past
	resUpdatedAfter := getSearchResults(db, queryUpdatedAfter)
	if !resUpdatedAfter.Success || resUpdatedAfter.Data.TotalRecords != 3 {
		t.Error("Search `resUpdatedAfter` failed")
	}

	// Case 8: updated after future
	queryUpdatedAfterFuture := baseQuery
	queryUpdatedAfterFuture.UpdatedAfter = &future
	resUpdatedAfterFuture := getSearchResults(db, queryUpdatedAfterFuture)
	if !resUpdatedAfterFuture.Success || resUpdatedAfterFuture.Data.TotalRecords != 0 {
		t.Error("Search `resUpdatedAfterFuture` failed")
	}
}

func seedData(db *gorm.DB) {
	data := []SearchData{}
	base := SearchData{
		Common: Common{
			ExtID:        uuid.FromStringOrNil("00000000-0000-0000-0000-000000000001"),
			GeneratedAt:  now,
			Sites:        nil,
			Route:        "/",
			Title:        title,
			Keywords:     "keyword",
			Type:         "page",
			PrivacyLevel: 0,
			Tags:         nil,
			PublishAt:    past,
			ExpireAt:     future,
			CreatedAt:    now,
			CreatedBy:    user1,
			UpdatedAt:    now,
			UpdatedBy:    user1,
		},
		Content: content,
	}

	all := base
	all.ExtID = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000002")
	all.Sites = []uuid.UUID{site1, site2, site3}
	all.Tags = []uuid.UUID{tag1, tag2, tag3}

	oneOnly := base
	oneOnly.ExtID = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000003")
	oneOnly.Sites = []uuid.UUID{site1}
	oneOnly.Tags = []uuid.UUID{tag1}

	anotherUser := oneOnly
	anotherUser.ExtID = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000004")
	anotherUser.CreatedBy = user2
	anotherUser.UpdatedBy = user2

	expired := oneOnly
	expired.ExtID = uuid.FromStringOrNil("00000000-0000-0000-0000-000000000005")
	expired.ExpireAt = past

	data = append(data, all, oneOnly, anotherUser, expired)
	if err := db.Save(&data).Error; err != nil {
		panic(err)
	}
}
