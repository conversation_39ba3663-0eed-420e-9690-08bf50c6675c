package search_v2

import (
	"contentmanager/infrastructure/database/driver"
	commonModels "contentmanager/library/tenant/common/models"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
	"time"
)

type (
	Common struct {
		commonModels.Entity
		ExtID        uuid.UUID
		GeneratedAt  time.Time
		Sites        driver.PgUUIDArray `gorm:"type:uuid[]"`
		Route        string
		Title        string
		Settings     json.RawMessage `gorm:"column:settings;type:jsonb"`
		Meta         json.RawMessage `gorm:"column:meta;type:jsonb"`
		MediaID      *uuid.UUID
		Keywords     string
		Type         string
		PrivacyLevel int
		Tags         driver.PgUUIDArray `gorm:"type:uuid[]"`
		PublishAt    time.Time
		ExpireAt     time.Time
		CreatedAt    time.Time
		CreatedBy    uuid.UUID
		UpdatedAt    time.Time
		UpdatedBy    uuid.UUID
	}

	SearchData struct {
		Common
		Content      string
		ProcessorLog datatypes.JSONType[ProcessorResults]
	}

	SearchResult struct {
		Common
		Rank    float64
		Snippet string
	}

	ProcessorResults struct {
		Retries int
		Log     []ProcessorLog
	}

	ProcessorLog struct {
		Time  time.Time
		Error string
	}

	KeywordSearch struct {
		Keywords   driver.PgStringArray `gorm:"type:text[]"`
		ExactMatch bool
	}
)

func (SearchData) TableName() string {
	return "search_data"
}
