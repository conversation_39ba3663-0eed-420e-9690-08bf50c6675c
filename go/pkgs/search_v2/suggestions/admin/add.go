package admin

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/search_v2/suggestions"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

func AddSuggestionsAdmin(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	// Add match-suggestions endpoint
	r.Group("/api/v1/search/match-suggestions", func(router httpService.Router) {
		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery suggestions.Query
		}) {
			utils.WriteResultJSON(w, suggestions.MatchSuggestions(r, params.FromQuery))
		})
	}, bindauth.BindParamsMiddleware())

	// Add manage suggestions endpoint
	r.Group("/api/v1/suggestions", func(router httpService.Router) {
		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery SearchQuery
		}) {
			utils.WriteResultJSON(w, SearchSuggestions(r, params.FromQuery))
		})

		router.Get("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
		}) {
			utils.WriteResultJSON(w, GetSuggestion(r, params.FromPath.ID))
		})

		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody SuggestionDTO
		}) {
			utils.WriteResultJSON(w, CreateSuggestion(r, params.FromBody))
		})

		router.Put("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
			FromBody SuggestionDTO
		}) {
			utils.WriteResultJSON(w, UpdateSuggestion(r, params.FromPath.ID, params.FromBody))
		})

		router.Delete("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
		}) {
			utils.WriteResultJSON(w, DeleteSuggestion(r, params.FromPath.ID))
		})

		router.Patch("/:id", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				ID uuid.UUID `binding:"required"`
			}
		}) {
			utils.WriteResultJSON(w, RestoreSuggestion(r, params.FromPath.ID))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}
