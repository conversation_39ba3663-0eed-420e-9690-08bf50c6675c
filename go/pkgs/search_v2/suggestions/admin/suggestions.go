package admin

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/search_v2"
	"contentmanager/pkgs/search_v2/suggestions"
	uuid "github.com/satori/go.uuid"
)

type (
	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search   string
		Inactive bool
	}

	SuggestionDTO struct {
		search_v2.KeywordSearch
		content.PublishPeriod
		auth.TenantWideBase
		suggestions.Secured

		Name  string
		Items []string
	}
)

func SearchSuggestions(r *shared.AppContext, query SearchQuery) result.Result[pagx.Paginated[suggestions.SearchSuggestion]] {
	var res pagx.Paginated[suggestions.SearchSuggestion]
	tx := r.TenantDatabase().Where("active = ?", !query.Inactive)
	if len(query.Search) > 0 {
		tx = tx.Where(suggestions.SearchSuggestion{}.SearchQuery(), pgxx.ILike(query.Search))
	}

	tx = tx.Order(query.GetSortingSQL("name"))

	if err := pagination.Paginate(tx, query.Query, &res); err != nil {
		return result.Error(err, res)
	}
	return result.Success(res)
}

func GetSuggestion(r *shared.AppContext, id uuid.UUID) result.Result[suggestions.SearchSuggestion] {
	var suggestion suggestions.SearchSuggestion
	if err := r.TenantDatabase().Where("active AND id = ?", id).First(&suggestion).Error; err != nil {
		return result.Error(err, suggestion)
	}

	return result.Success(suggestion)
}

func CreateSuggestion(r *shared.AppContext, dto SuggestionDTO) result.Result[uuid.UUID] {
	suggestion := suggestions.SearchSuggestion{
		KeywordSearch: dto.KeywordSearch,
		PublishPeriod: dto.PublishPeriod,
		Name:          dto.Name,
		Items:         dto.Items,
		Active:        true,
	}

	suggestion.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Create(&suggestion).Error; err != nil {
		return result.Error(err, uuid.Nil)
	}

	return result.Success(suggestion.ID)
}

func UpdateSuggestion(r *shared.AppContext, id uuid.UUID, dto SuggestionDTO) result.EmptyResult {
	var suggestion suggestions.SearchSuggestion
	if err := r.TenantDatabase().Where("active AND id = ?", id).First(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	suggestion.KeywordSearch = dto.KeywordSearch
	suggestion.PublishPeriod = dto.PublishPeriod
	suggestion.Name = dto.Name
	suggestion.Items = dto.Items

	suggestion.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func DeleteSuggestion(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	var suggestion suggestions.SearchSuggestion
	if err := r.TenantDatabase().Where("id = ?", id).First(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	suggestion.Active = false
	suggestion.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func RestoreSuggestion(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	var suggestion suggestions.SearchSuggestion
	if err := r.TenantDatabase().Where("id = ?", id).First(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	suggestion.Active = true
	suggestion.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&suggestion).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

var _ auth.Secured = (*SuggestionDTO)(nil)
