package index

import (
	"contentmanager/library/utils/slicexx/jsonxx"
	"testing"
)

var def ContentIndexingConfig

func Test_UnmarshallConfig(t *testing.T) {
	cases := []struct {
		input []byte
		want  ContentIndexingConfig
		err   bool
	}{
		{nil, def, true},
		{[]byte(`{}`), def, true},
		{[]byte(`{"ContentIndexingConfig":{}}`), def, false},
		{
			[]byte(`{"ContentIndexingConfig":{"ExcludeFromIndex":true}}`),
			ContentIndexingConfig{true, false, false, false},
			false,
		},
	}

	for _, c := range cases {
		var want ContentIndexingConfig
		if err := jsonxx.UnmarshalJSONAtPath(c.input, "ContentIndexingConfig", &want); err != nil {
			if !c.err {
				t.<PERSON><PERSON><PERSON>("UnmarshalJSONAtPath(%s) returned error: %v", c.input, err)
			}
		} else if c.err {
			t.<PERSON><PERSON><PERSON>("UnmarshalJSONAtPath(%s) did not return error", c.input)
		} else {
			if want != c.want {
				t.<PERSON><PERSON><PERSON>("UnmarshalJSONAtPath(%s) = %v, want %v", c.input, want, c.want)
			}
		}
	}
}
