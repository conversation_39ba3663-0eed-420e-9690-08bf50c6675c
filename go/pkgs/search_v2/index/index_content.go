package index

import (
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	publicModels "contentmanager/library/tenant/public/models"
	publicServices "contentmanager/library/tenant/public/services"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/logging"
	"contentmanager/pkgs/htmlxx"
	"contentmanager/pkgs/search_v2"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

type (
	IndexingParams struct {
		CurrentSite  tenancyModels.Site
		RawPath      string
		PrivacyLevel int
	}

	ContentIndexingConfig struct {
		ExcludeFromIndex      bool // If true, the content will not be indexed.
		IndexFullPage         bool // If true, the entire page is indexed; by default, only the content's template is indexed.
		IndexForAllSites      bool // If true, index content for all sites if shared. Default is true for distributed pages and false for others.
		IndexAllPrivacyLevels bool // If true, index all privacy levels present in the content. Default is false.
	}
)

func IndexTenantContent(ctx IndexingContext) {
	startTenant := time.Now()
	logger := logging.FromContext(ctx.Context).With().Str("tenant", ctx.Tenant.Name).Logger()

	logger.Info().Msg("----- Started -----")

	// a delay to avoid concurrent queries
	if err := utils.RandomSleep(ctx.Context, 10, 1000); err != nil {
		logging.RootLogger().Error().Err(err).Msg("Context cancelled")
		return
	}

	if err := CleanupSearchDataForContent(ctx.TenantDB); err != nil {
		logger.Error().Err(err).Msg("Failed to cleanup search data")
		return
	}

	var contentIDs []uuid.UUID
	if err := ctx.TenantDB.Raw(`
		SELECT id
		FROM content
		WHERE type IN ('page', 'news', 'event')
		  AND active
		  AND publish_at IS NOT NULL
		  AND (expire_at IS NULL OR expire_at > NOW())
		  AND NOT settings @> '{"ContentIndexingConfig": {"ExcludeFromIndex": true}}'
		;
	`).Pluck("id", &contentIDs).Error; err != nil {
		logger.Error().Err(err).Msg("Failed to get content IDs")
		return
	}

	for _, contentID := range contentIDs {
		if err := ctx.Context.Err(); err != nil {
			logger.Warn().Err(err).Msg("Context cancelled")
			return
		}
		if err := CreateIndexByContentID(ctx, contentID); err != nil {
			logger.Error().Err(err).Str("ext_id", contentID.String()).Msg("Failed to index content")
		}
	}

	logger.Info().Str("duration", time.Since(startTenant).String()).Msg("----- Finished -----")
}

func UpdateIndexForContentID(r *shared.AppContext, contentID uuid.UUID) {
	ctx, err := ContextFromAppContext(r)
	if err != nil {
		r.Logger().Error().Err(err).Msg("Failed to create indexing context")
		return
	}
	reqID := r.ContextValue("req_id")
	go utils.WithPanicRecoveryArg(func(ctx IndexingContext) {
		start := time.Now()
		logger := logging.RootLogger().With().
			Str("tenant", ctx.Tenant.Name).
			Str("for_req_id", reqID).
			Str("ext_id", contentID.String()).Logger()

		logger.Info().Msg("Updating index")

		if err := CreateIndexByContentID(ctx, contentID); err != nil {
			logging.RootLogger().Error().Err(err).Msg("Failed to update index")
		}

		logger.Info().Str("duration", time.Since(start).String()).Msg("Index updated")
	})(*ctx)
}

func CreateIndexByContentID(ctx IndexingContext, contentID uuid.UUID) error {
	var content publicModels.ContentForHandlebars

	if err := ctx.TenantDB.
		Where(" id = ?", contentID).
		Preload("Media").
		//Preload("Tags").
		Preload("ContentStructure").
		First(&content).Error; err != nil {
		return err
	}

	if tags, err := publicModels.MapTagsForContent(ctx.TenantDB, content.TagIds); err == nil {
		content.Tags = tags
	}

	// Check if the content is excluded from the search index:
	// - if the content is deleted
	// - if the content is not published
	// - if the content is expired
	// - if the content is an external link
	// - if the content is excluded from the search index
	if content.Active == false ||
		content.PublishAt == nil ||
		strings.HasPrefix(content.Route, "http") ||
		(content.ExpireAt != nil && content.ExpireAt.Before(time.Now().UTC())) {
		return DeleteByContentID(ctx.TenantDB, contentID)
	}

	var config ContentIndexingConfig
	if err := jsonxx.UnmarshalJSONAtPath(content.Settings, "ContentIndexingConfig", &config); err == nil && config.ExcludeFromIndex {
		return DeleteByContentID(ctx.TenantDB, contentID)
	}
	//////

	var contentChain []publicModels.ContentForHandlebars
	var err error

	if config.IndexFullPage {
		contentChain, err = GetContentChain(ctx.TenantDB, content)
		if err != nil {
			return err
		}
	} else {
		if content.ContentStructure != nil {
			content.Content = content.ContentStructure.Template
		}
		contentChain = []publicModels.ContentForHandlebars{content}
	}

	var isDistrictPage bool
	_ = jsonxx.UnmarshalJSONAtPath(content.Settings, "isDistrictPage", &isDistrictPage)
	indexAllSites := config.IndexForAllSites || isDistrictPage

	css := getCurrentSites(ctx.Sites, content.Sites, indexAllSites)

	pll := getPrivacyLevels(content.PrivacyLevel, config.IndexAllPrivacyLevels)

	res := map[string]*search_v2.SearchData{}

	for _, pl := range pll {
		for _, site := range css {
			params := IndexingParams{
				CurrentSite:  site,
				RawPath:      content.Route,
				PrivacyLevel: pl,
			}
			appContext := appContextForIndexing(ctx, params)

			html, err := publicServices.CompileHandlebars(appContext, publicServices.CompileHandlebarsParams{ContentChain: contentChain, ContentOnly: !config.IndexFullPage})
			if err != nil {
				// ignore individual errors and continue with the next site
				logging.RootLogger().Error().Err(err).Str("Tenant", ctx.Tenant.Name).Str("ext_id", contentID.String()).Msg("Failed to build HTML for indexing")
				continue
			}

			txt, err := htmlxx.ExtractTextFromHTMLForIndexing(html)
			if err != nil {
				// ignore individual errors and continue with the next site
				logging.RootLogger().Error().Err(err).Str("Tenant", ctx.Tenant.Name).Str("ext_id", contentID.String()).Msg("Failed to extract text from HTML for indexing")
				continue
			}

			hash := getSHA256Hash(txt)
			if data, ok := res[hash]; ok {
				data.Sites = append(data.Sites, site.ID)
				// no need to update PrivacyLevel. The loop should always start from 0, and if the content is the same for 2,
				// then there is no need in separate entries for different PrivacyLevels
			} else {
				var madiaID *uuid.UUID
				if content.MediaID.Valid {
					madiaID = &content.MediaID.UUID
				}
				data = &search_v2.SearchData{
					Common: search_v2.Common{
						ExtID:        contentID,
						GeneratedAt:  time.Now().UTC(),
						Sites:        []uuid.UUID{site.ID},
						Route:        content.Route,
						Title:        content.Title,
						Settings:     content.Settings,
						Meta:         content.Meta,
						MediaID:      madiaID,
						Keywords:     getKeywords(content),
						Type:         string(content.Type),
						PrivacyLevel: pl,
						Tags:         getTagIDs(content),
						PublishAt:    *content.PublishAt,
						ExpireAt:     getExpireAt(content.ExpireAt),
						CreatedAt:    content.Created,
						CreatedBy:    content.Owner,
						UpdatedAt:    content.Updated,
						UpdatedBy:    content.Publisher,
					},
					Content: txt,
				}

				res[hash] = data

				if !indexAllSites {
					// exit from the sites loop if the content is not unique for all sites
					data.Sites = content.Sites
					break
				}
			}
		}
	}

	if len(res) == 0 {
		return DeleteByContentID(ctx.TenantDB, contentID)
	}

	searchData := make([]search_v2.SearchData, 0, len(res))
	for _, data := range res {
		searchData = append(searchData, *data)
	}

	if err := DeleteByContentID(ctx.TenantDB, contentID); err != nil {
		return err
	}
	if err := ctx.TenantDB.Create(&searchData).Error; err != nil {
		return err
	}

	//l.Info().Str("ext_id", contentID.String()).
	//	Int("Rows created", len(searchData)).
	//	TimeDiff("duration", time.Now(), start).
	//	Msg("Indexing completed")

	return nil
}

func DeleteByContentID(tenantDB *gorm.DB, contentID uuid.UUID) error {
	return tenantDB.Where("ext_id = ?", contentID).Delete(&search_v2.SearchData{}).Error
}

func CleanupSearchDataForContent(db *gorm.DB) error {
	return db.Exec(`
		DELETE FROM search_data
		WHERE ext_id IN (
			SELECT id
			FROM content
			WHERE type IN ('page', 'news', 'event')
			  AND (
				active = FALSE OR
				publish_at IS NULL OR
				(expire_at IS NOT NULL AND expire_at < NOW()) OR
				settings @> '{"ContentIndexingConfig": {"ExcludeFromIndex": true}}'
				)
		);
`).Error
}
