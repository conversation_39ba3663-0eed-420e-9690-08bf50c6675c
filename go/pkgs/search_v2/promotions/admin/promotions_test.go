package admin

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/auth"
	models "contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/search_v2"
	"contentmanager/pkgs/search_v2/promotions"
	"contentmanager/tests"
	"context"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
	"testing"
	"time"
)

var (
	now     = time.Now()
	inMonth = now.AddDate(0, 1, 0)
	since   = now.AddDate(0, -1, 0)

	keywords0 = []string{"3 year", "3 year education plan", "3-year", "3-year ed plan", "cbe 3 year plan", "cbe three-year education plan", "ed plan", "education plan", "three year ed", "three year education", "three year education plan", "three-year ed", "three-year ed plan", "three-year education", "three-year education plan"}

	keywords1 = []string{"early french imersion", "french immersion", "german", "german bilingual", "immersion", "late french immersion", "mandarin", "spanish", "spanish bilingual"}

	keywords2 = []string{"elibrary", "library"}

	keywords3 = []string{"email", "e-mail", "human resources phone number", "phone", "phone number", "phone numbers"}

	pp = []PromotionDTO{
		getPromotion(keywords0),
		getPromotion(keywords1),
		getPromotion(keywords2),
		getPromotion(keywords3),
	}
)

func Test_Promotions(t *testing.T) {
	ctx := context.Background() //tests.InitLogging("Test_Promotions")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	r := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
		TenantDB: db.WithContext(ctx),
		Identity: &models.Account{
			ID: uuid.FromStringOrNil("********-0000-0000-0000-************"),
		},
	})

	// Create promotions
	ids := []uuid.UUID{}
	for _, p := range pp {
		res := CreatePromotion(r, p)
		ids = append(ids, res.Data)
		if !res.Success {
			t.Fatalf("Error saving promotion: %v", res.Unwrap())
		}
	}

	// Check exact match
	res := promotions.MatchPromotions(r, promotions.Query{SearchText: "library", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching promotions: %v", res.Unwrap())
	}
	if len(res.Data) != 1 {
		t.Fatalf("Expected 1 promotion, got %d", len(res.Data))
	}

	// Check non-exact match (should not match)
	res = promotions.MatchPromotions(r, promotions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching promotions: %v", res.Unwrap())
	}
	if len(res.Data) != 0 {
		t.Fatalf("Expected 1 promotion, got %d", len(res.Data))
	}

	// Update promotion to be non-exact match
	libraryPromotion := pp[2]
	libraryPromotion.ExactMatch = false
	resUpdate := UpdatePromotion(r, ids[2], libraryPromotion)
	if !resUpdate.Success {
		t.Fatalf("Error updating promotion: %v", resUpdate.Unwrap())
	}

	// Check if promotion is updated
	updated := GetPromotion(r, ids[2])
	if !updated.Success {
		t.Fatalf("Error getting promotion: %v", updated.Unwrap())
	}
	if updated.Data.ExactMatch {
		t.Fatalf("Expected promotion to be non-exact match")
	}

	// Check non-exact match (should match)
	res = promotions.MatchPromotions(r, promotions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching promotions: %v", res.Unwrap())
	}
	if len(res.Data) != 1 {
		t.Fatalf("Expected 1 promotion, got %d", len(res.Data))
	}

	// Delete all promotions
	for _, id := range ids {
		resDelete := DeletePromotion(r, id)
		if !resDelete.Success {
			t.Fatalf("Error deleting promotion: %v", resDelete.Unwrap())
		}
	}

	// Check if all promotions are deleted. No matches should be found
	res = promotions.MatchPromotions(r, promotions.Query{SearchText: "libraries", AllSites: true})
	if !res.Success {
		t.Fatalf("Error matching promotions: %v", res.Unwrap())
	}
	if len(res.Data) != 0 {
		t.Fatalf("Expected 0 promotion, got %d", len(res.Data))
	}

}

func getPromotion(keywords []string) PromotionDTO {
	link := fmt.Sprintf("https://example.com/%s", strings.Join(keywords, "-"))
	keywordsStr := strings.Join(keywords, "; ")
	return PromotionDTO{
		KeywordSearch:  search_v2.KeywordSearch{Keywords: keywords, ExactMatch: true},
		PublishPeriod:  content.PublishPeriod{PublishAt: &now, ExpireAt: &inMonth},
		TenantWideBase: auth.TenantWideBase{},
		Secured:        promotions.Secured{},
		Name:           keywordsStr,
		Items:          []promotions.Item{{ExtID: nil, Type: "content", DisplayOrder: 1, Title: keywordsStr, Description: "Description", Link: link}},
	}
}
