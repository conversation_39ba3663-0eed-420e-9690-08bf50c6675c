package promotions

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/content"
	"contentmanager/pkgs/search_v2"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
)

type (
	SearchPromotion struct {
		commonModels.Entity
		search_v2.KeywordSearch
		content.PublishPeriod
		commonModels.Tracking
		auth.TenantWideBase
		Secured

		Name  string
		Items datatypes.JSONSlice[Item] `gorm:"type:jsonb"`

		Active bool
	}

	Item struct {
		ExtID        *uuid.UUID
		Type         string // needed to distinguish content/document tables (fragments?)
		DisplayOrder int    // allows global sorting of items (if there are multiple matching promotions)

		// Fragments?
		Title       string
		Description string
		Link        string
	}

	ItemView struct {
		Item
		PID uuid.UUID // Promotion ID
	}

	Secured struct{}
)

func (SearchPromotion) TableName() string {
	return "search_promotions"
}

func (Secured) GetScopeEntity() string {
	return "cm.full_text_search.search_promotions"
}

func (SearchPromotion) SearchQuery() string {
	return `(
				search_promotions.id || ' ' 
				|| search_promotions.name || ' ' 
				|| coalesce(array_to_string_immutable(search_promotions.keywords, ' '), ' ')
			) ILIKE ?`
}

var _ auth.Secured = (*SearchPromotion)(nil)
