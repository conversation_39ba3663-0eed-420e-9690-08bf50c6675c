package spell

import (
	"testing"
)

func Test_Sanitize(t *testing.T) {
	cases := []struct {
		name  string
		input string
		want  string
		err   error
	}{
		{
			name:  "simple english word",
			input: "input",
			want:  "input",
			err:   nil,
		},
		{
			name:  "complex punctuation",
			input: "As-df, -- () \\// as'df7!",
			want:  "as df as'df7",
			err:   nil,
		},
		{
			name:  "non-english text",
			input: "Enchantée !",
			want:  "",
			err:   ErrNonEnglishText,
		},
		{
			name:  "empty string",
			input: "",
			want:  "",
			err:   nil,
		},
		{
			name:  "only numbers and punctuation",
			input: "123 ,./",
			want:  "123",
			err:   nil,
		},
	}

	for _, tc := range cases {
		t.Run(tc.name, func(t *testing.T) {
			got, err := Sanitize(tc.input)

			// First check the error
			if (err != nil) != (tc.err != nil) {
				t.<PERSON>("Sanitize(%q) error = %v, want error %v", tc.input, err, tc.err)
				return
			}
			if err != nil && err.Error() != tc.err.Error() {
				t.Errorf("Sanitize(%q) error = %v, want error %v", tc.input, err, tc.err)
				return
			}

			// Then check the result
			if got != tc.want {
				t.Errorf("Sanitize(%q) = %q, want %q", tc.input, got, tc.want)
			}
		})
	}
}
