# Full-text search

# Search (table `search_data`)
The table name is `search_data`. The column `ext_id` refers to the `content.id` or `document.id` depending on the `type` of the content.

### Options for indexing

```golang

type ContentIndexingConfig struct {
    ExcludeFromIndex      bool // If true, the content will not be indexed.
    IndexFullPage         bool // If true, the entire page is indexed; by default, only the content's template is indexed.
    IndexForAllSites      bool // If true, index content for all sites if shared. Default is true for distributed pages and false for others.
    IndexAllPrivacyLevels bool // If true, index all privacy levels present in the content. Default is false.
}

```

### Exclude from search

1. In template use `data-noindex` attribute to exclude part of the content from search (`<nav data-noindex>...<.nav>`).
1. To exclude a page from search, add `ContentIndexingConfig: {ExcludeFromIndex: true}` to the `settings`.
1. `active = false`, **non-published**, **expired** content is excluded from search.

### Tika server

1. Tika server is used to extract text from documents. The functionality depends on environment variable `TIKA_SERVER_URL`.
1. Inside the k8s cluster, the Tika server is available at:
   - http://tika-server-service:9998/tika (from `aws-prod-130-contentmanager` namespace)
   - http://tika-server-service.default.svc.cluster.local:9998/tika (elsewhere within the same cluster)
1. For the local development run: `docker run -d -p 9998:9998 apache/tika:*******-full` and set `TIKA_SERVER_URL=http://localhost:9998/tika`.

## Suggestions and Promotions

Both have `KeywordSearch` as a part of the struct.
```golang
	type KeywordSearch struct {
		Keywords   driver.PgStringArray `gorm:"type:text[]"`
		ExactMatch bool
	}
 ```
- `Keywords` is an array of strings.
- `ExactMatch` is a boolean:
  - `true` (default) means that the search should be an exact match (case-insensitive). So each keyword will be
    matched against the user's search phrase to check if there is an exact *word* match: the keyword `ed` will be matched with `ed` 
    but not with `fred` or `education`.
  - `false` means that full-text search should be used (the same as in the search_data table). So the keywords
     will be interpreted as a full-text search query: `library` will be matched with both `library`, `libraries`.

### Suggestions (table `search_suggestions`)
Nothing special here. Suggestions are stored in the `items` column as a `type:text[]`.

### Promotions (table `search_promotions`)

`Items` is a JSONB column. All items have the `DisplayOrder` field, that is used to sort the items "globally" 
(across all matched promotions).  

The `type` field can be:
- `none` a standalone promotion (`Title` and `Link` are required)
- `page`, `news`, `event` a promotion of a specific type of content (`ExtID` is required). 
   The content Title and URL can be overridden by `Title` and `Link` fields. Also users can add a custom `Description`
- `document` a promotion of a document (`ExtID` is required). Can be overridden the same way as for the content type.
