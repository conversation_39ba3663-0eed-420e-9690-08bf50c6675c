package audit_logger

import (
	"github.com/satori/go.uuid"
	"gorm.io/datatypes"
	"time"
)

type AuditEvent string

var AuditEvents = []AuditEvent{CantSendIssueToSubscriber, SendIssueStarted, SendIssueStopped, SendIssueStoppedError}

const (
	CantSendIssueToSubscriber AuditEvent = "cant_send_issue_to_subscriber"
	SendIssueStarted          AuditEvent = "send_issue_started"
	SendIssueStopped          AuditEvent = "send_issue_stopped"
	SendIssueStoppedError     AuditEvent = "send_issue_stopped_error"
)

type AuditRecord struct {
	ID           uuid.UUID `gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	SubscriberID *uuid.UUID
	IssueID      *uuid.UUID
	TopicID      *uuid.UUID
	CreatedAt    time.Time
	Event        AuditEvent
	Message      string
	XData        datatypes.JSON
}

func (s AuditRecord) SearchQuery() string {
	return `(id || ' ' || event || ' ' || coalesce(message, ' ') || ' '
              || coalesce(topic_id::text, ' ') || ' '
              || coalesce(subscriber_id::text, ' ') || ' '
              || coalesce(issue_id::text, ' ')) ilike ( ? )`
}
