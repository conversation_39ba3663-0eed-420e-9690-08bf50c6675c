package handlers

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/user_management/accounts"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

func AddAccountsManagement(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v1/user_management/accounts", func(router httpService.Router) {

		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery accounts.SearchQuery
		}) {
			utils.WriteResultJSON(w, accounts.SearchAccounts(r, params.FromQuery))
		})

		router.Get("/:accountID/manual_groups", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.GetManualGroups(r, params.FromPath.AccountID))
		})

		router.Post("/groups-for-assignment", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody accounts.GroupsForAssignmentQuery
		}) {
			utils.WriteResultJSON(w, accounts.GroupsForAssignment(r, params.FromBody))
		})

		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromBody accounts.CreateAccountDTO
		}) {
			utils.WriteResultJSON(w, accounts.CreateAccount(r, params.FromBody))
		})

		router.Delete("/:accountID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.SetActive(r, params.FromPath.AccountID, false))
		})

		router.Patch("/:accountID/restore", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.SetActive(r, params.FromPath.AccountID, true))
		})

		router.Patch("/:accountID/manual_groups/:groupID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
				GroupID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.AddManualGroup(r, params.FromPath.AccountID, params.FromPath.GroupID))
		})

		router.Delete("/:accountID/manual_groups/:groupID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
				GroupID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.RemoveManualGroup(r, params.FromPath.AccountID, params.FromPath.GroupID))
		})

		router.Patch("/:accountID/native_groups/:groupID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
				GroupID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.AddNativeGroup(r, params.FromPath.AccountID, params.FromPath.GroupID))
		})

		router.Delete("/:accountID/native_groups/:groupID", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromPath struct {
				AccountID uuid.UUID `binding:"cm_uuid"`
				GroupID   uuid.UUID `binding:"cm_uuid"`
			}
		}) {
			utils.WriteResultJSON(w, accounts.RemoveNativeGroup(r, params.FromPath.AccountID, params.FromPath.GroupID))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware())

	return r
}
