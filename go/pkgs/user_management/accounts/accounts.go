package accounts

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth/identity"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/crypto/bcrypt"
	"time"
)

type (
	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search string
		Active *bool
	}

	CreateAccountDTO struct {
		FirstName       string
		LastName        string
		Email           string `validate:"email"`
		Password        string
		PasswordConfirm string
	}
)

// SearchAccounts restricts the available accounts to the ones that the current user has access to.
func SearchAccounts(r *shared.AppContext, q SearchQuery) result.Result[pagx.Paginated[identity.Account]] {
	var pag pagx.Paginated[identity.Account]
	tx := r.TenantDatabase()

	// restrict the available accounts to the ones that the current user has access to
	if !r.Account().IsAdmin {
		availableExternalGroups := r.Account().Audiences()
		// External Groups are Azure ID's
		if len(availableExternalGroups) > 0 {
			tx = tx.Where(tx.Where(pgxx.ArrayHasAny("external_groups" /*"external_id_list"*/, availableExternalGroups)).Or("id = ?", r.Account().ID))
		} else {
			tx = tx.Where("id = ?", r.Account().ID)
		}
	}

	if q.Active != nil {
		tx = tx.Where("active = ?", *q.Active)
	}

	if q.Search != "" {
		tx = tx.Where(identity.Account{}.SearchQuery(), pgxx.ILike(q.Search))
	}
	tx = tx.Order(q.GetSortingSQL("email"))

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}

func CreateAccount(r *shared.AppContext, dto CreateAccountDTO) result.EmptyResult {
	if dto.Password != dto.PasswordConfirm {
		return result.ErrorEmpty(errx.NewValidationError(map[string]string{"password": "Passwords do not match"}))
	}

	password := ""
	if dto.Password != "" {
		accountPasswordBytes, err := bcrypt.GenerateFromPassword([]byte(dto.Password), bcrypt.DefaultCost)
		if err != nil {
			return result.ErrorEmpty(err)
		}
		password = string(accountPasswordBytes)
	}

	newAccount := identity.Account{
		Firstname:      dto.FirstName,
		Lastname:       dto.LastName,
		Email:          dto.Email,
		Password:       password,
		Active:         true,
		IsAdmin:        false,
		ExternalGroups: []string{},
		ManualGroups:   []uuid.UUID{},
		NativeGroups:   []uuid.UUID{},
	}
	newAccount.Track(time.Now(), r.Account().ID)

	if err := CheckPermissions(r.Account(), newAccount, false); err != nil {
		return result.ErrorEmpty(err)
	}

	if err := r.TenantDatabase().Create(&newAccount).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func AddManualGroup(r *shared.AppContext, accountID uuid.UUID, groupID uuid.UUID) result.EmptyResult {
	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := CheckPermissions(r.Account(), account, false); err != nil {
		return result.ErrorEmpty(err)
	}

	var group identity.SecurityGroup
	if err := r.TenantDatabase().InnerJoins("Role", r.TenantDatabase().Where(&commonModels.Role{Active: true})).First(&group, "security_group.id = ?", groupID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if group.Type != identity.MANUAL {
		return result.ErrorEmpty(fmt.Errorf("Group %s [%s] is not manual. ", group.Name, group.ID.String()))
	}
	if err := identity.ValidateDelegateGroup(*r.Account(), group); err != nil {
		return result.ErrorEmpty(err)
	}

	if err := identity.ValidateGroupAssignmentToAccount(group, account); err != nil {
		return result.ErrorEmpty(err)
	}

	account.ManualGroups = append(account.ManualGroups, groupID)
	account.ManualGroups = slicexx.Distinct(account.ManualGroups)
	account.Track(time.Now(), r.Account().ID)
	if err := r.TenantDatabase().Save(&account).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func RemoveManualGroup(r *shared.AppContext, accountID uuid.UUID, groupID uuid.UUID) result.EmptyResult {
	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if err := CheckPermissions(r.Account(), account, false); err != nil {
		return result.ErrorEmpty(err)
	}

	var group identity.SecurityGroup
	if err := r.TenantDatabase().First(&group, "id = ?", groupID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	if group.Type != identity.MANUAL {
		return result.ErrorEmpty(fmt.Errorf("Group %s [%s] is not manual. ", group.Name, group.ID.String()))
	}

	account.ManualGroups = slicexx.Remove(account.ManualGroups, groupID)
	account.Track(time.Now(), r.Account().ID)
	if err := r.TenantDatabase().Save(&account).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

// AddNativeGroup add native group to account, no validation for the group. Only admins can add native groups.
func AddNativeGroup(r *shared.AppContext, accountID uuid.UUID, groupID uuid.UUID) result.EmptyResult {
	if !r.Account().IsAdmin {
		return result.ErrorEmpty(utils.UnauthorizedError{Message: "Only admin can edit account with native groups"})
	}

	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	account.NativeGroups = append(account.NativeGroups, groupID)
	account.NativeGroups = slicexx.Distinct(account.NativeGroups)
	account.Track(time.Now(), r.Account().ID)
	if err := r.TenantDatabase().Save(&account).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func RemoveNativeGroup(r *shared.AppContext, accountID uuid.UUID, groupID uuid.UUID) result.EmptyResult {
	if !r.Account().IsAdmin {
		return result.ErrorEmpty(utils.UnauthorizedError{Message: "Only admin can edit account with native groups"})
	}

	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	account.NativeGroups = slicexx.Remove(account.NativeGroups, groupID)
	account.Track(time.Now(), r.Account().ID)
	if err := r.TenantDatabase().Save(&account).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func SetActive(r *shared.AppContext, accountID uuid.UUID, active bool) result.EmptyResult {
	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	if err := CheckPermissions(r.Account(), account, false); err != nil {
		return result.ErrorEmpty(err)
	}

	account.Track(time.Now(), r.Account().ID)
	account.Active = active
	if err := r.TenantDatabase().Save(&account).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	return result.SuccessEmpty()
}

// GetManualGroups returns all the manual groups that the current user can assign to the given account (no paging).
func GetManualGroups(r *shared.AppContext, accountID uuid.UUID) result.Result[[]identity.SecurityGroup] {
	var account identity.Account
	if err := r.TenantDatabase().First(&account, "id = ?", accountID).Error; err != nil {
		return result.Error(err, []identity.SecurityGroup{})
	}
	if err := CheckPermissions(r.Account(), account, false); err != nil {
		return result.Error(err, []identity.SecurityGroup{})
	}

	var assignableGroups []identity.SecurityGroup
	if err := r.TenantDatabase().Joins("Role").Where(pgxx.ArrayHasAny("depends_on", account.ExternalGroups)).Find(&assignableGroups).Error; err != nil {
		return result.Error(err, []identity.SecurityGroup{})
	}

	var validGroups []identity.SecurityGroup
	for _, group := range assignableGroups {
		if err := identity.ValidateGroupAssignmentToAccount(group, account); err != nil {
			continue
		}
		if err := identity.ValidateDelegateGroup(*r.Account(), group); err != nil {
			continue
		}
		assignableGroups = append(validGroups, group)
	}

	return result.Success(validGroups)
}

func CheckPermissions(editor *identity.Account, account identity.Account, allowSelf bool) error {
	if editor.IsAdmin {
		return nil
	}

	if len(account.NativeGroups) > 0 {
		return utils.UnauthorizedError{Message: "Only admin can edit account with native groups"}
	}

	if allowSelf && editor.ID == account.ID {
		return nil
	}

	availableExternalGroups := editor.Audiences()
	if slicexx.HasCommonElement(availableExternalGroups, account.ExternalGroups) {
		return nil
	}

	return utils.UnauthorizedError{Message: fmt.Sprintf("You can't edit %s account. Your account ID: %s and you have access to edit groups: %v. But account has: %v. ",
		account.ID.String(),
		editor.ID.String(),
		availableExternalGroups,
		account.ExternalGroups)}
}
