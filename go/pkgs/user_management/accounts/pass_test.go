package accounts

import (
	"github.com/stretchr/testify/assert"
	"golang.org/x/crypto/bcrypt"
	"testing"
)

func Test_Pass(t *testing.T) {
	password := []byte("12qwaszx") //$2a$10$jKxMk9Yvuo81lybRjDNo0OcGY/O6nahrn1bDhOzOyPghpdi9QzLDG
	accountPasswordBytes, _ := bcrypt.GenerateFromPassword(password, bcrypt.DefaultCost)
	hash := string(accountPasswordBytes)

	t.Log("hash:", hash)
	assert.Nil(t, bcrypt.CompareHashAndPassword([]byte(hash), password))
}
