package handlers

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/user_management/pat"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"github.com/satori/go.uuid"
)

func GetTokens(r *shared.AppContext, accountID uuid.UUID) result.Result[[]pat.PAToken] {
	now := r.AppTime().NowUTC()
	var tokens []pat.PAToken
	if err := r.TenantDatabase().Where("account_id = ? AND active AND expires_at > ?", accountID, now).Find(&tokens).Error; err != nil {
		return result.Error(err, tokens)
	}
	return result.Success(tokens)
}

func CreateToken(r *shared.AppContext, tokenDTO pat.PATokenDTO) result.Result[pat.PAToken] {
	now := r.AppTime().NowUTC()
	tenantID := r.TenantID()
	if tenantID == uuid.Nil {
		return result.Error(errors.New("Tenant ID is nil. "), pat.PAToken{})
	}

	if tokenDTO.ExpiresAt.Before(now) {
		return result.Error(errors.New("ExpiresAt is before now. "), pat.PAToken{})
	}

	if len(tokenDTO.Name) == 0 {
		return result.Error(errors.New("Name is empty. "), pat.PAToken{})
	}

	token, err := GenerateToken("cm_"+tenantID.String()+"_", 33)
	if err != nil {
		return result.Error(err, pat.PAToken{})
	}
	paToken := pat.PAToken{
		AccountID: tokenDTO.AccountID,
		Name:      tokenDTO.Name,
		Token:     token,
		ExpiresAt: tokenDTO.ExpiresAt,
		Active:    true,
	}
	paToken.Track(now, r.Account().ID)
	if err := r.TenantDatabase().Create(&paToken).Error; err != nil {
		return result.Error(err, paToken)
	}
	return result.Success(paToken)
}

func DeleteToken(r *shared.AppContext, id uuid.UUID) result.EmptyResult {
	now := r.AppTime().NowUTC()
	var token pat.PAToken
	if err := r.TenantDatabase().First(&token, "id = ?", id).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	token.Active = false
	token.Name = token.Name + "_deleted_" + token.ID.String()
	token.Track(now, r.Account().ID)

	return result.CheckEmpty(r.TenantDatabase().Save(&token).Error)
}

func GenerateToken(prefix string, length int) (string, error) {
	if length < 1 {
		return "", errors.New("Length should be greater than 0. ")
	}
	randomBytes := make([]byte, length)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", err
	}

	token := prefix + base64.URLEncoding.EncodeToString(randomBytes)
	return token, nil
}
