package pat

import (
	"contentmanager/library/shared/result"
	"contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

type (
	PAToken struct {
		ID uuid.UUID `gorm:"column:id;type:uuid; primary_key; not null; default:uuid_generate_v4();"`
		commonModels.Tracking
		AccountID uuid.UUID
		Name      string
		Token     string
		ExpiresAt time.Time
		Active    bool
	}

	PATokenDTO struct {
		AccountID uuid.UUID
		Name      string
		ExpiresAt time.Time
	}
)

func (t PAToken) TableName() string {
	return "pa_tokens"
}

func GetToken(db *gorm.DB, token string) result.Result[PAToken] {
	now := time.Now().UTC()
	var paToken PAToken
	if err := db.First(&paToken, "token = ? AND active AND expires_at > ?", token, now).Error; err != nil {
		return result.Error(err, paToken)
	}
	return result.Success(paToken)
}
