package sitemap

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	publicControllers "contentmanager/library/tenant/public/controllers"
	"fmt"
	"net/http"
)

func AddPublicRoute(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Get("/sitemap.txt", getSiteMapTXT)
	r.Get("/robots.txt", getRobotsTXT)
	return r
}

func getSiteMapTXT(w http.ResponseWriter, r *shared.AppContext) {
	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}
	publicControllers.WriteWithStandardHeaders(w, r, GetSiteMap(r.TenantDatabase(), r.Request().Host, r.CurrentSiteID()))
}

func getRobotsTXT(w http.ResponseWriter, r *shared.AppContext) {
	var sitemapEntry string
	var allowDisallowEntry string

	if publicControllers.IsStagingDomain(r.Request().RequestURI) {
		allowDisallowEntry = "Disallow: /"
	} else {
		allowDisallowEntry = "Allow: /"
		sitemapEntry = "Sitemap: https://" + r.Request().Host + "/sitemap.txt"
	}
	var response = fmt.Sprintf("User-agent: *\n%s\n%s", allowDisallowEntry, sitemapEntry)
	publicControllers.WriteWithStandardHeaders(w, r, []byte(response))
}
