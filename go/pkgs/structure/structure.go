package structure

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/permissions"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
)

type (
	Structure struct {
		ID uuid.UUID `gorm:"column:id;type:uuid; primary_key; not null; default:uuid_generate_v4();"`
		auth.TenantWideBase
		Active bool
		commonModels.Tracking
		Name          string
		Template      string
		FormStructure json.RawMessage
	}

	Component struct {
		Name      string `json:"name"`
		Type      string `json:"type"`
		Label     string `json:"label"`
		Required  bool   `json:"required,omitempty"`
		UniqScope string `json:"uniq_scope,omitempty"` // global | site
	}

	Section struct {
		Name          string      `json:"name"`
		Title         string      `json:"title"`
		AllowMultiple bool        `json:"allowMultiple,omitempty"`
		Components    []Component `json:"components"`
	}
)

func (s Structure) GetType() string {
	return permissions.Template
}

func (Structure) SelectNameQuery() string {
	return "name"
}

func (Structure) TableName() string {
	return "structure"
}

func (Structure) GetScopeEntity() string {
	return "cm.structure"
}

var _ commonModels.IContent = (*Structure)(nil)

func (s Structure) GetID() uuid.UUID {
	return s.ID
}

func (s Structure) Sections() ([]Section, error) {
	var sections []Section
	err := json.Unmarshal(s.FormStructure, &sections)

	return sections, err
}
