package handlers

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/structure"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

func ConvertFromDCT(r *shared.AppContext) result.Result[string] {
	var cc []commonModels.Content
	if err := r.TenantDatabase().Where("active and pg_column_size(structure) > 9").Find(&cc).Error; err != nil {
		return result.Error(err, "")
	}

	if len(cc) == 0 {
		return result.Success("No structures to migrate from DCT")
	}

	var migratedIDs []uuid.UUID
	if err := r.TenantDatabase().Model(structure.Structure{}).Select("id").Where("active").Pluck("id", &migratedIDs).Error; err != nil {
		return result.Error(err, "")
	}

	now := r.AppTime().NowUTC()
	ss := make([]structure.Structure, 0, len(cc))
	names := make(map[string]interface{})
	for _, c := range cc {
		if slicexx.Contains(migratedIDs, c.ID) {
			continue
		}

		fs := c.Structure
		var inf interface{}
		if err := json.Unmarshal(fs, &inf); err == nil {
			switch inf.(type) {
			case string:
				fs = []byte(inf.(string))
			}
		}

		uniqueName := "[DCT] " + c.Title + " (from DCT " + string(c.Type) + " " + c.PageLayout.String() + ")"
		if _, ok := names[strings.ToLower(uniqueName)]; ok {
			uniqueName = uniqueName + " " + c.ID.String()
		}

		names[strings.ToLower(uniqueName)] = struct{}{}

		s := structure.Structure{
			ID:            c.ID,
			Active:        c.Active,
			Name:          uniqueName,
			Template:      c.Content,
			FormStructure: fs,
		}
		s.Track(now, r.Account().ID)
		ss = append(ss, s)
	}

	// Migrate structures from DCT and associate them with the corresponding templates
	if len(ss) > 0 {
		if err := r.TenantDatabase().Create(&ss).Error; err != nil {
			return result.Error(err, "")
		}

		migratedStructures := slicexx.Select(ss, func(s structure.Structure) uuid.UUID {
			return s.ID
		})

		if err := r.TenantDatabase().Exec("update content set structures = ARRAY[id]::uuid[] where id in ?", migratedStructures).Error; err != nil {
			return result.Error(err, "")
		}
	}

	// Seed default structure and assotiate the rest of the templates with it (all NON-DCT templates considered as WYSIWYG)
	if err := UpsertDefaultStructure(r.TenantDatabase(), &r.Account().ID); err != nil {
		return result.Error(err, "")
	}

	if err := r.TenantDatabase().Exec("update content set structures = ARRAY[?]::uuid[] where type = 'template' and (structures is null OR cardinality(structures) = 0)", DefaultStructureID).Error; err != nil {
		return result.Error(err, "")
	}

	return result.Success(fmt.Sprintf("Migrated %v structures from DCT", len(ss)))
}

func MigratePagesToStructuredPages(r *shared.AppContext, structureID uuid.UUID) result.Result[[]uuid.UUID] {
	subPath := strings.Replace(structureID.String(), "-", "", -1)
	var pageIDs []uuid.UUID
	if err := r.TenantDatabase().Model(&commonModels.Content{}).
		Select("id").Where("type != 'distributed_page' and structure_id is null and id != ? and path ~ ?", structureID, "*."+subPath+".*").
		Pluck("id", &pageIDs).Error; err != nil {
		return result.Error[[]uuid.UUID](err, nil)
	}
	if len(pageIDs) == 0 {
		return result.Success[[]uuid.UUID]([]uuid.UUID{})
	}

	if err := r.TenantDatabase().Model(&commonModels.Content{}).Where("id IN ?", pageIDs).Update("structure_id", structureID).Error; err != nil {
		return result.Error[[]uuid.UUID](err, nil)
	}

	return result.Success(pageIDs)
}

func MigrateAllContent(r *shared.AppContext) result.Result[string] {
	var strIDs []uuid.UUID
	if err := r.TenantDatabase().Raw("select id from structure where exists(select 1 from content where content.id = structure.id)").Pluck("id", &strIDs).Error; err != nil {
		return result.Error(err, "")
	}
	total := 0
	// Migrate DCT pages to structured pages
	for _, strID := range strIDs {
		if res := MigratePagesToStructuredPages(r, strID); res.IsError() {
			return result.Error(res.Unwrap(), "")
		} else {
			total += len(res.Data)
		}
	}
	r.TenantDatabase().Delete(&commonModels.Content{}, uuid.Nil)
	// Migrate WYSIWYG pages to structured pages
	if res := r.TenantDatabase().Exec(
		`update content
				set structure_id = ?, data = jsonb_build_object(
					'mainContent', jsonb_build_object(
						'lexical', jsonb_build_object(
							'html', to_jsonb(content.content),
							'json', '{}'::jsonb,
							'engine', 'ckeditor'
						)
					)
				)
				where type in('page', 'news', 'event') and structure_id is null;`, DefaultStructureID); res.Error != nil {
		return result.Error(res.Error, "")
	} else {
		total += int(res.RowsAffected)
	}
	return result.Success(fmt.Sprintf("Migrated %v pages", total))
}

// Importer's ID: 45f06f48-a93c-414e-b9a0-7582e0abc085

var BotID = uuid.FromStringOrNil("45f06f48-a93c-414e-b9a0-7582e0abc085")
var DefaultStructureID = uuid.FromStringOrNil("27210c1e-3415-41b0-8157-88e56756677c")

func UpsertDefaultStructure(db *gorm.DB, accountID *uuid.UUID) error {
	if accountID == nil {
		accountID = &BotID
	}
	n, _ := time.Parse("2006-01-02T15:04:05.000Z", "2024-03-22T14:50:05.000Z")
	// Create default structure
	defaultStructure := structure.Structure{
		ID:            DefaultStructureID,
		Active:        true,
		Name:          "WYSIWYG",
		Template:      `{{{DctMap.mainContent.lexical.html}}}`,
		FormStructure: []byte(`[{"name": "mainContent", "title": "Main Content", "components": [{"key": true, "name": "lexical", "type": "rich-text", "label": "Content", "required": false, "validate": "", "maximumLength": ""}], "description": "Use the styles dropdown to change text from the 'Normal' style to a heading or list to make your content easier to scan, or use the Insert function to include images in your main content area."}]`),
	}

	defaultStructure.Track(n, BotID)
	return db.Save(&defaultStructure).Error
}
