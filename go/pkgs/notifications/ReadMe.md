# Notifications

## Relays
Each `Topic` requires `Relay`. Refer to [Test_SendGrid test](mailer/mailer_test.go) to see how to configure SendGrid 
(in short: `Username: "apikey", Password: "<API key>",`).

For SMTP all fields are required:

```golang
package mailer
type CommonRelayConfig struct {
	Sender   string // for bounces 
	ReplyTo string // for replays
}

type PlainSMTPConfig struct {
	CommonRelayConfig
	Username string
	Password string
	Host     string
	Port     int
}
```

## Generating alerts/bus_notifications
After admin creates Alert in CM the updated Alert information will be sent to `emailGenerators.CreateAlert` and issues will be generated for each *active* `Topic` with `SchoolID` presented in `content.Sites` prop for Alert.

The same goes for `bus_notifications` but `routes` will be used instead of `Sites`.

### Sendig emails (`mailer` service)
```
for each Tenant every ~5 minutes 
    find acive Issues with State=ready AND StartAt < Now
    for each acive Issue
        if Relay is available
            lock Relay
            start go routine
                send Issue through Relay
                unlock Relay                
```

## Templates
For email templates we are using Cerberus templates (hybrid variation): https://www.cerberusemail.com/

Github: https://github.com/TedGoas/Cerberus

[View models](templating/viewmodels/)

[Templates](templating/templates/) ([Layouts docs](templating/templates.md))



`SiteViewModel` expects these properties available in Site.Settings:
- `LogoSrc` full url to raster logo (200x50)
- `Primary` - color for links/buttons
- `PrimaryDark` - the same for dark mode
- [footer]
  - `Name` - school name
  - `Phone`
  - `Email`
  - `FullAddress` - if not provided will be calculated like: `FullAddress = strings.Join([]string{settings.Address, settings.City, settings.Province, settings.Postal}, ", ")`


Placeholders (replaced for each subscriber individually) only these for now:
- `[[UnsubscribeLink]]`
- `[[ManageLink]]`


## Render the Subscription management page

```go
type AccessCode struct {
	UserID  uuid.UUID
	Code    string
	Expires time.Time
}
```

* If `secret` is valid: then render the page
* If `userID` is present: then render a button: Get link to manage your page
  * Generate Code and send it to user's email.
* Else: render a form with Email input and button Get link to manage your page
  * Check if email exists in DB
    * Generate Code and send it to user's email

- ## /notifications/subscribe -- 
  - GET: renders input form with email and user data 
    and all available subscriptions?
  - POST: 
    - if email does not exist in DB:
        - create a new subscriber with email and user data + create subscriptions
    - else:
        - update subscriptions for the subscriber
    - Send email with a link to manage subscriptions



- ## /notifications/manage
  - GET: renders an input for email and a button "Get link to manage your page"
  - POST: 
    - if email exists in DB:
      - generate a secret and send it to user's email 
      a link to manage subscriptions with the secret
      and render a message "Check your email"
    - else:
      - error message: "Email does not exist in DB" 
      and link to  /notification/subscribe

- ## /notifications/manage?subscription=some_encoded_string (string encoded with TenantID, DomainID and SiteID)
  - GET: 
    - if  subscription is valid:
      - confirm the subscriber (update EmailConfirmedAt time)
      - render the page with all available subscriptions
    - else:
      - render an error message and a button "Get link to manage your page" 
  - POST: 
    - if  subscription is valid:
        - update subscriptions for the subscriber
        - render a message "Your subscriptions have been updated"
    - else:
      - render an error message and a button "Get link to manage your page"Subscribe
