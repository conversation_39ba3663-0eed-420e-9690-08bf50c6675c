package subscriptions

import (
	models2 "contentmanager/pkgs/notifications/models"
	"contentmanager/tests"
	uuid "github.com/satori/go.uuid"
	"strings"
	"testing"
)

func Test_DuplicateSubscription(t *testing.T) {
	ctx := tests.InitLogging("Test_DuplicateSubscription")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	//rq := shared.Request{
	//	Request:  nil,
	//	Session:  nil,
	//	Database: db.WithContext(ctx),
	//	Account: &models.Account{
	//		ID: uuid.NewV4(),
	//	},
	//}
	s1 := models2.Subscriber{
		ID:        uuid.NewV4(),
		FirstName: "John",
		LastName:  "Doe",
		Email:     "<EMAIL>",
	}
	if err := db.WithContext(ctx).Create(&s1).Error; err != nil {
		t.Error(err)
	}

	s2 := models2.Subscriber{
		ID:        uuid.NewV4(),
		FirstName: "<PERSON>",
		LastName:  "Doe",
		Email:     s1.Email,
	}

	if err := db.WithContext(ctx).Create(&s2).Error; err == nil {
		t.Error("duplicate email should not be allowed")
	} else {
		if !strings.Contains(err.Error(), "ux_subscriber_email") || !strings.Contains(err.Error(), "23505") {
			t.Error("unexpected error", err)
		}
	}
}
