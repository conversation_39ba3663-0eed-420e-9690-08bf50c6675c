package scheduler

import uuid "github.com/satori/go.uuid"

var s map[uuid.UUID]uuid.UUID

/*

Schedule crons (tenants)
	jobs[s.UUID]Job

	1.
	for each tenants->t
		foreach t.schedules -> s
			create job jobs[s.UUID]Job

	2.
	foreach message in chanel
		remove job for s.UUID
		create job and add to

create job (t.UUID, s.UUID)
	job = create issues (t.UUID, s.UUID)

create issues (t.UUID, s.UUID)
	get active Newsletters with active Subscribers
	cache[content.UUID]Content
	foreach Newsletters
		generate issue

*/

/*

Issues (ready for sending) Puller
	active tasks = 0  ?? maybe active tasks per SES config?
	each N-minute
		if active tasks > limit
			return
		for each tenants->t query for Issues in Ready | Progress-expired state
			if active tasks > limit
				return
			active tasks++
			start sending (t.UUID, issue)

start sending (t.UUID, issue)
	generate unique identifier
	update issue status to DB with optimistic locking

	while true
		get pack of subscribers with ID > MinSubscriberID subscribed for the Newsletter
		if no more
			change status to Done
			return
		send (push to channel) emails:
			select {
			case ch <- msg:
				// message sent successfully
				continue
			case <-time.After(timeout):
				// timeout occurred
				save updated MinSubscriberID and check IssueStatus, set the status to Ready if it is not Cancelled
					return
			}

		save updated MinSubscriberID and check IssueStatus
			if IssueStatus Cancelled
				return

*/
