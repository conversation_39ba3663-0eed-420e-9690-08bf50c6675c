package admin

import (
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/sns"
	"log"
	"testing"
	"time"
)

func Test_CreateTopic(t *testing.T) {
	t.Skip("skipping Test_CreateTopic")

	sess := session.Must(session.NewSessionWithOptions(session.Options{
		SharedConfigState: session.SharedConfigEnable, Config: aws.Config{
			Region: aws.String("ca-central-1"),
		},
	}))
	snsClient := sns.New(sess)

	// Create a topic
	createTopicResponse, err := snsClient.CreateTopic(&sns.CreateTopicInput{
		Name: aws.String("Test_CreateTopic"),
	})
	if err != nil {
		log.Fatalf("Error creating SNS topic: %v", err)
	}
	t.Log(createTopicResponse)

	// Subscribe to the topic
	sub1, err := snsClient.Subscribe(&sns.SubscribeInput{
		Endpoint:              aws.String("+12363381229"),
		Protocol:              aws.String("sms"),
		ReturnSubscriptionArn: aws.Bool(true),
		TopicArn:              createTopicResponse.TopicArn,
	})
	if err != nil {
		log.Fatalf("Error adding subscriber to SNS topic: %v", err)
	}
	_, err = snsClient.Subscribe(&sns.SubscribeInput{
		Endpoint:              aws.String("+12363381252"),
		Protocol:              aws.String("sms"),
		ReturnSubscriptionArn: aws.Bool(true),
		TopicArn:              createTopicResponse.TopicArn,
	})
	if err != nil {
		log.Fatalf("Error adding subscriber to SNS topic: %v", err)
	}

	// Publish a message to the topic
	publishResult, err := snsClient.Publish(&sns.PublishInput{
		Message:  aws.String("Hello world! This is an SMS message!"),
		TopicArn: createTopicResponse.TopicArn,
	})
	if err != nil {
		log.Fatalf("Error publishing to SNS topic: %v", err)
	}
	t.Log(publishResult)

	// Wait for the message to get delivered
	time.Sleep(10 * time.Second) // place breakpoint here

	// Unsuscribe from the topic
	_, err = snsClient.Unsubscribe(&sns.UnsubscribeInput{
		SubscriptionArn: sub1.SubscriptionArn,
	})
	if err != nil {
		log.Fatalf("Error removing subscriber from SNS topic: %v", err)
	}
	//_, err = snsClient.Unsubscribe(&sns.UnsubscribeInput{
	//	SubscriptionArn: sub2.SubscriptionArn,
	//})
	//if err != nil {
	//	log.Fatalf("Error removing subscriber from SNS topic: %v", err)
	//}

	// Delete the topic
	_, err = snsClient.DeleteTopic(&sns.DeleteTopicInput{
		TopicArn: createTopicResponse.TopicArn,
	})
	if err != nil {
		log.Fatalf("Error deleting SNS topic: %v", err)
	}
}
