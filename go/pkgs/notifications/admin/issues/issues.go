package issues

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	"contentmanager/library/utils/converters"
	"contentmanager/pkgs/notifications/mailer"
	"contentmanager/pkgs/notifications/models"
	uuid "github.com/satori/go.uuid"
)

type SearchQuery struct {
	pagx.Query
	sortx.SortingQuery
	Search        string
	Statuses      []string `json:"Statuses[]"`
	CreationTypes []string `json:"CreationTypes[]"`
	TopicID       *uuid.UUID
}

func Search(r *shared.AppContext, q SearchQuery) result.Result[pagx.Paginated[models.Issue]] {
	var pag pagx.Paginated[models.Issue]
	tx := r.TenantDatabase().Omit("html_content", "plain_text_content")

	if len(q.Search) > 0 {
		tx = tx.Where(models.Issue{}.SearchQuery(), `%`+q.Search+`%`)
	}

	if len(q.Statuses) > 0 {
		tx = tx.Where("status in ?", q.Statuses)
	}
	if len(q.CreationTypes) > 0 {
		tx = tx.Where("creation_type in ?", q.CreationTypes)
	}
	if q.TopicID != nil && converters.FromPointer(q.TopicID) != uuid.Nil {
		tx = tx.Where("topic_id = ?", q.TopicID)
	}

	tx = tx.Order(q.GetSortingSQL())

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}

func Preview(r *shared.AppContext, id uuid.UUID) result.Result[models.Issue] {
	var issue models.Issue
	return result.Check(issue, r.TenantDatabase().First(&issue, "id = ?", id).Error)
}

type SendSampleIssueParams struct {
	Email   string     `validate:"email"`
	IssueID *uuid.UUID `validate:"required"`
}

func SendSampleIssue(r *shared.AppContext, params SendSampleIssueParams) result.EmptyResult {
	var issue models.Issue
	if err := r.TenantDatabase().Preload("Topic").First(&issue, "id = ?", params.IssueID).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	var relay models.Relay
	if err := r.TenantDatabase().First(&relay, "id = ?", issue.Topic.RelayID).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	mailer, err := mailer.NewFromRelay(relay)
	if err != nil {
		return result.ErrorEmpty(err)
	}

	var subscriber models.Subscriber
	if err := r.TenantDatabase().First(&subscriber, "lower(email) = lower(?)", params.Email).Error; err != nil {
		// if subscriber not found, mock it
		now := r.AppTime().NowUTC()
		subscriber = models.Subscriber{
			FirstName:               "Test",
			LastName:                "Test",
			ManageCode:              converters.AsPointer(uuid.NewV4()),
			Email:                   params.Email,
			EmailConfirmedAt:        &now,
			EmailConfirmationSentAt: &now,
		}
	}

	if err := mailer.Send(&issue, subscriber, issue.BaseURL()); err != nil {
		return result.ErrorEmpty(err)
	}
	return result.SuccessEmpty()
}
