package admin

import (
	middlewares2 "contentmanager/infrastructure/middlewares"
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/notifications/admin/idmap"
	"contentmanager/pkgs/notifications/admin/issues"
	"contentmanager/pkgs/notifications/admin/schedules"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

type UriParams struct {
	Id     uuid.UUID
	Search string
	Path   string
}

func AddAdminNotifications(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {

	r.Group("/api/v1/notifications", func(router httpService.Router) {
		// TODO: move `map-ids` to a separate package/path
		router.Post("/map-ids", func(w http.ResponseWriter, r *shared.AppContext) {
			var query idmap.IDToNamesQuery
			if err := binding.JSON.Bind(r.Request(), &query); err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			utils.WriteResultJSON(w, idmap.GetIDMap(r, query))
		})

		router.Get("/topics", Topics_Get)

	}, middlewares2.RequiresAuthenticationMiddleware())

	r.Group("/api/v1/notifications", func(router httpService.Router) {

		router.Get("/test-middleware/:id/:path", func(w http.ResponseWriter, r *shared.AppContext) {
			var u UriParams
			if err := binding.MapFromMaps(&u, r.Maps()); err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			utils.WriteResponseJSON(w, u, nil)
		})

		router.Get("/schedules", func(w http.ResponseWriter, r *shared.AppContext) {
			utils.WriteResultJSON(w, schedules.GetAllSchedules(r))
		})
		router.Post("/schedules", func(w http.ResponseWriter, r *shared.AppContext) {
			var schedule schedules.Schedule
			if err := binding.JSON.Bind(r.Request(), &schedule); err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}
			utils.WriteResultJSON(w, schedules.CreateSchedule(r, schedule))
		})
		router.Patch("/schedules/:id", func(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
			id, err := p.GetUUID("id")
			if err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			var schedule schedules.Schedule
			if err := binding.JSON.Bind(r.Request(), &schedule); err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}
			utils.WriteResultJSON(w, schedules.UpdateSchedule(r, id, schedule))
		})

		router.Post("/topics", Topics_Post)
		router.Put("/topics", Topics_Put)
		router.Patch("/topics/:id", Topics_Patch)

		router.Get("/audit-records", AuditRecords_Get)

		router.Get("/issues", Issues_Get)
		router.Get("/issues/:id/preview", IssuesPreview_Get)
		router.Put("/issues/send-sample", func(w http.ResponseWriter, r *shared.AppContext) {
			var params issues.SendSampleIssueParams
			if err := binding.JSON.Bind(r.Request(), &params); err != nil {
				utils.WriteResponseJSON(w, nil, err)
				return
			}

			utils.WriteResultJSON(w, issues.SendSampleIssue(r, params))
		})

		router.Get("/subscribers", Subscribers_Get)
		router.Post("/subscribers", Subscribers_Post)
		router.Patch("/subscribers/:id", Subscribers_Patch)

		router.Get("/relays", Relays_Get)
		router.Post("/relays", Relays_Post)
		router.Patch("/relays/:id", Relays_Patch)

		// TODO: use correct permissions instead of RequireAdminMiddleware
	}, middlewares2.RequiresAuthenticationMiddleware(), middlewares2.RequireAdminMiddleware())

	return r
}
