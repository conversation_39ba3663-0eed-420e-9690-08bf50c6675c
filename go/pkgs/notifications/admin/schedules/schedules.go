package schedules

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/notifications/models"
	"contentmanager/pkgs/notifications/validating"
	uuid "github.com/satori/go.uuid"
)

type (
	Schedule struct {
		Cron        string `validate:"cron"`
		Description string `validate:"required"`
	}
)

func GetAllSchedules(rq *shared.AppContext) result.Result[[]models.Schedule] {
	schedules := []models.Schedule{}
	if err := rq.TenantDatabase().Find(&schedules).Error; err != nil {
		return result.Error(err, schedules)
	}
	return result.Success(schedules)
}

func CreateSchedule(rq *shared.AppContext, schedule Schedule) result.EmptyResult {
	vErr := schedule.Validate()
	if vErr != nil {
		return result.ErrorEmpty(vErr)
	}

	user := rq.Account()
	dbSchedule := models.Schedule{
		Cron:        schedule.Cron,
		Description: schedule.Description,
	}
	dbSchedule.Track(rq.AppTime().NowUTC(), user.ID)

	if err := rq.TenantDatabase().Save(&dbSchedule).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func UpdateSchedule(rq *shared.AppContext, id uuid.UUID, schedule Schedule) result.EmptyResult {
	vErr := schedule.Validate()
	if vErr != nil {
		return result.ErrorEmpty(vErr)
	}

	user := rq.Account()
	var dbSchedule models.Schedule
	if err := rq.TenantDatabase().Where("id = ?", id).First(&dbSchedule).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	dbSchedule.Cron = schedule.Cron
	dbSchedule.Description = schedule.Description
	dbSchedule.Track(rq.AppTime().NowUTC(), user.ID)

	if err := rq.TenantDatabase().Save(&dbSchedule).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func (s Schedule) Validate() error {
	ee := map[string]string{}
	if len(s.Cron) == 0 {
		ee["Cron"] = "Cron is required"
	} else if !validating.Cron(s.Cron) {
		ee["Cron"] = "Cron is invalid"
	}

	if len(s.Description) == 0 {
		ee["Description"] = "Description is required"
	}
	if len(ee) > 0 {
		return errx.NewValidationError(ee)
	}
	return nil
}
