package admin

import (
	"contentmanager/library/binding"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/logging"
	"contentmanager/pkgs/notifications/admin/audit_records"
	"contentmanager/pkgs/notifications/admin/issues"
	"contentmanager/pkgs/notifications/admin/relays"
	subscribers2 "contentmanager/pkgs/notifications/admin/subscribers"
	"contentmanager/pkgs/notifications/admin/topics"
	uuid "github.com/satori/go.uuid"
	"net/http"
)

type PathID struct {
	ID uuid.UUID `binding:"cm_uuid"`
}

func Relays_Patch(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := p.GetUUID("id")
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	var relay relays.PlainRelay
	if err := binding.JSON.Bind(r.Request(), &relay); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResultJSON(w, relays.Update(r, id, relay))
}
func Relays_Post(w http.ResponseWriter, r *shared.AppContext) {
	var relay relays.PlainRelay
	if err := binding.JSON.Bind(r.Request(), &relay); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	utils.WriteResultJSON(w, relays.Create(r, relay))
}
func Relays_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params relays.SearchQuery
	if err := binding.MapFromMaps(&params, r.Maps()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	utils.WriteResultJSON(w, relays.Search(r, params))
}

func AuditRecords_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params audit_records.SearchQuery
	if err := binding.MapFromMaps(&params, r.Maps()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	res := audit_records.Search(r, params)
	utils.WriteResponseJSON(w, res.GetData(), res.Unwrap())
}

func Topics_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params topics.SearchQuery
	if err := binding.MapFromMaps(&params, r.Maps()); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	res := topics.Search(r, params)
	utils.WriteResponseJSON(w, res.GetData(), res.Unwrap())
}

func Topics_Patch(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := p.GetUUID("id")
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	var topic topics.Topic
	if err := binding.JSON.Bind(r.Request(), &topic); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	logging.FromContext(r.Request().Context()).Info().Interface("topic", topic).Msg("")
	utils.WriteResultJSON(w, topics.UpdateTopic(r, id, topic))
}

func Topics_Post(w http.ResponseWriter, r *shared.AppContext) {
	var topic topics.Topic
	if err := binding.JSON.Bind(r.Request(), &topic); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	logging.FromContext(r.Request().Context()).Info().Interface("topic", topic).Msg("")
	utils.WriteResultJSON(w, topics.CreateTopic(r, topic))
}

func Topics_Put(w http.ResponseWriter, r *shared.AppContext) {
	utils.WriteResultJSON(w, topics.SeedTopics(r))
}

func Issues_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params issues.SearchQuery
	_ = binding.MapFromMaps(&params, r.Maps())

	res := issues.Search(r, params)
	utils.WriteResponseJSON(w, res.GetData(), res.Unwrap())
}

func IssuesPreview_Get(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	id, err := p.GetUUID("id")
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	res := issues.Preview(r, id)
	if res.IsError() {
		utils.WriteResponseJSON(w, res.GetData(), res.Unwrap())
		return
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Write([]byte(res.Data.HTMLContent))
}

func Subscribers_Get(w http.ResponseWriter, r *shared.AppContext) {
	var params subscribers2.SearchSubscribersQuery
	_ = binding.MapFromMaps(&params, r.Maps())

	res := subscribers2.SearchSubscribers(r, params)
	utils.WriteResponseJSON(w, res.GetData(), res.Unwrap())
}

func Subscribers_Post(w http.ResponseWriter, r *shared.AppContext) {
	var params subscribers2.Subscriber
	if err := binding.JSON.Bind(r.Request(), &params); err != nil {
		utils.WriteResponseJSON(w, nil, err)
	}

	utils.WriteResponseJSON(w, nil, subscribers2.CreateSubscriber(r, params))
}

func Subscribers_Patch(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	var params subscribers2.Subscriber
	if err := binding.JSON.Bind(r.Request(), &params); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}
	id, err := p.GetUUID("id")
	if err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	utils.WriteResponseJSON(w, nil, subscribers2.UpdateSubscriber(r, id, params))
}
