package subscribers

import (
	"contentmanager/library/shared"
	"contentmanager/pkgs/notifications/models"
	uuid "github.com/satori/go.uuid"
	"strings"
)

func UpdateSubscriber(rq *shared.AppContext, id uuid.UUID, subscriber Subscriber) error {
	vErr := subscriber.Validate()
	if vErr != nil {
		return vErr
	}

	db := rq.TenantDatabase()
	var prevSubscriber models.Subscriber
	if err := db.Where("id = ?", id).First(&prevSubscriber).Error; err != nil {
		return err
	}

	if prevSubscriber.Email != strings.ToLower(subscriber.Email) {
		prevSubscriber.Email = strings.ToLower(subscriber.Email)
		prevSubscriber.EmailConfirmedAt = nil
		prevSubscriber.EmailConfirmationSentAt = nil
	}

	if prevSubscriber.Phone != subscriber.Phone {

		prevSubscriber.Phone = subscriber.Phone
		prevSubscriber.PhoneConfirmedAt = nil
		prevSubscriber.PhoneConfirmationSentAt = nil
		prevSubscriber.PhoneConfirmationCode = nil
	}

	prevSubscriber.FirstName = subscriber.FirstName
	prevSubscriber.LastName = subscriber.LastName

	user := rq.Account()
	prevSubscriber.Track(rq.AppTime().NowUTC(), user.ID)

	if err := db.Save(&prevSubscriber).Error; err != nil {
		return err
	}

	return nil
}
