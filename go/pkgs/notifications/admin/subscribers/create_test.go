package subscribers

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/utils/converters"
	models "contentmanager/pkgs/auth/identity"
	models2 "contentmanager/pkgs/notifications/models"
	"contentmanager/tests"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"testing"
)

func Test_JSONUnmarshalling(t *testing.T) {
	type Test struct {
		ID *uuid.UUID
	}
	str := `{"id": "d02160e7-4de1-41c6-b26f-15626f4080d5"}`
	var test Test
	json.Unmarshal([]byte(str), &test)
	if test.ID == nil || test.ID.String() != "d02160e7-4de1-41c6-b26f-15626f4080d5" {
		t.Error("json unmarshal error")
	}

	str2 := `{"id": null}`
	var test2 Test
	json.Unmarshal([]byte(str2), &test2)
	if test2.ID != nil {
		t.Error("json unmarshal error")
	}

	str3 := `{}`
	var test3 Test
	err3 := json.Unmarshal([]byte(str3), &test3)
	if err3 != nil {
		t.Error("json unmarshal error")
	}
	if test3.ID != nil {
		t.Error("json unmarshal error")
	}

	str4 := `{"id": "invalid uuid"}` // invalid uuid
	var test4 Test
	err4 := json.Unmarshal([]byte(str4), &test4)
	if err4 == nil {
		t.Error("json unmarshal error")
	}

	str5 := `{"id": ""}`
	var test5 Test
	err5 := json.Unmarshal([]byte(str5), &test5)
	if err5 == nil {
		t.Error("json unmarshal error")
	}
}

func Test_CreateSubscriber(t *testing.T) {
	ctx := tests.InitLogging("Test_CreateSubscriber")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	rq := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
		TenantDB: db.WithContext(ctx),
		Identity: &models.Account{
			ID: uuid.NewV4(),
		},
	})

	subscriber := Subscriber{
		Email: "<EMAIL>",
		Phone: converters.AsPointer("+************"), // valid e164 format
	}
	err := CreateSubscriber(rq, subscriber)
	if err != nil {
		t.Error(err)
	}

	var dbSubscriber models2.Subscriber
	findErr := db.WithContext(ctx).Where("email = ?", subscriber.Email).First(&dbSubscriber).Error
	if findErr != nil {
		t.Errorf("expected no errors, got %s", findErr)
	}

	if dbSubscriber.CreatedBy != rq.Account().ID ||
		dbSubscriber.UpdatedBy != rq.Account().ID ||
		dbSubscriber.CreatedAt.IsZero() ||
		dbSubscriber.UpdatedAt != dbSubscriber.CreatedAt {
		t.Errorf("expected CreatedBy, UpdatedBy, CreatedAt, UpdatedAt to be set, got %+v", dbSubscriber)
	}

	// can't create subscriber with the same email (case insensitive)
	secondTimeErr := CreateSubscriber(rq, Subscriber{
		Email: "<EMAIL>",
	})
	if secondTimeErr == nil {
		t.Errorf("expected error, got none")
	}
}

func Test_CreateSubscriber_Validate(t *testing.T) {
	type cases struct {
		input  Subscriber
		expect int
	}
	cc := []cases{
		{input: Subscriber{}, expect: 1},
		{input: Subscriber{Email: "<EMAIL>"}, expect: 0},
		{input: Subscriber{Email: "<EMAIL>", Phone: converters.AsPointer("+1")}, expect: 1},
		{input: Subscriber{Email: "<EMAIL>", Phone: converters.AsPointer("+************")}, expect: 0},
		{input: Subscriber{Email: "", Phone: converters.AsPointer("+************")}, expect: 1},
		{input: Subscriber{Email: "<EMAIL>", Phone: converters.AsPointer("+************")}, expect: 0},
	}

	for _, c := range cc {
		err := c.input.Validate()
		if err != nil {
			vErr, _ := err.(*errx.ValidationErr)
			if len(vErr.ErrorMessages) != c.expect {
				t.Errorf("expected %d errors, got %d for %+v", c.expect, len(vErr.ErrorMessages), c.input)
			}
		}
		if c.expect == 0 && err != nil {
			t.Errorf("expected no errors, got %s for %+v, %+v", err, c.input, err)
		} else if c.expect > 0 && err == nil {
			t.Errorf("expected %d errors, got none for %+v", c.expect, c.input)
		} else if c.expect > 0 && err != nil {
			vErr := err.(*errx.ValidationErr)
			if len(vErr.ErrorMessages) != c.expect {
				t.Errorf("expected %d errors, got %d for %+v, %+v", c.expect, len(vErr.ErrorMessages), c.input, vErr)
			}
		}
	}
}
