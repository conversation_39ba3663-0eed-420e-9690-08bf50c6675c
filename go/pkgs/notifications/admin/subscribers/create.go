package subscribers

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/errx"
	"contentmanager/library/utils/converters"
	"contentmanager/pkgs/notifications/models"
	"contentmanager/pkgs/notifications/validating"
	"strings"
)

type (
	Subscriber struct {
		FirstName string
		LastName  string
		Email     string
		Phone     *string
	}
)

func CreateSubscriber(rq *shared.AppContext, subscriber Subscriber) error {
	vErr := subscriber.Validate()
	if vErr != nil {
		return vErr
	}
	user := rq.Account()
	db := rq.TenantDatabase()

	newSubscriber := models.Subscriber{
		FirstName: subscriber.FirstName,
		LastName:  subscriber.LastName,
		Email:     strings.ToLower(subscriber.Email),
		Phone:     subscriber.Phone,
	}

	newSubscriber.Track(rq.AppTime().NowUTC(), user.ID)

	return db.Create(&newSubscriber).Error
}

func (s Subscriber) Validate() error {
	ee := map[string]string{}
	if len(s.Email) == 0 {
		ee["Email"] = "Email is required"
	} else if !validating.Email(s.Email) {
		ee["Email"] = "Email is invalid"
	}
	phone := converters.FromPointer(s.Phone)
	if len(phone) > 0 &&
		!validating.PhoneE164(phone) {
		ee["Phone"] = "Phone is invalid"
	}
	if len(ee) > 0 {
		return errx.NewValidationError(ee)
	}
	return nil
}
