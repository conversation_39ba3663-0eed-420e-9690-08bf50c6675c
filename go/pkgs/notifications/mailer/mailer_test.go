package mailer

import (
	"encoding/json"
	"testing"
)

func Test_ConfigSerialization(t *testing.T) {
	jstr := []byte(`{}`)
	var srv server
	s := json.Unmarshal(jstr, &srv)
	if s != nil {
		t.Error(s)
	}
}

func Test_SendGrid(t *testing.T) {
	//m, e := NewSMTPMailer(PlainSMTPConfig{
	//	CommonRelayConfig: CommonRelayConfig{
	//		Sender:  "<EMAIL>",
	//		ReplyTo: "<EMAIL>",
	//	},
	//	Username: "apikey",
	//	// Password = API key. For peelsb.com copy/paste from here: https://app.clickup.com/t/860q4rp5y
	//	Password: "<API key>",
	//	Host:     "smtp.sendgrid.net",
	//	Port:     587,
	//})
	//if e != nil {
	//	t.Error(e)
	//}
	//if err := m.SendHTML("<EMAIL>", "Hello SendGrid SMTP", "<p>Hello SendGrid SMTP</p>"); err != nil {
	//	t.Error(e)
	//}
}

func Test_o365(t *testing.T) {
	t.Skip()
	m, e := NewSMTPMailer(PlainSMTPConfig{
		CommonRelayConfig: CommonRelayConfig{
			Sender:  "<EMAIL>",
			ReplyTo: "<EMAIL>",
		},
		Username: "",
		Password: "",
		Host:     "smtp.office365.com",
		Port:     587,
	})
	if e != nil {
		t.Error(e)
	}
	if err := m.SendHTML("<EMAIL>", "Hello o365 SMTP", "<p>Hello o365 SMTP</p>"); err != nil {
		t.Error(e)
	}
}

//func Test_SES(t *testing.T) {
//	m, e := NewSMTPMailer(PlainSMTPConfig{
//		CommonRelayConfig: CommonRelayConfig{
//			Sender:   "<EMAIL>",
//			ReplyTo: "<EMAIL>",
//		},
//		Username: "AKIA3RQ4JNO7XSU73BZ5",
//		Password: "<Password>",
//		Host:     "email-smtp.ca-central-1.amazonaws.com",
//		Port:     587,
//	})
//	if e != nil {
//		t.Error(e)
//	}
//	var wg sync.WaitGroup
//	br := false
//	start := time.Now()
//	for i := 0; i < 1000; i++ {
//		if br {
//			break
//		}
//		wg.Add(1)
//		cnt := strconv.Itoa(i)
//		//
//		go func(num string) {
//			defer wg.Done()
//			if err := m.SendHTML(uuid.NewV4().String()+"@host-web-site.com", "Hello Slow SES SMTP: "+num, "<p>Hello SES SMTP</p>"); err != nil {
//				t.Error(err)
//			}
//		}(cnt)
//	}
//
//	wg.Wait()
//	t.Log(fmt.Sprintf("Time: %s ", time.Since(start)))
//	t.Log("----")
//}

//func Test_SES_SlowRate(t *testing.T) {
//	m, e := NewSMTPMailer(PlainSMTPConfig{
//		CommonRelayConfig: CommonRelayConfig{
//			Sender:   "<EMAIL>",
//			ReplyTo: "<EMAIL>",
//		},
//		Username: "AKIA3RQ4JNO754UKDFAA",
//		Password: "<Password>",
//		Host:     "email-smtp.us-east-2.amazonaws.com", // email-smtp.us-east-2.amazonaws.com
//		Port:     587,
//	})
//	if e != nil {
//		t.Error(e)
//	}
//	var wg sync.WaitGroup
//
//	br := false
//	for i := 0; i < 2; i++ {
//		if br {
//			break
//		}
//		wg.Add(1)
//		cnt := strconv.Itoa(i % 4)
//		go func(c string, num int) {
//			defer wg.Done()
//			t.Log("Sending " + strconv.Itoa(num))
//			if err := m.SendHTML("example+"+c+"@example.com", "Hello Slow SES SMTP", "<p>Hello SES SMTP</p>"); err != nil {
//				t.Error(err)
//				br = true
//			}
//			t.Log("XXX Done " + strconv.Itoa(num))
//		}(cnt, i)
//	}
//	wg.Wait()
//	// time.Sleep(5 * time.Second)
//}
