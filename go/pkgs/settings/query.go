package settings

import (
	"bytes"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/utils/slicexx"
	"encoding/json"
	"github.com/antchfx/jsonquery"
	"github.com/antchfx/xpath"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"regexp"
	"strings"
)

// QueryJSON returns a JSON array of strings that match the given XPath query.
// More examples: https://github.com/antchfx/jsonquery
// TODO: consider using https://github.com/jqlang/jq instead of jsonquery (https://github.com/itchyny/gojq & https://jqplay.org/)
func QueryJSON(bb []byte, query string) ([]byte, error) {
	doc, err := jsonquery.Parse(bytes.NewReader(bb))
	if err != nil {
		return nil, err
	}
	expr, err := xpath.Compile(query)
	if err != nil {
		return nil, err
	}
	iter := expr.Select(jsonquery.CreateXPathNavigator(doc))
	var result []string
	for iter.MoveNext() {
		result = append(result, iter.Current().Value())
	}
	result = slicexx.Select(result, addQuotes)
	jsonStr := "[" + strings.Join(result, ",") + "]"
	return []byte(jsonStr), nil
}

func addQuotes(s string) string {
	if strings.HasPrefix(s, "[") || strings.HasPrefix(s, "{") {
		return s
	}
	return "\"" + escapeDoubleQuotes(s) + "\""
}

var r = regexp.MustCompile(`\\([\s\S])|(")`)

func escapeDoubleQuotes(str string) string {
	return string(r.ReplaceAll([]byte(str), []byte(`\$1$2`)))
}

func GetSettingsWithType(db *gorm.DB, settingsType SettingsType) ([]Settings, error) {
	var settings []Settings

	if err := db.Table(Settings{}.TableName()).Where("type = ?", settingsType).Where("active").Find(&settings).Error; err != nil {
		return []Settings{}, err
	}

	return settings, nil
}

func GetSettings(db *gorm.DB, settingsType SettingsType, sites []uuid.UUID) ([]Settings, error) {
	var allSettingsWithSiteID []Settings

	if err := db.Table(Settings{}.TableName()).Where("type = ?", settingsType).Where("active").Where(pgxx.ArrayHasAny("sites", sites)).Find(&allSettingsWithSiteID).Error; err != nil {
		return []Settings{}, err
	}

	return allSettingsWithSiteID, nil
}

// GetSecrets
// marshalTo should be a pointer to an underlying value
// (e.g): var example models.Model
// GetSecrets(db, "facebook-secrets", &example)
func GetSecrets(db *gorm.DB, secret SettingsType, marshalTo interface{}) error {
	var credentials Settings
	if err := db.
		Where("type = ?", secret).
		Where("active").
		First(&credentials).
		Error; err != nil {
		return err
	}
	if err := credentials.Validate(); err != nil {
		return err
	}
	return json.Unmarshal(credentials.Data, &marshalTo)
}
