package admin

import (
	"contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/templates"
	"net/http"
)

func AddTemplatesV2(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {
	r.Group("/api/v2/templates", func(router httpService.Router) {

		router.Get("", func(w http.ResponseWriter, r *shared.AppContext, params struct {
			bindauth.BindableParams
			FromQuery templates.SearchQuery
		}) {
			utils.WriteResultJSON(w, templates.Search(r, params.FromQuery))
		})

	}, middlewares.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware(), bindauth.AuthorizeMiddleware())
	return r
}
