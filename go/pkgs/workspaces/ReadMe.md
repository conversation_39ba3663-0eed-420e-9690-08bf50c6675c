# Workspaces

## Unresolved issues
- [ ] Templates' paths
- [ ] Distributed pages
- [ ] Delete, expire live and draft version
  
#### Resolve TODO: `TODO: @Anatoly`

## Overview
Admin users can manage content across multiple workspace environments (similar to git branches),
so that they can prepare and review content changes before publishing them to the public-facing site.

### Requirements:

1. For version 1, we will only support two workspaces: `live` and `draft`.

2. "Deletion" works differently for `live` and non-`live` workspaces:
   1. `live`: always soft-deleted
   2. non-`live`:
      1. "Soft-delete": the entity is deleted in that workspace (the same as soft-deleting in `live`)
      2. "Discard changes": means in that workspace the `live` version will be used (the row is deleted)


### Edge Cases:
1. If an admin is in the `live` version and wants to switch to a non-`live` workspace that was soft-deleted:
   The system needs to determine whether the soft-deleted version should be restored or the `live` version should be used.

   **Solution**: Show a clear indication that the version in this workspace is deleted.
   E.g.: "The version in this workspace is deleted — do you want to restore or clone from `live`?"

   Provide options to either:
    * "Restore this version" (which would undelete it)
    * "Create new version based on live" (which would create a fresh copy)

2. If the `live` version is soft-deleted:

   **Solution**: Other workspace versions should remain unaffected.

3. The "Clone" operation is only available for the `live` workspace. Other workspaces cannot be cloned.

### Examples:

A page exists in the `live` workspace.
In the editor admin switches to the `draft` workspace. 

## Implementation details

1. Content entities will implement a Workspaced interface with:

    ```go
   package shared

   type (
           Workspaced interface {
           GetWorkspace() string
           GetEntityID() uuid.UUID
           GetEffectiveIn() []string
	    }
   
    )
   ```

   - `EffectiveIn` is an array of strings on `"live"` entities only. 
   - The `EffectiveIn` field will be used for query optimization (as `workspace = ? OR ? = ANY(effective_in)`). 
   - If the entity is not present in the workspace, the system will fall back to the `live` version.
   - The `EffectiveIn` field will be updated when the entity is deleted or created in a specific workspace.
   - Each time a new workspace is created, the `EffectiveIn` field will be updated to include the new workspace.

2. The system will support multiple workspaces, with `live` being the production/public-facing version
