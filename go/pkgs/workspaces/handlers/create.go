package handlers

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/library/shared"
	"contentmanager/library/utils/slicexx"
	shared2 "contentmanager/pkgs/workspaces/shared"
	"fmt"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

type WorkspacedEntity interface {
	shared2.Workspaced
	schema.Tabler
}

// CreateWorkspacedEntity handles `effective_in` column for workspaced entities (updates 'live' if necessary)
// The function doesn't handle `Tracking` & `ID` generation
func CreateWorkspacedEntity[T WorkspacedEntity](r *shared.AppContext, entity T) error {

	return r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		var entityWorkspaces []string
		if err := tx.Table(entity.TableName()).Select("workspace").Where("id = ?", entity.GetEntityID()).Pluck("workspace", &entityWorkspaces).Error; err != nil {
			return err
		}

		if slicexx.Contains(entityWorkspaces, entity.GetWorkspace()) {
			return fmt.Errorf("Entity already exists in workspace %s. ", entity.GetWorkspace())
		}
		entityWorkspaces = append(entityWorkspaces, entity.GetWorkspace())

		if entity.GetWorkspace() == "live" {
			entity.SetEffectiveIn(shared2.EffectiveIn(entity.GetWorkspace(), r.WorkspacesInTenant(), entityWorkspaces))
		} else {
			entity.SetEffectiveIn([]string{})

			// Update live effective_in
			liveEffectiveIn := shared2.EffectiveIn("live", r.WorkspacesInTenant(), entityWorkspaces)
			if err := tx.Table(entity.TableName()).Where("id = ? AND workspace = 'live'", entity.GetEntityID()).Update("effective_in", dbDriver.PgStringArray(liveEffectiveIn)).Error; err != nil {
				return err
			}
		}

		if err := tx.Create(entity).Error; err != nil {
			return err
		}

		return nil
	})
}
