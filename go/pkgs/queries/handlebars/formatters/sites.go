package formatters

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/library/tenant/public/utils/handlebars"
	"fmt"
	"github.com/satori/go.uuid"
	"html"
	"sort"
	"strings"
)

func sites(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars.Options) handlebars.SafeString {
	var tt []uuid.UUID
	switch value.(type) {
	case driver.PgUUIDArray:
		tt = value.(driver.PgUUIDArray)
	default:
		return handlebars.SafeString(fmt.Sprintf("<!-- [sites] Error: The format is not supported for %v -->", value))
	}

	r, err := helpers.RequestContext()
	if err != nil {
		return handlebars.SafeString(fmt.Sprintf("<!-- [sites] Error: %s -->", err.Error()))
	}

	tenantSites, err := r.Sites()
	if err != nil {
		return handlebars.SafeString(fmt.Sprintf("<!-- [sites] Error: %s -->", err.Error()))
	}

	siteMap := make(map[uuid.UUID]string, len(tenantSites))
	for _, site := range tenantSites {
		siteMap[site.ID] = site.Name
	}

	names := make([]string, 0, len(tt))
	for _, siteID := range tt {
		if name, exists := siteMap[siteID]; exists {
			names = append(names, name)
		}
	}

	sort.Strings(names)
	return handlebars.SafeString(html.EscapeString(strings.Join(names, ", ")))
}
