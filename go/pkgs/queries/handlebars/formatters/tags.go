package formatters

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/library/tenant/public/utils/handlebars"
	"fmt"
	"github.com/satori/go.uuid"
	"html"
	"strings"
)

func tags(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars.Options) handlebars.SafeString {
	var tt []uuid.UUID
	switch value.(type) {
	case driver.PgUUIDArray:
		tt = value.(driver.PgUUIDArray)
	default:
		return handlebars.SafeString(fmt.Sprintf("<!-- [tags] Error: The format is not supported for %v -->", value))
	}

	r, err := helpers.RequestContext()
	if err != nil {
		return handlebars.SafeString(fmt.Sprintf("<!-- [tags] Error: %s -->", err.Error()))
	}

	names := r.TagIDsToNames(tt)
	return handlebars.SafeString(html.EscapeString(strings.Join(names, ", ")))
}
