package formatters

import (
	"contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/library/tenant/public/utils/handlebars"
	"strings"
)

func document(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars.Options) handlebars.SafeString {
	var a = strings.Builder{}
	a.WriteString("<a href='")
	a.WriteString(value.(string))
	a.WriteString("' target='_blank'>")
	a.WriteString("View Document")
	a.WriteString("</a>")
	return handlebars.SafeString(a.String())
}
