package formatters

import (
	"contentmanager/library/templates/hbs_helpers/shared"
	handlebars2 "contentmanager/library/tenant/public/utils/handlebars"
	"fmt"
	"github.com/araddon/dateparse"
	"time"
)

func timestamp(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars2.Options) handlebars2.SafeString {
	format := "Jan 2, 2006 3:04 PM"
	if config != nil {
		if formatConfig, ok := config["format"]; ok {
			if formatConfigString, ok := formatConfig.(string); ok {
				format = formatConfigString
			}
		}
	}
	switch value.(type) {
	case *time.Time:
		return handlebars2.SafeString(value.(*time.Time).Format(format))
	case time.Time:
		return handlebars2.SafeString(value.(time.Time).Format(format))
	case string:
		if t, err := dateparse.ParseAny(value.(string)); err == nil {
			return handlebars2.SafeString(t.Format(format))
		} else {
			return handlebars2.SafeString(fmt.Sprintf("<!-- [timestamp] Error: Can't parse %v | %s -->", value, err.Error()))
		}
	}

	return handlebars2.SafeString(fmt.Sprintf("<!-- [timestamp]  Error: The format is not supported for %v -->", value))
}
