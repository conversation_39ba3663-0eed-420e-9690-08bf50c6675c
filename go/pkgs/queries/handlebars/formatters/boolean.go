package formatters

import (
	"contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/library/tenant/public/utils/handlebars"
	"fmt"
)

func boolean(value interface{}, path string, model interface{}, config map[string]interface{}, helpers shared.IHbsHelpers, options *handlebars.Options) handlebars.SafeString {
	format := map[bool]string{true: "All Day", false: ""}
	switch value.(type) {
	case *bool:
		return handlebars.SafeString(format[*value.(*bool)])
	case bool:
		return handlebars.SafeString(format[value.(bool)])
	}

	return handlebars.SafeString(fmt.Sprintf("<!-- [timestamp]  Error: The format is not supported for %v -->", value))
}
