package handlebars

import (
	"contentmanager/library/shared/pagx"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/pkgs/queries/templates"
	"fmt"
	"strings"
)

func init() {
	handlebars.RegisterHelper("queryPager", queryPagerHelper)
	handlebars.RegisterPartial("queryPager", "{{#queryPager}}{{/queryPager}}")
}

func queryPagerHelper(options *handlebars.Options) handlebars.SafeString {
	root := options.RootCtx()
	vm, ok := root.(templates.CompileViewModel)
	if !ok {
		return handlebars.SafeString("<!-- [queryPagerHelper] Error: context is not a CompileViewModel -->")
	}

	return handlebars.SafeString(defaultPager(vm))
}

func defaultPager(vm templates.CompileViewModel) string {
	pager, ok := vm.QueryResult.(pagx.PaginatedResult)
	if !ok {
		return "<!-- [defaultPager] Error: context is not a pagx.Paginator -->"
	}
	if pager.GetTotalPages() <= 1 {
		return ""
	}

	result := strings.Builder{}
	result.WriteString("<div class=\"pager\">")

	for i := 1; i <= pager.GetTotalPages(); i++ {
		if i == pager.GetPage() {
			result.WriteString(fmt.Sprintf(" <span class=\"page current\">%d</span> ", i))
		} else {
			result.WriteString(fmt.Sprintf(" <a class=\"page\" href=\"#%d\" data-page=\"%d\">%d</a> ", i, i, i))
		}
	}
	result.WriteString("</div>")
	return result.String()
}
