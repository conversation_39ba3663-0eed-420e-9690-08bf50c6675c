package presenter

import (
	"contentmanager/library/helpers/init_handlebars"
	"contentmanager/library/shared/pagx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/queries/db_models"
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/queries/templates"
	"contentmanager/pkgs/structure"
	"contentmanager/tests/mocks"
	"contentmanager/tests/test_utils"
	uuid "github.com/satori/go.uuid"
	"path/filepath"
	"sort"
	"strings"
	"testing"
)

func Test_Compile(t *testing.T) {
	init_handlebars.InitHandlebars()

	var s structure.Structure
	test_utils.MustDeserializeJSON(filepath.Join("testdata", "structure.json"), &s)

	var res pagx.Paginated[db_models.Content]
	test_utils.MustDeserializeJSON(filepath.Join("testdata", "results.json"), &res)
	sort.Slice(res.Rows, func(i, j int) bool {
		name1 := res.Rows[i].CreatedAt
		name2 := res.Rows[j].CreatedAt
		return name1.Before(name2)
	})

	grouping := []models.Grouping{
		{
			Sorting: models.Sorting{
				Field:     "CreatedAt",
				Direction: "desc",
			},
			Method:  "timestamp",
			Options: "YYYY",
		},
		{
			Sorting: models.Sorting{
				Field:     "CreatedAt",
				Direction: "desc",
			},
			Method:  "timestamp",
			Options: "MMM",
		},
	}
	grouped, _ := Group(grouping, res.Rows)

	tpl := templates.GetDefaultTemplate()
	queryID := uuid.FromStringOrNil("777105f1-f9ba-48a6-ba2a-b4b6000a0574")

	q := models.Query{
		Entity:    commonModels.Entity{ID: queryID},
		Structure: &s,
		Title:     "Test Query",
	}

	params := templates.CompileHandlebarsParams{
		Query: q,
		QueryResult: pagx.PaginatedData[models.GroupedResult[db_models.Content]]{
			TotalRecords: res.TotalRecords,
			TotalPages:   res.TotalPages,
			Offset:       res.Offset,
			PageSize:     res.PageSize,
			Page:         res.Page,
			Data:         grouped,
		},
		Template: tpl,
	}

	appCtx := mocks.MockAppContext()

	output, err := templates.CompileHandlebars(appCtx, params)
	if err != nil {
		t.Error(err)
	}

	if strings.Contains(output, "data-query-id=\"777105f1-f9ba-48a6-ba2a-b4b6000a0574\"") &&
		strings.Contains(output, "Created At") &&
		strings.Contains(output, "ie-query-group-header-2") &&
		strings.Contains(output, "<span class=\"page current\">1</span>") &&
		strings.Contains(output, "Apr 29, 2023 7:55 PM") &&
		strings.Contains(output, "A moreover whom there then towards ours would several.") {
		t.Log("everything is ok")
	} else {
		t.Error("")
		t.Log(output)
	}
}
