package presenter

import (
	"contentmanager/pkgs/queries/models"
	"testing"
)

type TestRecord struct {
	ID   int    `json:"id"`
	Name string `json:"name"`
	Date string `json:"date"`
}

func Test_Misc(t *testing.T) {

	// Test for empty grouping
	grouping := []models.Grouping{}
	data := []TestRecord{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
	}

	result, err := Group(grouping, data)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(result.Records) != 2 {
		t.Fatalf("Expected 2 records, got %d", len(result.Records))
	}

	// Test for single grouping
	grouping = []models.Grouping{
		{Sorting: models.Sorting{Field: "Name"}, Method: "firstN", Options: 1},
	}
	data = []TestRecord{
		{ID: 1, Name: "Alice"},
		{ID: 2, Name: "Bob"},
	}

	result, err = Group(grouping, data)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(result.SubGroups) != 2 {
		t.Fatalf("Expected 2 subgroups, got %d", len(result.SubGroups))
	}
}

func Test_GroupingByString(t *testing.T) {
	grouping := []models.Grouping{
		{Sorting: models.Sorting{Field: "Name"}, Method: "firstN", Options: 2},
	}
	result, err := Group(grouping, testData)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(result.SubGroups) != 6 {
		t.Fatalf("Expected 6 subgroups, got %d", len(result.SubGroups))
	}
}

func Test_GroupingByYear(t *testing.T) {
	grouping := []models.Grouping{
		{Sorting: models.Sorting{Field: "Date"}, Method: "date", Options: "YYYY"},
	}
	result, err := Group(grouping, testData)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(result.SubGroups) != 2 {
		t.Fatalf("Expected 2 subgroups, got %d", len(result.SubGroups))
	}
}

func Test_GroupingByYearThenMonth(t *testing.T) {
	grouping := []models.Grouping{
		{Sorting: models.Sorting{Field: "Date"}, Method: "date", Options: "YYYY"},
		{Sorting: models.Sorting{Field: "Date"}, Method: "date", Options: "MMM"},
	}
	result, err := Group(grouping, testData)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}
	if len(result.SubGroups) != 2 {
		t.Fatalf("Expected 2 subgroups, got %d", len(result.SubGroups))
	}
	if len(result.SubGroups[1].SubGroups) != 1 {
		t.Fatalf("Expected 1 subgroups in the second group, got %d", len(result.SubGroups[0].SubGroups))
	}
}

// Must be sorted by grouped fields (in this case both sort orders must be correct - for name and for date)
var testData = []TestRecord{
	{ID: 1, Name: "Alice", Date: "2023-10-01"},
	{ID: 2, Name: "Bob", Date: "2023-11-02"},
	{ID: 3, Name: "Charlie", Date: "2023-11-03"},
	{ID: 4, Name: "Dan", Date: "2023-11-04"},
	{ID: 5, Name: "David", Date: "2023-11-05"},
	{ID: 6, Name: "Eve", Date: "2023-12-03"},
	{ID: 7, Name: "Eve", Date: "2023-12-03"},
	{ID: 8, Name: "Frodo", Date: "2024-01-03"},
}
