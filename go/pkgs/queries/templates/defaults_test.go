package templates

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"testing"
)

func Test_Templates(t *testing.T) {
	data := map[string]interface{}{
		"departments": []map[string]interface{}{
			{
				"name":     "Engineering",
				"id":       "eng-01",
				"location": "Building A",
				"employees": []map[string]interface{}{
					{"name": "<PERSON>", "role": "Software Engineer"},
					{"name": "<PERSON>", "role": "DevOps"},
				},
			},
			{
				"name":     "Marketing",
				"id":       "mkt-02",
				"location": "Building B",
				"employees": []map[string]interface{}{
					{"name": "<PERSON>", "role": "Content Writer"},
				},
			},
		},
	}

	// Create our template with nested #each helpers
	template := `
<h1>Company Departments</h1>
{{#each departments}}
  <h2>Department: {{name}}</h2>
  <ul>
    {{#each employees}}
      <li>{{name}} - {{role}}</li>
      <li>Department Info: {{../name}}, ID: {{../id}}, Location: {{../location}}</li>
      <!-- Can use the entire department object for custom helpers -->
      {{#with ../this}}
        <li>All Dept Info: Name: {{name}}, ID: {{id}}, Location: {{location}}</li>
      {{/with}}
    {{/each}}
  </ul>
{{/each}}
`
	tpl, err := handlebars.Parse(template)
	if err != nil {
		t.Fatalf("Failed to parse template: %v", err)
	}
	result, err := tpl.Exec(data)
	if err != nil {
		t.Fatalf("Failed to execute template: %v", err)
	}
	t.Logf("Rendered Template:\n%s", result)
}
