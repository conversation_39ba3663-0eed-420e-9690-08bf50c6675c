package templates

import (
	"contentmanager/etc/conf"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/queries/models"
	_ "embed"
	uuid "github.com/satori/go.uuid"
)

type (
	DefaultViewSpec struct {
		CSS             string
		JS              string
		WrapperTemplate string
		GroupTemplate   string
		ItemTemplate    string
	}
)

var (
	DefaultViewSpecs = map[string]DefaultViewSpec{
		"table": {
			CSS:             tableCSS,
			JS:              "",
			WrapperTemplate: wrapperTemplate,
			GroupTemplate:   tableGroupTemplate,
			ItemTemplate:    tableItemTemplate,
		},
		"custom": {
			CSS:             customCSS,
			JS:              "",
			WrapperTemplate: wrapperTemplate,
			GroupTemplate:   customGroupTemplate,
			ItemTemplate:    customItemTemplate,
		},
	}

	//go:embed data/wrapper.hbs
	wrapperTemplate string

	tableDefaultID = uuid.FromStringOrNil("555105f1-f9ba-48a6-ba2a-b4b6000a0555")
	//go:embed data/table_group.hbs
	tableGroupTemplate string
	//go:embed data/table_item.hbs
	tableItemTemplate string
	//go:embed data/table.css
	tableCSS string

	//go:embed data/custom_group.hbs
	customGroupTemplate string
	//go:embed data/custom_item.hbs
	customItemTemplate string
	//go:embed data/custom.css
	customCSS string
)

func GetDefaultTemplate() models.Template {
	defaultValues, _ := DefaultViewSpecs["table"]

	return models.Template{
		Entity: commonModels.Entity{
			ID: tableDefaultID,
		},
		Tracking: commonModels.Tracking{
			CreatedAt: conf.SeedDate,
			CreatedBy: conf.ImporterAccountId,
			UpdatedAt: conf.SeedDate,
			UpdatedBy: conf.ImporterAccountId,
		},
		XData: commonModels.XData{
			Data: []byte(`{
				"Cells": [	
					{"Label": "Title", "Name": "Title"},
					{"Label": "Created At", "Name": "CreatedAt"}
				]	
			}`),
		},
		Title:           "Sys Default",
		Type:            "table",
		QueryID:         nil,
		StructureID:     nil,
		Description:     "Default template",
		CSS:             defaultValues.CSS,
		JS:              defaultValues.JS,
		ItemTemplate:    defaultValues.ItemTemplate,
		GroupTemplate:   defaultValues.GroupTemplate,
		WrapperTemplate: defaultValues.WrapperTemplate,
	}
}
