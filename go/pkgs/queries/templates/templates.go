package templates

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/queries/models"
	"encoding/json"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
)

type (
	SearchTemplatesParams struct {
		pagx.Query
		sortx.SortingQuery
		QueryID     *uuid.UUID
		StructureID *uuid.UUID
	}

	CommonDTO struct {
		auth.TenantWideBase
		Title       string
		Description string
		Data        json.RawMessage
	}

	CreateTemplateDTO struct {
		CommonDTO
		Type        string // table, cards, custom
		QueryID     *uuid.UUID
		StructureID *uuid.UUID
	}

	UpdateTemplateDTO struct {
		CommonDTO
		CSS             string
		JS              string
		ItemTemplate    string
		GroupTemplate   string
		WrapperTemplate string
	}
)

func (t CommonDTO) GetScopeEntity() string {
	return "cm.query.template"
}

func SearchTemplates(r *shared.AppContext, q SearchTemplatesParams) result.Result[pagx.Paginated[models.Template]] {
	var res pagx.Paginated[models.Template]
	tx := r.TenantDatabase().Omit("css, js, item_template, group_template, wrapper_template").
		Where("active = ?", true)

	if q.QueryID != nil {
		tx = tx.Where("query_id = ?", *q.QueryID)
	}
	if q.StructureID != nil {
		tx = tx.Where("structure_id = ?", *q.StructureID)
	}
	tx = tx.Order(q.GetSortingSQL("title"))

	if err := pagination.Paginate(tx, q.Query, &res); err != nil {
		return result.Error(err, res)
	}
	return result.Success(res)
}

func GetTemplate(r *shared.AppContext, id uuid.UUID) result.Result[models.Template] {
	var template models.Template
	if err := r.TenantDatabase().Where("id = ?", id).First(&template).Error; err != nil {
		return result.Error(err, template)
	}
	return result.Success(template)
}

func CreateTemplate(r *shared.AppContext, dto CreateTemplateDTO) result.Result[uuid.UUID] {
	if err := uniqueByTitle(r, dto.Title, uuid.Nil, *dto.QueryID); err != nil {
		return result.Error(err, uuid.Nil)
	}

	template := models.Template{
		Title:       dto.CommonDTO.Title,
		Description: dto.CommonDTO.Description,
		Type:        dto.Type,
		QueryID:     dto.QueryID,
		StructureID: dto.StructureID,
		Active:      true,
		XData:       commonModels.XData{Data: dto.Data},
	}
	template.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Create(&template).Error; err != nil {
		return result.Error(err, uuid.Nil)
	}

	return result.Success(template.ID)
}

func UpdateTemplate(r *shared.AppContext, id uuid.UUID, dto UpdateTemplateDTO) result.EmptyResult {

	var template models.Template
	if err := r.TenantDatabase().Where("id = ?", id).First(&template).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	if err := uniqueByTitle(r, dto.Title, id, *template.QueryID); err != nil {
		return result.ErrorEmpty(err)
	}

	template.Title = dto.CommonDTO.Title
	template.Description = dto.CommonDTO.Description
	template.Data = dto.Data

	template.CSS = dto.CSS
	template.JS = dto.JS
	template.ItemTemplate = dto.ItemTemplate
	template.GroupTemplate = dto.GroupTemplate
	template.WrapperTemplate = dto.WrapperTemplate

	template.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&template).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	return result.SuccessEmpty()
}

func SetTemplateActive(r *shared.AppContext, id uuid.UUID, active bool) result.EmptyResult {
	var template models.Template
	if err := r.TenantDatabase().Where("id = ?", id).First(&template).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	template.Active = active
	if !active {
		template.Title = template.Title + " (deleted) " + r.AppTime().NowUTC().Format("2006-01-02 15:04:05")
	}

	template.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&template).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func uniqueByTitle(r *shared.AppContext, title string, templateID, queryID uuid.UUID) error {
	type Template struct {
		ID    uuid.UUID
		Title string
	}

	var templates []Template
	if err := r.TenantDatabase().Table("templates").
		Where("active").
		Where("query_id = ?", queryID).
		Where("lower(title) = lower(?)", title).
		Find(&templates).Error; err != nil {
		return err
	}

	if len(templates) == 0 {
		return nil
	}
	if len(templates) == 1 && templates[0].ID == templateID {
		return nil
	}

	conflicts := slicexx.Filter(templates, func(c Template) bool {
		return c.ID != templateID
	})
	conflictNames := slicexx.Select(conflicts, func(c Template) string {
		return fmt.Sprintf("%s (%s)", c.Title, c.ID.String())
	})
	return errors.New(fmt.Sprintf("The template title is already taken. Conflicting templates: %s", slicexx.JoinAsStrings(conflictNames, ", ")))
}
