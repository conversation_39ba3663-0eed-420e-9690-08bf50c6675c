package templates

import (
	"contentmanager/library/helpers/ietags"
	"contentmanager/library/shared"
	hbsHelpers "contentmanager/library/templates/hbs_helpers"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/structure"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
)

type (
	CompileHandlebarsParams struct {
		Query       models.Query
		QueryResult any
		Template    models.Template
	}

	CompileViewModel struct {
		CompileHandlebarsParams
		TypeMap map[string]string
		Suffix  string
	}
)

func CompileHandlebars(r *shared.AppContext, params CompileHandlebarsParams) (string, error) {
	vm := CompileViewModel{
		CompileHandlebarsParams: params,
		TypeMap:                 addStructureFields(params.Query.Structure),
		Suffix:                  getSuffix(params.Query.ID),
	}

	tpl, err := handlebars.Parse(params.Template.WrapperTemplate)
	if err != nil {
		return "", err
	}
	tpl.RegisterPartial("viewGroup", params.Template.GroupTemplate)
	tpl.RegisterPartial("viewItem", params.Template.ItemTemplate)

	helpers := hbsHelpers.NewHbsHelpers(r)

	html, err := tpl.ExecWithHelpers(vm, helpers)
	if err != nil {
		return "", err
	}

	return ietags.ReplaceIETags(r, html), nil
}

func getSuffix(id uuid.UUID) string {
	subID := strings.Split(id.String(), "-")[0]
	return "ie-query-" + subID
}

var typeMap = map[string]string{
	"ID":           "uuid",
	"Type":         "string",
	"Title":        "string",
	"Path":         "string",
	"Settings":     "json",
	"Meta":         "json",
	"Data":         "json",
	"Tags":         "tags",
	"Sites":        "sites",
	"DepartmentID": "sites", // nullable
	"MediaID":      "image", // nullable
	"PrivacyLevel": "int",
	"PublishAt":    "timestamp",
	"ExpireAt":     "timestamp",
	"CreatedAt":    "timestamp",
	"UpdatedAt":    "timestamp",
	"CreatedBy":    "account",
	"UpdatedBy":    "account",
	"Route":        "string",
	"Event":        "schedule",
	"StartDate":    "timestamp",
	"EndDate":      "timestamp",
	"IsAllDay":     "boolean",
	"Link":         "string",
}

type (
	Component struct {
		Name string `json:"name"`
		Type string `json:"type"`
	}

	Section struct {
		Name       string      `json:"name"`
		Components []Component `json:"components"`
	}
)

func addStructureFields(structure *structure.Structure) map[string]string {
	res := make(map[string]string, len(typeMap)+10) // +10 is an estimate for additional filters

	for k, v := range typeMap {
		res[k] = v
	}

	if structure == nil {
		return res
	}

	var sections []Section
	if err := json.Unmarshal(structure.FormStructure, &sections); err != nil {
		return res
	}

	for _, section := range sections {
		for _, component := range section.Components {
			res[fmt.Sprintf("%s.%s", section.Name, component.Name)] = component.Type
		}
	}

	return res
}
