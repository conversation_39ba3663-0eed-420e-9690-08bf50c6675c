package filters

import (
	"testing"

	uuid "github.com/satori/go.uuid"
)

func TestConvertToUUID(t *testing.T) {
	// Test valid UUID string conversion
	validUUIDStr := "123e4567-e89b-12d3-a456-************"
	expectedUUID, _ := uuid.FromString(validUUIDStr)

	result, err := ConvertTo[uuid.UUID](validUUIDStr)
	if err != nil {
		t.Errorf("Failed to convert valid UUID string: %v", err)
	}
	if result != expectedUUID {
		t.<PERSON><PERSON><PERSON>("Expected %v, got %v", expectedUUID, result)
	}

	// Test invalid UUID string conversion
	invalidUUIDStr := "not-a-uuid"
	_, err = ConvertTo[uuid.UUID](invalidUUIDStr)
	if err == nil {
		t.Error("Expected error when converting invalid UUID string, but got none")
	}

	// Test UUID array conversion
	uuidStrArray := []string{validUUIDStr, "f47ac10b-58cc-4372-a567-0e02b2c3d479"}
	expectedUUIDArray := []uuid.UUID{
		expectedUUID,
		uuid.FromStringOrNil("f47ac10b-58cc-4372-a567-0e02b2c3d479"),
	}

	resultArray, err := ConvertTo[[]uuid.UUID](uuidStrArray)
	if err != nil {
		t.Errorf("Failed to convert UUID string array: %v", err)
	}
	if len(resultArray) != len(expectedUUIDArray) {
		t.Errorf("Expected array length %d, got %d", len(expectedUUIDArray), len(resultArray))
	}
	for i, expected := range expectedUUIDArray {
		if resultArray[i] != expected {
			t.Errorf("At index %d: Expected %v, got %v", i, expected, resultArray[i])
		}
	}
}
