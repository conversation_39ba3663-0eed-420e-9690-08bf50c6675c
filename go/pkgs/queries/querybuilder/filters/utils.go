package filters

import (
	"fmt"
	"regexp"
	"strings"
)

func regex(fieldName string, keywords []string, match string, inverted bool) string {
	// Escape regex special characters in each keyword
	escapedKeywords := make([]string, len(keywords))
	for i, keyword := range keywords {
		// Escape regex special characters
		escaped := regexp.QuoteMeta(keyword)
		// Escape single quotes for SQL
		escaped = strings.ReplaceAll(escaped, "'", "''")
		escapedKeywords[i] = escaped
	}

	// Join keywords with | for regex OR
	pattern := strings.Join(escapedKeywords, "|")

	switch match {
	case "startsWith":
		pattern = fmt.Sprintf("^(%s)", pattern)
	case "endsWith":
		pattern = fmt.Sprintf("(%s)$", pattern)
	case "contains":
		pattern = fmt.Sprintf(".*(%s).*", pattern)
	case "in":
		pattern = fmt.Sprintf("(%s)", pattern)
	}

	var query string
	if strings.Contains(fieldName, ".") {
		query = fmt.Sprintf(`data @? '$.%s ? (@ like_regex "%s" flag "i")'`, fieldName, pattern)
	} else {
		query = fmt.Sprintf("{{%s}} ~* '%s'", fieldName, pattern)
	}

	if inverted {
		query = fmt.Sprintf("NOT (%s)", query)
	}

	return query
}

func invertIfNeeded(params Params, query string) string {
	if params.Field.Inverted {
		return fmt.Sprintf("NOT (%s)", query)
	}
	return query
}
