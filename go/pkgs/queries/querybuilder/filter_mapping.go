package querybuilder

import (
	"contentmanager/pkgs/queries/querybuilder/filters"
)

var filterComponentsMapping = map[string]map[string]func(params filters.Params){
	"checkbox": {
		"checked": filters.FilterYesNo,
	},
	"select": {
		"in": filters.FilterSelect,
	},
	"email": {
		"startsWith": filters.FilterMultiString,
		"endsWith":   filters.FilterMultiString,
		"contains":   filters.FilterMultiString,
		"in":         filters.FilterMultiString,
	},
	"text": {
		"startsWith": filters.FilterMultiString,
		"endsWith":   filters.FilterMultiString,
		"contains":   filters.FilterMultiString,
		"in":         filters.FilterMultiString,
	},
	"date": {
		"after":   filters.FilterDate,
		"before":  filters.FilterDate,
		"between": filters.FilterDateRange,
	},
	"privacy": {
		"privacyLevel": filters.FilterPrivacyLevel,
	},
	"tags": {
		"in":                filters.FilterTags,
		"containsAll":       filters.FilterTags,
		"lengthEqualTo":     filters.FilterNumber,
		"lengthGreaterThan": filters.FilterNumber,
		"lengthLessThan":    filters.FilterNumber,
		"lengthBetween":     filters.FilterNumberRange,
	},
	"sites": {
		"sites":             filters.FilterSites,
		"lengthEqualTo":     filters.FilterNumber,
		"lengthGreaterThan": filters.FilterNumber,
		"lengthLessThan":    filters.FilterNumber,
		"lengthBetween":     filters.FilterNumberRange,
	},
	"folders": {
		"folders": filters.FilterLegacyFolders,
	},
	"image": {
		"exists": filters.FilterYesNo,
	},
	"document": {
		"exists": filters.FilterYesNo,
	},
	"array": {
		"lengthEqualTo":     filters.FilterNumber,
		"lengthGreaterThan": filters.FilterNumber,
		"lengthLessThan":    filters.FilterNumber,
		"lengthBetween":     filters.FilterNumberRange,
	},
	"schedule": {
		"timeRange": filters.FilterDateRange,
	},
	"contact-form-link": {
		"exists": filters.FilterYesNo,
	},
}
