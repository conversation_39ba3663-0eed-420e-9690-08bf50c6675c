package querybuilder

import (
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/queries/querybuilder/filters"
	"contentmanager/servers/seeder/fakex"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"testing"
	"time"
)

func Test_ParserExhaustive(t *testing.T) {
	filters := []models.Filter{}
	now := time.Now()
	siteID := uuid.NewV4()

	goMapping := getGoMapping()
	// because of the random nature of the test, we run it multiple times
	for iter := 0; iter < 100; iter++ {
		for fieldType, goOps := range goMapping {
			for operation, goFn := range goOps {
				filter := models.Filter{
					FieldName: getFieldName(fieldType),
					FieldType: fieldType,
					Operation: operation,
					Inverted:  fakex.RandomElement([]bool{true, false}),
					Value:     getValue(goFn),
				}
				filters = append(filters, filter)
				_, err := ParseQuery(Params{
					Filters:       []models.Filter{filter},
					Now:           now,
					CurrentSiteID: siteID,
				})
				if err != nil {
					t.<PERSON>rrorf("%s -> %s -> %s -> %s : %s", fieldType, operation, goFn, filter.FieldName, err.Error())
				}
			}
		}
	}

	// not needed for now but just in case if we introduce cross filter dependencies
	_, err := ParseQuery(Params{
		Filters:       filters,
		Now:           now,
		CurrentSiteID: siteID,
	})
	if err != nil {
		t.Error(err)
	}
}

func getValue(filter string) interface{} {
	switch filter {
	case "FilterYesNo":
		return true
	case "FilterSelect":
		return []string{"one", "two"}
	case "FilterMultiString":
		return []string{"one", "two"}
	case "FilterDate":
		return "2023-01-01"
	case "FilterDateRange":
		return []string{"2023-01-01", "2023-01-02"}
	case "FilterPrivacyLevel":
		return 2
	case "FilterTags":
		return []string{"574b33e6-b41a-4b02-87d1-a5323c3e9359", "931ff6d4-8c1e-4507-b277-7df40897416a"}
	case "FilterSites":
		return fakex.RandomElement([]filters.SiteSelectorValue{
			{
				Sites:        []uuid.UUID{uuid.FromStringOrNil("574b33e6-b41a-4b02-87d1-a5323c3e9359"), uuid.FromStringOrNil("931ff6d4-8c1e-4507-b277-7df40897416a")},
				DepartmentID: nil,
			},
			{
				Sites:        nil,
				DepartmentID: nil,
			},
			{
				Sites:        nil,
				DepartmentID: &uuid.UUID{},
			},
			{
				Sites:        []uuid.UUID{uuid.FromStringOrNil("574b33e6-b41a-4b02-87d1-a5323c3e9359"), uuid.FromStringOrNil("931ff6d4-8c1e-4507-b277-7df40897416a")},
				DepartmentID: &uuid.UUID{},
			},
			{
				Sites:        []uuid.UUID{},
				DepartmentID: &uuid.UUID{},
			},
		})
	case "FilterNumber":
		return 2
	case "FilterNumberRange":
		return []int{0, 2}
	case "FilterLegacyFolders":
		return []uuid.UUID{uuid.NewV4(), uuid.NewV4()}
	default:
		panic("unknown filter")
	}
}

func getFieldName(operation string) string {
	switch operation {
	case "date":
		return fakex.RandomElement([]string{"created_at", randomDataName()})
	case "privacy":
		return "privacy_level"
	case "tags", "sites":
		return operation
	case "array":
		return fakex.RandomElement([]string{"sites", "tags", randomDataName()})
	case "text":
		return fakex.RandomElement([]string{"title", randomDataName()})
	default:
		return randomDataName()
	}
}

func randomDataName() string {
	return fmt.Sprintf("%s.%s", fakex.RandomElement([]string{"one", "two", "three", "four", "five"}), fakex.RandomElement([]string{"one", "two", "three", "four", "five"}))
}
