package querybuilder

import (
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/queries/querybuilder/filters"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"strings"
	"time"
)

type (
	Params struct {
		Filters       []models.Filter
		Now           time.Time
		CurrentSiteID uuid.UUID
	}

	Result struct {
		Parts []string
	}
)

func ParseQuery(query Params) (*Result, error) {
	queryParts := []string{}
	var errs []error

	sitesVisited := false
	for idx, filter := range query.Filters {
		if err := filter.Validate(); err != nil {
			errs = append(errs, err)
			continue
		}

		fieldType, ok := filterComponentsMapping[filter.FieldType]
		if !ok {
			errs = append(errs, fmt.Errorf("filter type %s at idx %d is not supported", filter.FieldType, idx))
			continue
		}

		if filter.FieldType == "sites" {
			sitesVisited = true
		}

		processor, ok := fieldType[filter.Operation]
		if !ok {
			errs = append(errs, fmt.Errorf("filter type %s at idx %d is not supported", filter.Operation, idx))
			continue
		}

		processor(filters.Params{
			Field:       filter,
			Placeholder: paramName(idx, filter.FieldName),
			QueryParts:  &queryParts,
			Errors:      &errs,
			Now:         query.Now,
		})
	}

	if len(errs) > 0 {
		return nil, errors.Join(errs...)
	}

	if !sitesVisited {
		queryParts = append(queryParts, fmt.Sprintf("'%s' = ANY(sites)", query.CurrentSiteID.String()))
	}

	return &Result{Parts: queryParts}, nil
}

func paramName(idx int, fieldName string) string {
	fieldName = strings.ReplaceAll(fieldName, ".", "_")
	return fmt.Sprintf("Idx%d_%s", idx, fieldName)
}
