package executor

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	events2 "contentmanager/pkgs/content/events"
	"contentmanager/pkgs/queries/db_models"
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/queries/querybuilder"
	"contentmanager/pkgs/queries/querybuilder/filters"
	"contentmanager/pkgs/queries/sorting"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm/schema"
	"reflect"
	"strings"
	"time"
)

type (
	RunQueryParams struct {
		pagx.Query
		Data         models.QueryData
		ContentTypes []string
		StructureID  *uuid.UUID
	}
)

var fieldsNamesMapping = map[string]map[string]string{
	"UpdatedAt": {"content": "updated", "document": "updated", "media": "updated"},
	"CreatedAt": {"content": "created", "document": "created", "media": "created"},
	"UpdatedBy": {"content": "publisher", "document": "", "media": ""},
	"CreatedBy": {"content": "owner", "document": "", "media": ""},
	"Title":     {"content": "title", "document": "filename", "media": "filename"},
	"Type":      {"content": "type", "document": "type", "media": "type"},
	"StartDate": {"content": "immutable_timestamptz(settings->>'startdate')", "document": "", "media": ""},
	"EndDate":   {"content": "immutable_timestamptz(settings->>'enddate')", "document": "", "media": ""},
}

func RunQuery[T schema.Tabler](r *shared.AppContext, params RunQueryParams) result.Result[pagx.Paginated[T]] {
	var pag pagx.Paginated[T]
	var zero T

	// just a sanity check for development, should never happen
	v := reflect.ValueOf(zero)
	if v.Kind() == reflect.Ptr && v.IsNil() {
		return result.Error(fmt.Errorf("[RunQuery] invalid type"), pag)
	}
	table := zero.TableName()

	location, err := time.LoadLocation(r.Timezone())
	if err != nil {
		return result.Error(err, pag)
	}

	now := time.Now().In(location)

	queryParts, err := querybuilder.ParseQuery(querybuilder.Params{
		Filters:       params.Data.Filters,
		Now:           now,
		CurrentSiteID: r.CurrentSiteID(),
	})
	if err != nil {
		return result.Error(err, pag)
	}

	customClauses := strings.Join(queryParts.Parts, " AND ")
	// replace current site id placeholder
	customClauses = strings.ReplaceAll(customClauses, "{{@CurrentSiteID}}", r.CurrentSiteID().String())

	// replace column names placeholders ({{updated_at}}, etc.) with actual column names
	for fieldName, v := range fieldsNamesMapping {
		if _, ok := v[table]; ok {
			customClauses = strings.ReplaceAll(customClauses, fmt.Sprintf("{{%s}}", fieldName), v[table])
		}
	}

	dbQuery := r.TenantDatabase().Table(table).
		Where("active").
		Where(customClauses)

	// all images are public
	if table != "media" {
		dbQuery = dbQuery.Where(" ((privacy_level & ?) = privacy_level OR privacy_level = 0) ", r.PublicAccount().PrivacyLevel)
	}

	// only content has publish period and workspace
	if table == "content" {
		dbQuery = dbQuery.Where(pgxx.PublishPeriod(now.UTC(), "", false)).
			Where(pgxx.Workspace(r.Workspace(), ""))
	}

	if len(params.ContentTypes) > 0 {
		dbQuery = dbQuery.Where("type IN (?)", params.ContentTypes)
	}

	if params.StructureID != nil {
		dbQuery = dbQuery.Where("structure_id = ?", *params.StructureID)
	}

	/*
		Add sorting
	*/
	// Event's schedule handled separately
	timeRange, isEventTimeRange := params.Data.DateRange()
	if isEventTimeRange {
		if err := dbQuery.Limit(1000).Find(&pag.Rows).Error; err != nil {
			return result.Error(err, pag)
		}
		from, to, err := filters.ParseDateRange(timeRange.Value, now)
		if err != nil {
			return result.Error(err, pag)
		}

		if contents, ok := any(pag.Rows).([]db_models.Content); ok {
			pag.Rows = any(expandEvents(r, contents, from, to)).([]T)
		}

		var sortings []sorting.Sorting
		seen := make(map[string]struct{})
		for _, g := range params.Data.Grouping {
			if _, ok := seen[g.Field]; !ok {
				seen[g.Field] = struct{}{}
				sortings = append(sortings, sorting.Sorting{
					FieldName: g.Field,
					Direction: g.Direction,
				})
			}
		}
		if _, ok := seen[params.Data.Sorting.Field]; !ok {
			sortings = append(sortings, sorting.Sorting{
				FieldName: params.Data.Sorting.Field,
				Direction: params.Data.Sorting.Direction,
			})
		}

		pag.Rows = sorting.Sort(pag.Rows, sortings)

		pag.TotalRecords = int64(len(pag.Rows))
		pag.TotalPages = 1
		pag.PageSize = len(pag.Rows)
		pag.Page = 1
		pag.Offset = 0
		return result.Success(pag)

	} else {
		// process grouping first
		for i, _ := range params.Data.Grouping {
			fn := params.Data.Grouping[i].Field
			if m, ok := fieldsNamesMapping[params.Data.Grouping[i].Field]; ok {
				fn = m[table]
			}

			sq := sortx.SortingQuery{
				SortingField:     fn,
				SortingDirection: params.Data.Grouping[i].Direction,
			}
			sortingQuery := sq.GetSortingSQL(fn)
			dbQuery = dbQuery.Order(sortingQuery)
		}

		// process sorting
		if m, ok := fieldsNamesMapping[params.Data.Sorting.Field]; ok {
			params.Data.Sorting.Field = m[table]
		}
		sqParams := sortx.SortingQuery{
			SortingField:     params.Data.Sorting.Field,
			SortingDirection: params.Data.Sorting.Direction,
		}
		sortingQuery := sqParams.GetSortingSQL(fieldsNamesMapping["UpdatedAt"][table])
		dbQuery = dbQuery.Order(sortingQuery)

		// default applied always
		dbQuery = dbQuery.Order("coalesce(updated, created) desc").Order("id")
		// Paginate
		params.PageSize = params.GetPageSizeWithLimit(1000)
		if err := pagination.Paginate(dbQuery, params, &pag); err != nil {
			return result.Error(err, pag)
		}
	}

	if pag.Rows == nil {
		pag.Rows = []T{}
	}

	return result.Success(pag)
}

func expandEvents(r *shared.AppContext, events []db_models.Content, from, to time.Time) []db_models.Content {
	if len(events) == 0 {
		return events
	}

	tz := r.Timezone()

	var expandedEvents = make([]db_models.Content, 0)
	for _, event := range events {
		if len(event.Event.RRule) == 0 {
			expandedEvents = append(expandedEvents, event)
			continue
		}

		schedules, err := events2.GetExpandedSchedules(*event.Event, tz, from, to)
		if err != nil {
			continue
		}
		for _, schedule := range schedules {
			expandedEvent := event
			expandedEvent.Settings, _ = json.Marshal(schedule)
			expandedEvent.ProcessSchedule(r)
			expandedEvents = append(expandedEvents, expandedEvent)
		}
	}
	return expandedEvents
}
