
## TODO/TBD

### Discuss with designers
- [ ] Default templates (feedback from designers)
- [ ] Styling: should we keep CSS vars in the default CSS, or should we move them to the master CSS (per district)?

### Developers and PO
- [ ] Nothing prevent us from implementing raw SQL queries.
- [ ] Queries for accounts. It's possible, but it's not implemented. Do we need it?
- [ ] Folders for documents. It's impossible to implement with current database structure.
- [ ] `select` from DCT. Only values stored in the database, so it's impossible to sort/group by `label`
  (though it's possible to access them in the template, but it hasn't been implemented)
- [ ] Implement custom paging (default, select) and make it configurable (display on top and bottom).
  This will require updating [`ie-query.js`](templates/data/ie-query.js).- 
 
## Dev

### Back-end Structure

The `query` module structure:
```
queries (the root of the feature)
│
├─public        // Public endpoints
│
├─admin         // Admin endpoints and CRUD operations for Queries / Preview for admins
│
├─models        // Core Query and Template DB Models (for admin CRUD)
│
├─db_models     // Db Models for tables we execute query against
│
├─querybuilder  // Parsing, validation, and SQL query building
│ └─ filters    // 1:1 with filter-components from UI which handle deserialization and converting to SQL
│
├─executor      // Executes the query, handles paging, and content-type specific logic (recurrent events timerange)
│
├─presenter     // Builds ViewModel and renders with Handlebars
│
└─templates     // Template Retrieval and Management (including admin CRUD)
  └─   data     // Embedded default values for templates (item, group, wrapper, js, css) 
```

## Recurring Events Considerations

Recurring events are only handled correctly in event-specific queries (queries where ContentTypes contains only [`event`])
and the time range parameter is provided.

Limitations:
- Pagination is disabled, all results are returned in one page
- Event-specific time range parameter is mandatory. If not provided, the query will be executed as a regular query, 
  no recurrence rules are applied.
- Database query is limited to 1000 rows (this includes recurring events whose start/end dates may fall outside 
  the specified time range, as they need to be processed for recurrence rules)

### Timezone Considerations
For grouping to work correctly for IsAllDay events, we need to convert them to the same timezone as the rest of the events.
(See the code in the method `ProcessSchedule` in [db_models/content.go](db_models/content.go))


## Handlebars Helpers and Partials

### {{>subGroupsOrRecords}}
The structure of a grouped result:
```go
type GroupedResult[T any] struct {
    GroupKey  string
    Level     int
    SubGroups []GroupedResult[T]
    Records   []T
}
```
You usually need to iterate over either `SubGroups` or `Records`. To simplify this, we can use `{{>subGroupsOrRecords}}`.

The source:
```handlebars
{{#if SubGroups}}
    {{#each SubGroups}}
        {{>viewGroup}}
    {{/each}}
{{/if}}

{{#if Records}}
    {{#each Records}}
        {{>viewItem}}
    {{/each}}
{{/if}}
```

### {{>ieQuery DctMap.mainContent.rquery}}
You can use it with DCT components: `{{>ieQuery DctMap.mainContent.nameOfTheQueryComponent}}`.
The source:
```handlebars
{{#with this}}
    {{#if queryID}}
        <ie-query data-query-id="{{queryID}}"
            {{#if templateID}}data-template-id="{{templateID}}"{{/if}}
            {{#if pageSize}}data-page-size="{{pageSize}}"{{/if}}
            ></ie-query>
    {{/if}}
{{/with}}
```
