package db_models

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenant/common/models"
	"contentmanager/pkgs/auth"
	"fmt"
	"gorm.io/gorm"
	"sort"
	"strings"
)

type Folder struct {
	commonModels.Entity
	auth.SharableBase
	Filename     string
	Path         string
	ParentPath   string
	PrivacyLevel int
	SiteNames    []string `gorm:"-"`
	Level        int      `gorm:"-"`
	FolderPath   string   `gorm:"-"`
}

func (c *Folder) AfterFind(tx *gorm.DB) error {
	r, ok := tx.Statement.Context.Value("app_context").(*shared.AppContext)
	if !ok {
		return fmt.Errorf("app_context not found in context")
	}
	c.SiteNames = []string{}
	for _, siteID := range c.Sites {
		if site, err := r.SiteByID(siteID); err == nil {
			c.SiteNames = append(c.SiteNames, site.Name)
		}
	}

	c.FolderPath = r.PathAsFolders(c.Path)

	sort.Strings(c.Site<PERSON>)

	if c.ParentPath == "" {
		c.Level = 0
	} else {
		c.Level = len(strings.Split(c.ParentPath, "."))
	}

	return nil
}
