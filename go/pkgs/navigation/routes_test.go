package navigation

import (
	tenancyModels "contentmanager/library/tenancy/models"
	"reflect"
	"testing"
)

func Test_Reflect(t *testing.T) {
	var s1 []tenancyModels.Site
	var s2 []tenancyModels.Site
	t1 := reflect.TypeOf(s1)
	t2 := reflect.TypeOf(s2)
	if t1 != t2 {
		t.<PERSON><PERSON>("t1 != t2")
	}
}

func Test_Clone(t *testing.T) {
	type Struct struct {
		Name string
	}
	ss := map[string]Struct{
		"1": Struct{
			Name: "1",
		},
	}
	s1 := ss["1"]
	s2 := ss["1"]
	s2.Name = "2"
	if s1.Name == s2.Name {
		t.<PERSON>("s1.Name == s2.Name")
	}
}
