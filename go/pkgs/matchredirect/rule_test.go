package matchredirect

import (
	"net/url"
	"strings"
	"testing"
)

func Test_Redirects(t *testing.T) {
	var (
		cases = map[string]struct {
			res string
			ok  bool
		}{
			"https://rcsd.ca/schools/deshaye/some/url/here.html":                                   {res: "https://deshaye.rcsd.ca/some/url/here.html", ok: true},
			"https://example.com/TestString/":                                                      {res: "/string/", ok: true},
			"https://example.com/adult-learning/students/Pages/default.aspx":                       {res: "/students/", ok: true},
			"https://example.com/adult-learning/students/Pages/":                                   {res: "/students/", ok: true},
			"https://peelschools.org/adult-learning/resources/students/Documents/someDocument.pdf": {res: "https://peelschools.org/documents/someDocument.pdf", ok: true},
			"https://random.com/test":                                                              {res: "https://random.com/test", ok: false},
			"https://ecsd.cmdesign.imagineeverything.com/1970":                                     {res: "https://stbrendan.cmdesign.imagineeverything.com", ok: true},
			"https://www.rcsd.ca/learningonline":                                                   {res: "https://lo.rcsd.ca", ok: true},
		}

		redirects = []Rule{
			//{
			//	Type:     Regex,
			//	UrlContext: HostAndPath,
			//	From:     `https://rcsd\.ca/schools/([^/]+)/(.*?)$`,
			//	To:       "https://$1.rcsd.ca/$2",
			//},
			{
				Type:        Custom,
				UrlContext:  HostAndPath,
				FromPattern: "https://ecsd.cmdesign.imagineeverything.com/1970(*)$",
				ToPattern:   "https://stbrendan.cmdesign.imagineeverything.com",
			},
			{
				Type:        Custom,
				UrlContext:  HostAndPath,
				FromPattern: `https://rcsd.ca/schools/([path-segment])/(*)$`,
				ToPattern:   "https://$1.rcsd.ca/$2",
			},
			{
				Type:        String,
				UrlContext:  PathOnly,
				FromPattern: "/TestString",
				ToPattern:   "/string/",
			},
			{
				Type:        Regex,
				UrlContext:  PathOnly,
				FromPattern: "(?:.*?)/([^/]+)/Pages(?:/default.aspx|/|)$",
				ToPattern:   "/$1/",
			},
			//{
			//	Type:     Regex,
			//	UrlContext: PathOnly,
			//	From:     `.*?/resources/.*?/Documents/([^/.]+)\.([^/]+)$`,
			//	To:       "https://peelschools.org/documents/$1.$2",
			//},
			{
				Type:        Custom,
				UrlContext:  PathOnly,
				FromPattern: `*/resources/*/Documents/([path-segment].[path-segment])$`,
				ToPattern:   "https://peelschools.org/documents/$1",
			},
			{
				Type:        Custom,
				UrlContext:  HostAndPath,
				FromPattern: `https://www.rcsd.ca/learningonline(*)$`,
				ToPattern:   "https://lo.rcsd.ca",
			},
		}
	)

	for i, r := range redirects {
		err := r.Initialize()
		if err != nil {
			t.Errorf("%v\n", err)
		}
		redirects[i] = r
	}

	for input, expected := range cases {
		i, _ := url.Parse(input)
		res, ok := match(i, redirects)
		if !strings.EqualFold(res.String(), expected.res) || ok != expected.ok {
			t.Errorf("%s != %s\nInput: %s\n", res.String(), expected.res, input)
		}
	}
}
