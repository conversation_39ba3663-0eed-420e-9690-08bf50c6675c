package matchredirect

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

var (
	ErrToPatternEmpty       = fmt.Errorf("r.ToPattern can't be empty")
	ErrFromEmpty            = fmt.Errorf("r.FromPattern can't be empty or contain only `/`")
	ErrAbsolutePathRequired = fmt.Errorf("only absolute paths can be used, r.FromPattern should start from `/` if Rule uses only URL path")
	ErrAbsoluteURLRequired  = fmt.Errorf("if Rule uses both HOST and PATH then FROM should be an absolute URL")
)

type Type string

const (
	String Type = "string"
	Regex  Type = "regex"
	Custom Type = "custom"
)

type UrlContext string

const (
	HostAndPath UrlContext = "host-path"
	PathOnly    UrlContext = "path"
)

// customPatternReplacements holds the static mapping of custom pattern tokens to their regex equivalents
var customPatternReplacements = []struct{ old, new string }{
	{".", `\.`},
	{"*", `.*?`},
	{"[path-segment]", `[^/]+`},
	{"[schema]", `http[s]{0,1}://`},
	{"[optional-www]", `(?:www\.|)`},
	{"[optional-slash]", `(?:/|)`},
}

type Rule struct {
	Type        Type
	UrlContext  UrlContext
	FromPattern string
	ToPattern   string
	regx        *regexp.Regexp
	toParsed    *url.URL
	fromParsed  *url.URL
}

func (r *Rule) String() string {
	return r.FromPattern
}

func (r *Rule) Regex() *regexp.Regexp {
	return r.regx
}

func (r *Rule) Initialize() error {
	if len(r.ToPattern) == 0 {
		return ErrToPatternEmpty
	}

	if len(strings.Trim(r.FromPattern, " /")) == 0 {
		return ErrFromEmpty
	}

	r.FromPattern = strings.ToLower(r.FromPattern)

	if r.Type == String {
		if from, err := url.Parse(r.FromPattern); err != nil {
			return err
		} else {
			r.fromParsed = from
		}

		if to, err := url.Parse(r.ToPattern); err != nil {
			return err
		} else {
			r.toParsed = to
		}

		if r.UrlContext == PathOnly {
			if !strings.HasPrefix(r.FromPattern, "/") {
				return ErrAbsolutePathRequired
			}
		} else {
			if !r.fromParsed.IsAbs() {
				return ErrAbsoluteURLRequired
			}
		}
	}

	if r.Type == Regex {
		e := r.compileRegex(r.FromPattern)
		if e != nil {
			return e
		}
	}

	if r.Type == Custom {
		e := r.compileCustom(r.FromPattern)
		if e != nil {
			return e
		}
	}

	return nil
}

func (r *Rule) compileRegex(str string) error {
	regx, e := regexp.Compile("(?i)" + str)
	if e != nil {
		return e
	}
	r.regx = regx
	return nil
}

func (r *Rule) compileCustom(str string) error {
	for _, replacement := range customPatternReplacements {
		str = strings.ReplaceAll(str, replacement.old, replacement.new)
	}

	return r.compileRegex(str)
}

func getUrlParts(u *url.URL) (full string, path string) {
	path = strings.ToLower(strings.TrimRight(u.Path, " /")) // ignore ending / to match both '/test/' and '/test'
	full = strings.ToLower(u.Scheme + "://" + u.Host + path)
	return
}

func checkStringPattern(str string, r Rule) (*url.URL, bool) {
	if str != r.FromPattern {
		return nil, false
	}
	return r.toParsed, true
}

func checkRegexpPattern(str string, r Rule) (*url.URL, bool) {
	if !r.regx.MatchString(str) {
		return nil, false
	}

	res := r.regx.ReplaceAllString(str, r.ToPattern)
	u, err := url.Parse(res)
	if err != nil {
		return nil, false
	}
	return u, true
}
