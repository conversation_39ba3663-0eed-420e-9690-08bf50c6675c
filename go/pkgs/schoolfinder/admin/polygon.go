package admin

import (
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/logging"
	polygonxx "contentmanager/pkgs/schoolfinder/polygon"
	"errors"
	"gorm.io/gorm"
)

func StoreDocumentPolygons(dbCon *gorm.DB, document commonModels.Document, binary string) {
	// Delete polygons if the document is not active
	if document.Active == false {
		if err := dbCon.Where("document_id = ?", document.ID).Delete(&polygonxx.MapOverlayPolygon{}).Error; err != nil {
			logging.RootLogger().Error().Err(err).Msgf("[StoreDocumentPolygons] Failed to delete related overlies for document: %s", document.ID.String())
		}

		return
	}

	// Ignore empty binary
	if len(binary) == 0 {
		return
	}

	// Convert the document to overlays and "replace" the existing ones
	overlays, err := polygonxx.ConvertDocumentToOverlays(document.ID, document.Filename, binary)
	if err != nil {
		if !errors.Is(err, polygonxx.ErrUnsupportedFileType) {
			logging.RootLogger().Error().Err(err).Msgf("[StoreDocumentPolygons] Failed to convert document to overlays: %v", err)
		}
		return
	}

	if err := dbCon.Transaction(func(tx *gorm.DB) error {
		if err := dbCon.Where("document_id = ?", document.ID).Delete(&polygonxx.MapOverlayPolygon{}).Error; err != nil {
			return err
		}
		return dbCon.Create(&overlays).Error
	}); err != nil {
		logging.RootLogger().Error().Err(err).Msgf("[StoreDocumentPolygons] Failed to create overlays: %v", err)
	}
}
