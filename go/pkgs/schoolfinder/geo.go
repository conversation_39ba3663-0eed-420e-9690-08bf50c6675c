package schoolfinder

import (
	polygonxx "contentmanager/pkgs/schoolfinder/polygon"
	"gorm.io/gorm"
	"strconv"
)

type GeoQuery struct {
	Lat string `json:"lat"`
	Lng string `json:"lng"`
}

func (gq GeoQuery) GeometricPoint() (string, error) {
	var err error
	var point polygonxx.GeometricPoint
	if point.Y, err = strconv.ParseFloat(gq.Lat, 64); err != nil {
		return "", err
	}
	if point.X, err = strconv.ParseFloat(gq.Lng, 64); err != nil {
		return "", err
	}
	return point.String(), nil
}

// QueryCoordinates TODO => Join, filter inactive documents
//
//	future: filter unpublished documents.
func QueryCoordinates(db *gorm.DB, coordinates string) ([]string, error) {
	var documentIds = []string{}
	db.Model(polygonxx.MapOverlayPolygon{}).
		Select("document_id").
		Where("polygon @> ?", coordinates).
		Find(&documentIds)
	return documentIds, db.Error
}
