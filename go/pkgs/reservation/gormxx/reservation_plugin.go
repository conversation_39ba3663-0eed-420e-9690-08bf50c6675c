package gormxx

import (
	"contentmanager/infrastructure/database/pgxx"
	appContext "contentmanager/library/context"
	"contentmanager/pkgs/reservation/models"
	"errors"
	"gorm.io/gorm"
	"reflect"
)

type ReservationPlugin struct{}

func (p ReservationPlugin) Name() string {
	return "reservationPlugin"
}

func (p ReservationPlugin) Initialize(db *gorm.DB) error {
	if err := db.Callback().Update().Before("gorm:update").Register("reservation:before_update", p.beforeUpdate); err != nil {
		return err
	}

	return nil
}

func (p ReservationPlugin) beforeUpdate(tx *gorm.DB) {
	p.checkReservations(tx)
}

func (p ReservationPlugin) checkReservations(tx *gorm.DB) {
	if tx.Statement.Schema == nil {
		return
	}

	switch tx.Statement.ReflectValue.Kind() {
	case reflect.Struct:
		// Example: Check if this is a reservation-related model
		if reservable, ok := tx.Statement.ReflectValue.Interface().(interface{ ReservationKey() string }); ok {
			validateReservations(tx, []string{reservable.ReservationKey()})
		}
	case reflect.Slice, reflect.Array:
		var keys []string
		for i := 0; i < tx.Statement.ReflectValue.Len(); i++ {
			item := tx.Statement.ReflectValue.Index(i)
			if reservable, ok := item.Interface().(interface{ ReservationKey() string }); ok {
				keys = append(keys, reservable.ReservationKey())
			}
		}
		validateReservations(tx, keys)
	}
}

func validateReservations(tx *gorm.DB, keys []string) {
	if len(keys) == 0 {
		return
	}

	r, ok := tx.Statement.Context.Value("app_context").(appContext.AppContextProvider)
	if !ok {
		tx.AddError(errors.New("[validateReservations] app_context not found in context"))
		return
	}

	if r.Account() == nil {
		tx.AddError(errors.New("[validateReservations] account not found in context"))
		return
	}

	editingSession, err := r.EditingSession()
	if err != nil {
		tx.AddError(err)
		return
	}

	userID := r.Account().ID

	var reservations []models.Reservation
	if len(keys) > 0 {
		// tx.Session(&gorm.Session{NewDB: true}) keeps transaction but resets clauses
		if err := tx.Session(&gorm.Session{NewDB: true}).Where(pgxx.FieldInArray("key", keys)).
			Find(&reservations).Error; err != nil {
			tx.AddError(err)
			return
		}
	}

	var errs []error
	for _, rsrv := range reservations {
		if e := rsrv.AvailableFor(userID, editingSession); e != nil {
			errs = append(errs, e)
		}
	}

	if len(errs) > 0 {
		tx.AddError(errors.Join(errs...))
	}
}

//// Register the plugin
//db.Use(ReservationPlugin{})
