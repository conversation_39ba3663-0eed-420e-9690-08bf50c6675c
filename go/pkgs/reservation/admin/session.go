package admin

import (
	"contentmanager/library/shared"
	"contentmanager/library/shared/result"
	"contentmanager/pkgs/reservation/models"
	"errors"
	"gorm.io/gorm"
	"time"
)

func StartEditingSessionFor(r *shared.AppContext, key string) result.EmptyResult {
	sessionKey, sessErr := r.EditingSession()
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}
	if err := validateEditingSession(sessionKey); err != nil {
		return result.ErrorEmpty(err)
	}

	return result.CheckEmpty(r.TenantDatabase().Transaction(func(tx *gorm.DB) error {
		var reservation models.Reservation
		if err := tx.Where("key = ?", key).First(&reservation).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				reservation = models.Reservation{
					Key:           key,
					CurrentEditor: &r.Account().ID,
				}
			} else {
				return err
			}
		} else if reservation.CurrentEditor != nil && *reservation.CurrentEditor == r.Account().ID &&
			reservation.EditingSession != nil && reservation.EditingSession.Equal(sessionKey.UTC()) {
			return nil // Already editing this reservable
		}

		if err := reservation.AvailableFor(r.Account().ID, sessionKey); err != nil {
			return err
		}

		// TODO: @Anatoly implement permissions check

		reservation.EditingSession = sessionKey
		if err := tx.Save(&reservation).Error; err != nil {
			return err
		}

		return nil
	}))
}

func EndEditingSessionFor(r *shared.AppContext, key string) result.EmptyResult {
	sessionKey, sessErr := r.EditingSession()
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}
	if sessionKey == nil {
		return result.ErrorEmpty(errors.New("no editing session found"))
	}

	if err := r.TenantDatabase().Model(&models.Reservation{}).Where("key = ? AND current_editor = ? AND editing_session = ?", key, r.Account().ID, sessionKey).Updates(map[string]interface{}{
		"editing_session": nil,
	}).Error; err != nil {
		return result.ErrorEmpty(err)
	}
	return result.SuccessEmpty()
}

func ExtendEditingSession(r *shared.AppContext, newSession time.Time) result.EmptyResult {
	sessionKey, sessErr := r.EditingSession()
	if sessErr != nil {
		return result.ErrorEmpty(sessErr)
	}

	// if current session is invalid, we cannot extend it
	if err := validateEditingSession(sessionKey); err != nil {
		return result.SuccessEmpty()
	}

	if err := validateEditingSession(&newSession); err != nil {
		return result.ErrorEmpty(err)
	}

	if err := r.TenantDatabase().Model(&models.Reservation{}).Where("current_editor = ? AND editing_session = ?", r.Account().ID, sessionKey).Updates(map[string]interface{}{
		"editing_session": newSession,
	}).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func validateEditingSession(tt ...*time.Time) error {
	if len(tt) == 0 {
		return errors.New("no editing session time provided")
	}

	for _, t := range tt {
		if t == nil {
			return errors.New("editing session time cannot be nil")
		}
		if t.IsZero() {
			return errors.New("editing session time cannot be zero")
		}
		if t.Before(time.Now().UTC()) {
			return errors.New("editing session time cannot be in the past")
		}
	}
	return nil
}
