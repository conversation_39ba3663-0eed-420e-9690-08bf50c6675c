package googleapi

import (
	"errors"
)

// MaxPageSize is the maximum Page size for an API-call. This will be rewritten to use paging some day. Currently limits environments to 999 entries (e.g. Users, CalendarEvents etc.)
const MaxPageSize int = 999

var (
	// ErrFindUser is returned on any func that tries to find a user with the given parameters that can not be found
	ErrFindUser = errors.New("unable to find user")
	// ErrFindGroup is returned on any func that tries to find a group with the given parameters that can not be found
	ErrFindGroup = errors.New("unable to find group")
)
