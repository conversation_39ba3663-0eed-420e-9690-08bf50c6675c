package logging

import (
	"contentmanager/logging/stacktrace"
	"context"
	"errors"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/hlog"
	"github.com/rs/zerolog/log"
	"io"
	stdlog "log"
	"net/http"
	"os"
	"sync"
	"time"
)

func init() {
	zerolog.ErrorMarshalFunc = func(err error) interface{} {
		var errChain []string
		for err != nil {
			errChain = append(err<PERSON>hain, err.Error())
			err = errors.Unwrap(err)
		}
		return err<PERSON>hain
	}
}

var (
	ServiceName   string
	LogOutput     io.Writer // used for tests
	defaultLogger zerolog.Logger
	loggerMu      sync.RWMutex
	once          sync.Once
)

func rootLogger() zerolog.Logger {
	once.Do(func() {
		if len(ServiceName) == 0 {
			ServiceName = "no_name"
		}
		InitLogging()
	})

	loggerMu.RLock()
	logger := defaultLogger.With().Str("role", ServiceName).Logger()
	loggerMu.RUnlock()
	return logger
}
func RootLogger() *zerolog.Logger {
	l := rootLogger()
	return &l
}

func FromRequest(r *http.Request) *zerolog.Logger {
	return hlog.FromRequest(r)
}

func FromContext(context context.Context) *zerolog.Logger {
	return zerolog.Ctx(context)
}

func InjectLoggerInRequest(req *http.Request) *http.Request {
	l := RootLogger()
	return req.WithContext(l.WithContext(req.Context()))
}

func InjectLoggerInContext(ctx context.Context) context.Context {
	l := RootLogger()
	return l.WithContext(ctx)
}

func IsDebug() bool {
	val := os.Getenv("DEBUG")
	return val == "1" || val == "true"
}

func InitLogging() {
	debug := IsDebug()

	zerolog.ErrorStackMarshaler = stacktrace.ErrorMarshaller
	if debug {
		initDebug()
	} else {
		initProd()
	}

	loggerMu.RLock()
	log.Logger = defaultLogger.With().CallerWithSkipFrameCount(4).Str("level", "info").Logger()
	loggerMu.RUnlock()

	stdlog.SetFlags(0)
	stdlog.SetOutput(log.Logger)
}

func initDebug() {
	zerolog.SetGlobalLevel(zerolog.TraceLevel)

	output := zerolog.ConsoleWriter{Out: os.Stdout, TimeFormat: time.RFC3339}
	//output.FormatLevel = func(i interface{}) string {
	//	if i == nil {
	//		i = zerolog.InfoLevel
	//	}
	//	return strings.ToUpper(fmt.Sprintf("| %-6s|", i))
	//}
	//output.FormatMessage = func(i interface{}) string {
	//	return fmt.Sprintf("\"%s\"", i)
	//}
	//output.FormatFieldName = func(i interface{}) string {
	//	return fmt.Sprintf("%s:", i)
	//}
	//output.FormatFieldValue = func(i interface{}) string {
	//	return strings.ToUpper(fmt.Sprintf("%s", i))
	//}

	loggerMu.Lock()
	defaultLogger = zerolog.New(output).With().Caller().Timestamp().Logger()
	loggerMu.Unlock()

}

func initProd() {
	zerolog.SetGlobalLevel(zerolog.InfoLevel)

	zerolog.TimeFieldFormat = "2006-01-02T15:04:05.000Z"

	if LogOutput == nil {
		LogOutput = os.Stderr
	}

	loggerMu.Lock()
	defaultLogger = zerolog.New(LogOutput).With().Timestamp().Caller().Logger()
	loggerMu.Unlock()
}
