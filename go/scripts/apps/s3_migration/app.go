package main

// TODO => @Nick: Confirm & Delete
//
//import (
//	"contentmanager/library/shared"
//	tenancyDatabase "contentmanager/library/tenancy/database"
//	"contentmanager/library/tenant/database"
//	"contentmanager/pkgs/config"
//	"contentmanager/scripts/apps/lib"
//	"log"
//	"runtime"
//	"sync"
//)
//
//var s3MigrationServiceName = "S3 Migration Service"
//
//func RunMigration(tenantInfo shared.TenantInfo, wg *sync.WaitGroup) {
//	log.Printf("----------DOCUMENT QUEUE SERVICE STARTED: %s----------: Thread %d ----------\n", tenantInfo.Name, runtime.NumGoroutine())
//	lib.RunDocumentMigration(tenantInfo)
//	lib.RunMediaMigration(tenantInfo)
//	log.Printf("----------DOCUMENT QUEUE SERVICE FINISHED: %s----------: Thread %d ----------\n", tenantInfo.Name, runtime.NumGoroutine())
//	defer wg.Done()
//}
//
//func main() {
//	runtime.GOMAXPROCS(runtime.NumCPU())
//	//tenancyServiceConfig := config.IParseConfigAdapter().ParseConfig()
//	//cache.ICacheAdapter().CacheObject(conf.TenancySiteUrlCachekey, tenancyServiceConfig.AdminSiteUrl, false)
//
//	//cache.ICacheAdapter().CacheObject(conf.TenancyConfigCacheKey, tenancyServiceConfig, false)
//	appConfig := config.GetAppConfig()
//	tenancyDatabase.ITenancyDBAdapter().CacheTenancyDatabaseConnection(*appConfig)
//	tenants := database.ITenantSessionAdapter().GetAllTenantInfo()
//	var wg sync.WaitGroup
//	wg.Add(len(tenants))
//	for _, tenantInfo := range tenants {
//		go RunMigration(tenantInfo, &wg)
//	}
//	wg.Wait()
//	log.Printf("Waiting for all go routines to finish\n")
//
//}
