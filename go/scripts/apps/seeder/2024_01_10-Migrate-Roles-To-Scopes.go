package main

import (
	commonModels "contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
	"log"
)

type Scopes struct {
	label    string
	pattern  string
	children []string
}

var AvailableScopes = struct {
	Pages          Scopes
	News           Scopes
	Events         Scopes
	Alerts         Scopes
	Transportation Scopes
	Media          Scopes
	Instagram      Scopes
	Navigation     Scopes
	Core           map[int][]string
}{
	Pages: Scopes{
		label:    "Pages Editor",
		pattern:  "cm.content.page/*",
		children: []string{},
	},
	News: Scopes{
		label:    "News Editor",
		pattern:  "cm.content.news/*",
		children: []string{},
	},
	Events: Scopes{
		label:    "Events Editor",
		pattern:  "cm.content.event/*",
		children: []string{},
	},
	Alerts: Scopes{
		label:    "Alerts Editor",
		pattern:  "cm.content.alert/*",
		children: []string{},
	},
	Transportation: Scopes{
		label:    "Transportation Admin",
		pattern:  "cm.transportation*",
		children: []string{"cm.transportation.bus_status/*"},
	},
	Media: Scopes{
		label:    "Images Editor",
		pattern:  "cm.image/*",
		children: []string{"cm.image/*", "cm.document*/*"},
	},
	Instagram: Scopes{
		label:    "Instagram Editor",
		pattern:  "cm.settings.instagram/*",
		children: []string{},
	},

	// New permission, but used to be tied to pages
	Navigation: Scopes{
		label:   "Navigation Editor",
		pattern: "cm.navigation*", /* Mod pages = 1 / 3 */
		children: []string{ /* Mod Pages = 2 */
			"cm.navigation*/create",
			"cm.navigation*/update",
			"cm.navigation*/delete",
		},
	},
	Core: map[int][]string{
		2:  []string{"cm.resource*/*"},
		4:  []string{"cm.resource*/*"},
		8:  []string{"cm.site*"},
		16: []string{"cm.tag/*"},
	},
}

func MigrateRolesToScopes(db *gorm.DB) error {
	var roles []commonModels.Role
	//var backup = []commonModels.Role{}
	if err := db.Find(&roles).Error; err != nil {
		return err
	}
	//for _, role := range roles {
	//	backup = append(backup, role)
	//}

	for _, role := range roles {
		if len(role.Scopes) > 0 {
			// If scopes have already been added manually, skip over.
			continue
		}
		var newScopes = map[string]bool{}
		var addChildren = func(s Scopes) {
			for _, action := range s.children {
				newScopes[action] = true
			}
		}

		if role.Pages > 0 {
			newScopes[AvailableScopes.Pages.pattern] = true
			if role.Pages == 2 {
				/* var PAGES_SITE = 2 */
				addChildren(AvailableScopes.Navigation)
			} else {
				/* PAGES_SHARED = 1 OR PAGES_SHARED + PAGES_SITE */
				newScopes[AvailableScopes.Navigation.pattern] = true
			}
		}
		if role.Transportation > 0 {
			if role.Transportation == 2 {
				/*VAR TRANSPORTATION_STATUS = 2*/
				addChildren(AvailableScopes.Transportation)
			} else {
				/*VAR TRANSPORTATION_ADMIN = 1*/
				newScopes[AvailableScopes.Transportation.pattern] = true
			}
		}
		if role.Events > 0 {
			newScopes[AvailableScopes.Events.pattern] = true
		}
		if role.News > 0 {
			newScopes[AvailableScopes.News.pattern] = true
		}
		if role.Alerts > 0 {
			newScopes[AvailableScopes.Alerts.pattern] = true
		}
		if role.Media > 0 {
			/* Media includes Documents & Images */
			addChildren(AvailableScopes.Media)
		}
		if role.Instagram > 0 {
			newScopes[AvailableScopes.Instagram.pattern] = true
		}
		if role.Core > 0 {
			for permissionLevel, actionForScope := range AvailableScopes.Core {
				if (permissionLevel & role.Core) == permissionLevel {
					for _, action := range actionForScope {
						newScopes[action] = true
					}
				}
			}
		}
		for scope, _ := range newScopes {
			role.Scopes = append(role.Scopes, scope)
		}
		log.Println(role.Scopes)
		if err := db.Save(&role).Error; err != nil {
			return err
		}
	}
	return nil
}
