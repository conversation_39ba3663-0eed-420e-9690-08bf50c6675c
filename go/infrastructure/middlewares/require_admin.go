package middlewares

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"net/http"
)

func RequireAdminMiddleware() httpService.Handler {
	return func(rw http.ResponseWriter, r *shared.AppContext, c httpService.Context) {
		if r == nil {
			http.Error(rw, "Unauthorized", http.StatusUnauthorized)
			return
		}

		if err := r.AccountWithGroups(); err != nil {
			http.Error(rw, "Unauthorized access", http.StatusUnauthorized)
			return
		}

		if !r.Account().IsAdmin {
			http.Error(rw, "Forbidden for non-admin user "+r.Account().Email+". The operation available only for admin users. ", http.StatusForbidden)
			return
		}

		c.Next()
	}
}
