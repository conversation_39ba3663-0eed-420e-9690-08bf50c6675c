package middlewares

import (
	"contentmanager/library/httpService"
	"net/http"
	"strings"
)

var redirects = []func(string) (string, bool){
	equalsTo("/search"),
	equalsTo("/documents"),
	startsWith("/documents/"),
	startsWith("/folder/"),
	startsWith("/images/"),
	startsWith("/thumbnails/"),
	startsWith("/go/"),
}

func CaseSensitivityRedirect() httpService.Handler {
	return func(w http.ResponseWriter, r *http.Request, ctx httpService.Context) {
		for _, redirect := range redirects {
			if u, ok := redirect(r.URL.Path); ok {
				if len(r.URL.RawQuery) > 0 {
					u += "?" + r.URL.RawQuery
				}
				http.Redirect(w, r, u, http.StatusMovedPermanently)
				return
			}
		}
		ctx.Next()
	}
}

func startsWith(substr string) func(s string) (string, bool) {
	return func(s string) (string, bool) {
		return starts(s, substr)
	}
}

func equalsTo(substr string) func(s string) (string, bool) {
	return func(s string) (string, bool) {
		return equals(s, substr)
	}

}

func starts(s, substr string) (string, bool) {
	if strings.HasPrefix(s, substr) {
		return "", false
	}
	if strings.HasPrefix(strings.ToLower(s), substr) {
		return substr + s[len(substr):], true
	}
	return "", false
}

func equals(s, substr string) (string, bool) {
	if s == substr {
		return "", false
	}
	if strings.ToLower(s) == substr {
		return substr, true
	}
	return "", false
}
