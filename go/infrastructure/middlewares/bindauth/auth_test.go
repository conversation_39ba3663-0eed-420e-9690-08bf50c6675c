package bindauth

import (
	"bytes"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	tenancyModels "contentmanager/library/tenancy/models"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/refxx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/tests"
	"context"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"io"
	"mime/multipart"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
)

var areaID = uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7")
var routeID = uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e8")
var siteWithPermissions = uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e9")
var siteNoPermissions = uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d01908710")
var siteDoesNotExists = uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d01908711")

var sites = []tenancyModels.Site{{
	BaseSite: tenancyModels.BaseSite{
		ID:     siteWithPermissions,
		Type:   "school",
		Active: true,
	},
}, {
	BaseSite: tenancyModels.BaseSite{
		ID:     siteNoPermissions,
		Type:   "school",
		Active: true,
	},
}}

func Test_Auth(t *testing.T) {
	// user don't have permissions for cm.transportation.bus_area
	ts, dispose := setup(t, getAccount(false, getGroup(nil, "cm.transportation.bus_route*"), getGroup(&siteWithPermissions, "cm.content.news*")))
	defer dispose()

	// should fail because user don't have permissions for cm.transportation.bus_area
	if resp, err := httpDelete(fmt.Sprintf("%s/binding/auth/test/%s/%s", ts.URL, areaID.String(), routeID.String())); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusForbidden {
		t.Fatalf("Expected status code %d, got %d", http.StatusForbidden, resp.StatusCode)
	}

	// still should fail because bus_area is not skipped
	if resp, err := httpDelete(fmt.Sprintf("%s/binding/auth/test-skip-route/%s/%s", ts.URL, areaID.String(), routeID.String())); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusForbidden {
		t.Fatalf("Expected status code %d, got %d", http.StatusForbidden, resp.StatusCode)
	}

	// should succeed because bus_area is skipped for authorization
	if resp, err := httpDelete(fmt.Sprintf("%s/binding/auth/test-skip-all/%s/%s", ts.URL, areaID.String(), routeID.String())); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	if resp, err := httpDelete(fmt.Sprintf("%s/binding/auth/test-skip-all-explicit/%s/%s", ts.URL, areaID.String(), routeID.String())); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	if resp, err := httpDelete(fmt.Sprintf("%s/binding/auth/test-skip-area/%s/%s", ts.URL, areaID.String(), routeID.String())); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	if resp, err := http.Post(fmt.Sprintf("%s/binding/auth/test-news-single", ts.URL), "application/json", strings.NewReader(fmt.Sprintf(`{"Sites":["%s"]}`, siteWithPermissions.String()))); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	if resp, err := http.Post(fmt.Sprintf("%s/binding/auth/test-news", ts.URL), "application/json", strings.NewReader(fmt.Sprintf(`[{"Sites":["%s"]}]`, siteWithPermissions.String()))); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusOK {
		t.Fatalf("Expected status code %d, got %d", http.StatusOK, resp.StatusCode)
	}

	if resp, err := http.Post(fmt.Sprintf("%s/binding/auth/test-news", ts.URL), "application/json", strings.NewReader(fmt.Sprintf(`[{"Sites":["%s"]}]`, siteNoPermissions.String()))); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusForbidden {
		t.Fatalf("Expected status code %d, got %d", http.StatusForbidden, resp.StatusCode)
	}

	if resp, err := http.Post(fmt.Sprintf("%s/binding/auth/test-news", ts.URL), "application/json", strings.NewReader(fmt.Sprintf(`[{"Sites":["%s"]}]`, siteDoesNotExists.String()))); err != nil {
		t.Fatalf("Failed to get: %s", err)
	} else if resp.StatusCode != http.StatusBadRequest {
		t.Fatalf("Expected status code %d, got %d", http.StatusBadRequest, resp.StatusCode)
	}

}

func Test_FromMultipartForm(t *testing.T) {
	ts, dispose := setup(t, getAccount(true))
	defer dispose()

	// Create a buffer to write our multipart form to
	var b bytes.Buffer
	writer := multipart.NewWriter(&b)

	// Add form fields
	writer.WriteField("Name", "John Doe")
	writer.WriteField("Email", "<EMAIL>")

	// Add single file
	avatarWriter, err := writer.CreateFormFile("Avatar", "avatar.jpg")
	if err != nil {
		t.Fatal(err)
	}
	io.WriteString(avatarWriter, "fake avatar content")

	// Add multiple files
	for i := 0; i < 2; i++ {
		docWriter, err := writer.CreateFormFile("Documents", fmt.Sprintf("doc%d.pdf", i))
		if err != nil {
			t.Fatal(err)
		}
		io.WriteString(docWriter, fmt.Sprintf("fake document content %d", i))
	}

	// Close the multipart writer
	writer.Close()

	// Create the request
	req, err := http.NewRequest("POST", ts.URL+"/binding/auth/upload", &b)
	if err != nil {
		t.Fatal(err)
	}

	// Set the content type
	req.Header.Set("Content-Type", writer.FormDataContentType())

	// Send the request
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		t.Fatal(err)
	}
	defer resp.Body.Close()

	// Check the response
	m := asMap(resp)
	fromMultipartForm := m["FromMultipartForm"].(map[string]interface{})

	if fromMultipartForm["Name"].(string) != "John Doe" {
		t.Errorf("Expected FromMultipartForm.Name to be John Doe, got %v", fromMultipartForm["Name"])
	}
	if fromMultipartForm["Email"].(string) != "<EMAIL>" {
		t.Errorf("Expected FromMultipartForm.Email <NAME_EMAIL>, got %v", fromMultipartForm["Email"])
	}

	avatar := fromMultipartForm["Avatar"].(map[string]interface{})
	if avatar["Filename"].(string) != "avatar.jpg" {
		t.Errorf("Expected Avatar filename to be avatar.jpg, got %v", avatar["Filename"])
	}

	documents := fromMultipartForm["Documents"].([]interface{})
	if len(documents) != 2 {
		t.Errorf("Expected 2 documents, got %d", len(documents))
	}
	for i, doc := range documents {
		docMap := doc.(map[string]interface{})
		expectedFilename := fmt.Sprintf("doc%d.pdf", i)
		if docMap["Filename"].(string) != expectedFilename {
			t.Errorf("Expected document filename to be %s, got %v", expectedFilename, docMap["Filename"])
		}
	}
}

type MockNews struct {
	Sites        []uuid.UUID
	DepartmentID *uuid.UUID
}

func (m MockNews) GetSites() []uuid.UUID {
	return m.Sites
}

func (m MockNews) GetDepartmentID() *uuid.UUID {
	return m.DepartmentID
}

func (m MockNews) GetScopeEntity() string {
	return "cm.content.news"
}

var _ auth.Secured = (*MockNews)(nil)

func setup(t *testing.T, account *identity.Account) (*httptest.Server, func()) {
	os.Setenv("DEBUG", "1")

	ctx := context.Background()
	// ctx := tests.InitLogging("Test_Auth")
	db, dispose := tests.InitTenantDB()

	if err := db.Save(&commonModels.BusArea{ID: areaID, Name: "issue", Active: true}).Error; err != nil {
		t.Fatalf("Failed to save bus area: %s", err)
	}

	if err := db.Save(&commonModels.BusRoute{ID: routeID, Areas: []uuid.UUID{areaID}, Name: "issue", Active: true}).Error; err != nil {
		t.Fatalf("Failed to save bus area: %s", err)
	}

	defaultMiddleware := httpService.DefaultLight()
	defaultMiddleware.Use(func(rw http.ResponseWriter, r *http.Request, c httpService.Context) {
		appCtx := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
			TenantDB: db.WithContext(ctx),
			Identity: account,
			Request:  r,
		})
		_ = refxx.SetPrivateFieldByName(appCtx, "sites", sites)
		c.Map(appCtx)
	})

	defaultMiddleware.Group("/binding/auth", func(r httpService.Router) {
		r.Delete("/test/:AreaID/:RouteID", func(w http.ResponseWriter, params struct {
			BindableParams
			FromPath struct {
				AreaID  uuid.UUID
				RouteID uuid.UUID
			}
			FromDBArea commonModels.BusArea  `query:"id = @FromPath.AreaID"`
			FromDBBus  commonModels.BusRoute `query:"id = @FromPath.RouteID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Delete("/test-skip-route/:AreaID/:RouteID", func(w http.ResponseWriter, params struct {
			BindableParams
			SkipAuth `skip:"FromDBBus"`
			FromPath struct {
				AreaID  uuid.UUID
				RouteID uuid.UUID
			}
			FromDBArea commonModels.BusArea  `query:"id = @FromPath.AreaID"`
			FromDBBus  commonModels.BusRoute `query:"id = @FromPath.RouteID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Delete("/test-skip-area/:AreaID/:RouteID", func(w http.ResponseWriter, params struct {
			BindableParams
			SkipAuth `skip:"FromDBArea"`
			FromPath struct {
				AreaID  uuid.UUID
				RouteID uuid.UUID
			}
			FromDBArea commonModels.BusArea  `query:"id = @FromPath.AreaID"`
			FromDBBus  commonModels.BusRoute `query:"id = @FromPath.RouteID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Delete("/test-skip-all/:AreaID/:RouteID", func(w http.ResponseWriter, params struct {
			BindableParams
			SkipAuth
			FromPath struct {
				AreaID  uuid.UUID
				RouteID uuid.UUID
			}
			FromDBArea commonModels.BusArea  `query:"id = @FromPath.AreaID"`
			FromDBBus  commonModels.BusRoute `query:"id = @FromPath.RouteID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Delete("/test-skip-all-explicit/:AreaID/:RouteID", func(w http.ResponseWriter, params struct {
			BindableParams
			SkipAuth `skip:"FromDBArea,FromDBBus"`
			FromPath struct {
				AreaID  uuid.UUID
				RouteID uuid.UUID
			}
			FromDBArea commonModels.BusArea  `query:"id = @FromPath.AreaID"`
			FromDBBus  commonModels.BusRoute `query:"id = @FromPath.RouteID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Post("/test-news", func(w http.ResponseWriter, params struct {
			BindableParams
			FromBody []MockNews
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Post("/test-news-single", func(w http.ResponseWriter, params struct {
			BindableParams
			FromBody MockNews
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Post("/upload", func(w http.ResponseWriter, params struct {
			BindableParams
			FromMultipartForm struct {
				Name      string
				Email     string
				Avatar    *multipart.FileHeader
				Documents []*multipart.FileHeader
			}
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

	}, BindParamsMiddleware(), AuthorizeMiddleware())

	// validate routes
	ValidateRoutes(defaultMiddleware)
	// Create a test server using httptest
	ts := httptest.NewServer(defaultMiddleware)

	return ts, func() {
		dispose()
		ts.Close()
	}
}

func httpDelete(url string) (*http.Response, error) {
	req, err := http.NewRequest(http.MethodDelete, url, nil)
	if err != nil {
		return nil, err
	}
	return http.DefaultClient.Do(req)
}

func getAccount(isAdmin bool, groups ...identity.SecurityGroup) *identity.Account {
	return &identity.Account{
		IsAdmin: isAdmin,
		Groups:  groups,
		Active:  true,
		Email:   "<EMAIL>",
	}
}

func getGroup(siteID *uuid.UUID, scopes ...string) identity.SecurityGroup {
	return identity.SecurityGroup{
		SiteID: siteID,
		Active: true,
		Role: commonModels.Role{
			Scopes: scopes,
			Active: true,
		},
	}
}
