package sharedDatabase

import (
	"contentmanager/etc/conf"
	"fmt"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
	"os"
	"time"
)

type ConnectionStringer interface {
	ConnectionString() string
}

func CreateDatabaseConnection(model ConnectionStringer) (*gorm.DB, error) {
	return CreateDatabaseConnectionByString(model.ConnectionString())
}
func CreateDatabaseConnectionByString(connectionString string) (*gorm.DB, error) {
	val := os.Getenv("DEBUG")
	debug := val == "1" || val == "true"

	db, err := gorm.Open(postgres.Open(connectionString), createGormConfig(debug))
	if err != nil {
		return nil, fmt.Errorf("Fatal error connecting to database \n error: [%s] \n connection: [%s]", err, connectionString)
	}
	sqlDb, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("Fatal error connecting to database \n error: [%s] \n connection: [%s]", err, connectionString)
	}
	sqlDb.SetMaxIdleConns(conf.DatabaseMaxIdleConnections)
	sqlDb.SetMaxOpenConns(conf.DatabaseMaxOpenConnections)
	sqlDb.SetConnMaxLifetime(conf.DatabaseMaxConnectionLifetime)

	return db, nil
}

func createGormConfig(debug bool) *gorm.Config {
	var ilogger logger.Interface
	if debug {
		logConfig := logger.Config{
			SlowThreshold:             time.Millisecond * 50,
			IgnoreRecordNotFoundError: false,
			LogLevel:                  logger.Info,
		}
		ilogger = New(logConfig)
	} else {
		logConfig := logger.Config{
			SlowThreshold:             time.Millisecond * 100,
			IgnoreRecordNotFoundError: false,
			LogLevel:                  logger.Warn,
		}
		ilogger = New(logConfig)
	}

	return &gorm.Config{
		Logger: ilogger,
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // use singular table name, table for `User` would be `user` with this option enabled
		},
	}
}
