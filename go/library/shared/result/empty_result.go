package result

import (
	"contentmanager/library/utils/converters"
	"net/http"
)

type EmptyResult struct {
	StatusCode   int
	Success      bool
	ErrorMessage *string
	error        error
}

var _ IResult = (*EmptyResult)(nil)

func (r EmptyResult) GetStatusCode() int {
	return r.StatusCode
}

func (r EmptyResult) GetData() interface{} {
	return nil
}

func (r EmptyResult) Unwrap() error {
	return r.error
}

func (r EmptyResult) IsError() bool {
	return r.error != nil
}

func (r EmptyResult) IsSuccess() bool {
	return r.error == nil
}

func ErrorEmpty(err error) EmptyResult {
	return EmptyResult{
		StatusCode:   http.StatusBadRequest,
		Success:      false,
		ErrorMessage: converters.AsPointer(err.Error()),
		error:        err,
	}
}

func SuccessEmpty() EmptyResult {
	return EmptyResult{
		StatusCode: http.StatusNoContent,
		Success:    true,
	}
}
