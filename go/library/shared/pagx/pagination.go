package pagx

type (
	Query struct {
		Page     int
		PageSize int
	}

	Paginated[T any] struct {
		TotalRecords int64
		TotalPages   int
		Offset       int
		PageSize     int
		Page         int
		Rows         []T
	}

	PaginatedData[T any] struct {
		TotalRecords int64
		TotalPages   int
		Offset       int
		PageSize     int
		Page         int
		Data         T
	}

	Paginator interface {
		GetPageSize() int
		GetPage() int
		GetOffset() int
	}

	PaginatedResult interface {
		Paginator
		GetTotalRecords() int64
		GetTotalPages() int
	}
)

func (p PaginatedData[T]) GetTotalRecords() int64 {
	return p.TotalRecords
}

func (p PaginatedData[T]) GetTotalPages() int {
	return p.TotalPages
}

var _ PaginatedResult = (*PaginatedData[any])(nil)

func (q Query) GetPageSize() int {
	//if q.PageSize <= 0 || q.PageSize > 100 {
	//	return 10
	//}
	if q.PageSize <= 0 {
		return 10
	}
	return q.PageSize
}

func (q Query) GetPageSizeWithLimit(limit int) int {
	if q.PageSize <= 0 || q.PageSize > limit {
		return limit
	}

	return q.PageSize
}

func (q Query) GetPage() int {
	if q.Page <= 0 {
		return 1
	}
	return q.Page
}

func (q Query) GetOffset() int {
	return (q.GetPage() - 1) * q.GetPageSize()
}

func (p PaginatedData[T]) GetPageSize() int {
	if p.PageSize <= 0 {
		return 10
	}
	return p.PageSize
}

func (p PaginatedData[T]) GetPage() int {
	if p.Page <= 0 {
		return 1
	}
	return p.Page
}

func (p PaginatedData[T]) GetOffset() int {
	return (p.GetPage() - 1) * p.GetPageSize()
}
