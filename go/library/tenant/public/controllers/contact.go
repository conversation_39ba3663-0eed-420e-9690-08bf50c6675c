package publicControllers

import (
	"contentmanager/etc/conf"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/pkgs/config"
	"contentmanager/pkgs/forms/email_validator"
	mailer2 "contentmanager/pkgs/notifications/mailer"
	"contentmanager/pkgs/notifications/mailer/app_mailer"
	"encoding/json"
	"fmt"
	"golang.org/x/crypto/bcrypt"
	"net/http"
)

type (
	ContactController struct{}
	ContactForm       struct {
		Name    string
		Phone   string
		Email   string
		Message string
	}
	ContactInformation struct {
		Settings json.RawMessage
	}
)

func (ctc ContactController) PublicContactPost(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	//Allow Anonymous
	// TODO => MT: Replace with `r.CurrentSite`
	//  Temporarily using SiteByID since methods are different between types.
	site, siteErr := r.SiteByID(r.CurrentSiteID())
	if siteErr != nil {
		r.Logger().Error().Err(siteErr).Msgf("Can't get site with ID: %s", r.CurrentSiteID().String())
		http.Error(w, fmt.Sprintf("Can't get site with ID: %s", r.CurrentSiteID().String()), http.StatusBadRequest)
		return
	}

	var settings = site.GetAllSettings()
	var emailToInf = settings["email"]

	if emailToInf == nil {
		m := fmt.Sprintf("No email profided for the site %s: %s", site.ID.String(), site.Name)
		r.Logger().Error().Msg(m)
		http.Error(w, m, http.StatusBadRequest)
		return
	}
	emailTo, ok := emailToInf.(string)
	if !ok {
		r.Logger().Error().Interface("email", emailToInf).Msg("Can't get Email")
		http.Error(w, fmt.Sprintf("Can't get site with ID: %s", r.CurrentSiteID().String()), http.StatusBadRequest)
		return
	}
	if !email_validator.IsValid(emailTo) {
		r.Logger().Error().Msgf("Invalid emailTo: %s", emailTo)
		http.Error(w, fmt.Sprintf("Can't send email. The site has incrorrect configuration. "), http.StatusBadRequest)
		return
	}

	emailFrom := r.Request().Form.Get("email")
	if !email_validator.IsValid(emailTo) {
		r.Logger().Error().Msgf("Invalid eamilFrom: %s", emailFrom)
		http.Error(w, fmt.Sprintf("Invalid eamilFrom: %s", emailFrom), http.StatusBadRequest)
		return
	}

	tokenErr := bcrypt.CompareHashAndPassword([]byte(r.Request().Form.Get("token")), []byte(conf.BcryptUUIDTemplate))
	if tokenErr != nil {
		m := fmt.Sprintf("Captcha is invalid")
		r.Logger().Error().Err(tokenErr).Msg(m)
		http.Error(w, m, http.StatusBadRequest)
		return
	}

	body := r.Request().Form.Get("message")
	body += "\r\n <p>Name:" + r.Request().Form.Get("name")
	body += "\r\n <p>Phone:" + r.Request().Form.Get("phone")
	body += "\r\n <p>Email:" + r.Request().Form.Get("email")

	mailer, err := app_mailer.NewAppMailer()
	if err != nil {
		r.Logger().Error().Err(err).Msg("Error creating mailer")
		http.Error(w, "Can't send email", http.StatusBadRequest)
		return
	}

	appConfig := config.GetAppConfig()
	r.Logger().Info().Msgf("Sending email to %s, using user: %s, and host: %s", emailTo, appConfig.MailUser, appConfig.MailHost)

	if err := mailer.SendHTMLWithReplyTo(mailer2.GenericMessage{
		To:      emailTo,
		Subject: "New Message from: " + emailFrom,
		HTML:    body,
		ReplyTo: emailFrom,
	}); err != nil {
		r.Logger().Error().Err(err).Msg("Error sending email")
		http.Error(w, "Can't send Email.", http.StatusBadRequest)
		return
	}

	http.Redirect(w, r.Request(), r.Request().Referer(), http.StatusMovedPermanently)
}
