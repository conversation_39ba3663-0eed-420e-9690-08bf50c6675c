package publicControllers

import (
	"bytes"
	"contentmanager/etc/conf"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/public/services"
	"contentmanager/library/utils"
	"encoding/base64"
	"errors"
	uuid "github.com/satori/go.uuid"
	"golang.org/x/crypto/bcrypt"
	"net/http"
	"net/url"
	"strings"
)

/*************************************************************************************************************
API Document Controller
*************************************************************************************************************/

type (
	DocumentController struct{}
	BulkDocuments      struct {
		Ids         []uuid.UUID `json:"ids"`
		Destination uuid.UUID   `json:"folderId"`
	}
	FolderViewModel struct {
		FolderFilename string
		FolderContents []commonModels.Document
		Parent         commonModels.Document
		Error          error
		CurrentTitle   string
	}
	DocumentResultSet struct {
		TotalRecords int `json:"total_records"`
		Offset       int `json:"offset"`
		Limit        int `json:"limit"`
	}
	DocumentResponse struct {
		Results   []commonModels.Document `json:"results"`
		ResultSet DocumentResultSet       `json:"resultset"`
	}
)

// -------------------- Public --------------------  //

func (dc DocumentController) RoutePublicDocument(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	//Allow Anonymous

	tenantDB := r.TenantDatabase()
	//Parse the HTML Form (including query params) For Reading
	r.Request().ParseForm()
	var id string
	if len(p["id"]) > 0 {
		id = p["id"]
	}
	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}
	document, err := publicServices.GetPublicDocumentById(tenantDB, id, r.CurrentSiteIDNullable(), r.PublicAccount().PrivacyLevel, false)

	if err != nil {
		if utils.IsErrorNotFound(err) {
			w.WriteHeader(http.StatusNotFound)
			publicServices.WriteNotFound(w, r)
		} else if errors.Is(err, utils.PermissionRequiredErrorMsg) {
			w.WriteHeader(http.StatusForbidden)
			publicServices.AccessDenied(w, r)
			return
		} else {
			http.Error(w, err.Error(), 500)
		}
		return
	}
	redirectRoute := "https://" + r.Request().Host + "/documents/" + document.ID.String() + "/" + url.PathEscape(document.Filename)
	http.Redirect(w, r.Request(), redirectRoute, http.StatusPermanentRedirect)
}

func (dc DocumentController) GetPublicFolder(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	//Allow Anonymous

	tenantDB := r.TenantDatabase()
	var id string
	var folder, folderParent commonModels.Document
	var folderContents []commonModels.Document
	var folderViewModel = FolderViewModel{Error: nil, CurrentTitle: "Documents"}
	var err error

	r.Request().ParseForm()
	if len(p["id"]) > 0 {
		id = p["id"]
	}

	if r.TenantDatabase() == nil {
		http.NotFound(w, r.Request())
		return
	}

	if len(id) > 0 {
		if folder, folderContents, folderParent, err = publicServices.GetPublicFolderContentsById(tenantDB, id, r.CurrentSiteIDNullable(), r.PublicAccount().PrivacyLevel); err == nil {
			folderViewModel.Parent = folderParent
			folderViewModel.FolderContents = folderContents
			folderViewModel.FolderFilename = folder.Filename
			folderViewModel.CurrentTitle = folder.Filename
		} else {
			publicServices.WriteNotFound(w, r)
			return
		}
	} else {
		if folderContents, err = publicServices.GetPublicRootLevelDocuments(tenantDB, r.CurrentSiteIDNullable(), r.PublicAccount().PrivacyLevel); err == nil {
			folderViewModel.FolderContents = folderContents
			folderViewModel.FolderFilename = "Documents"
		} else {
			publicServices.WriteNotFound(w, r)
			return
		}
	}

	results, resultsErr := publicServices.CompileTemplate(r, folderViewModel, publicServices.FolderTemplate)
	if resultsErr != nil {
		http.Error(w, "error aggregating search results", 400)
		return
	}

	token, _ := bcrypt.GenerateFromPassword([]byte(conf.BcryptUUIDTemplate), bcrypt.DefaultCost)
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Header().Set("X-CSRF-Token", string(token))
	w.Write([]byte(results))
}

// -------------------- Utility --------------------  //
func (dc DocumentController) DecodeB64(binary []byte) ([]byte, error) {
	str := bytes.NewBuffer(binary).String()
	if strings.Index(str, "base64,") == -1 {
		return []byte{}, errors.New("error decoding base64")
	}
	pdfStr := str[strings.Index(str, "base64,")+7:]
	imgBase64Decoded, _ := base64.StdEncoding.DecodeString(pdfStr)
	return imgBase64Decoded, nil
}
