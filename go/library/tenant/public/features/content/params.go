package publiccontent

import (
	"encoding/json"
	"errors"
	"github.com/satori/go.uuid"
	"strconv"
	"strings"
	"time"
)

type Type string

const (
	Alert        Type = "alert"
	ExternalLink Type = "external_link"
	Event        Type = "event"
	News         Type = "news"
	JS           Type = "js"
	CSS          Type = "css"
	Page         Type = "page"
	Template     Type = "template"
)

var allTypes = map[Type]struct{}{
	Alert:        {},
	ExternalLink: {},
	Event:        {},
	News:         {},
	JS:           {},
	CSS:          {},
	Page:         {},
	Template:     {},
}

type PublicationState int

const (
	IgnorePublication PublicationState = iota
	Published
	NotPublished
)

type ExpirationState int

const (
	IgnoreExpiration ExpirationState = iota
	NotExpired
	ExpiredOnly
)

type Params struct {
	Skip                 int
	Limit                int
	ContentType          []Type
	PublicationState     PublicationState
	ExpirationState      ExpirationState
	Search               string
	Template             string
	UsePriority          bool
	SiteId               uuid.UUID
	PrivacyLevel         int
	ConsiderStartDate    bool
	ConsiderEndDate      bool
	ConsiderUpcomingDate bool
	LastVisited          time.Time
	IncludeMedia         bool
	IncludeTags          bool
	OnlyWithDct          bool
	Classification       string
	AlertType            string
	OnlyTagged           bool
	Sortings             []Sort
	Filters              []Filter
	Editors              []uuid.UUID
	Tags                 []uuid.UUID
	DeletedAfter         time.Time
	OnlyEditable         bool
	IdsForDistributed    []uuid.UUID
	IdsForCommon         []uuid.UUID
	Imported             bool
	ImportSource         string
	IncludeAdminData     bool
	SiteOnly             bool
	StructureIDs         []uuid.UUID
}

type Option func(params *Params)

func (p Params) Validate() error {
	ee := &[]string{}
	if p.SiteId == uuid.Nil {
		*ee = append(*ee, "SiteId is required and can't be empty")
	}

	if p.Limit <= 0 {
		*ee = append(*ee, "PageSize (page size) can't be less or equal to 0")
	}

	if len(*ee) > 0 {
		return errors.New(strings.Join(*ee, "; "))
	}

	return nil
}

func (p Params) With(o Option) Params {
	o(&p)
	return p
}

func (p Params) WithTags() Params {
	p.IncludeTags = true
	return p
}

func (p Params) WithOnlyTagged() Params {
	p.OnlyTagged = true
	return p
}

func (p Params) WithSiteId(siteId uuid.UUID) Params {
	p.SiteId = siteId
	return p
}

func (p Params) WithClassification(c string) Params {
	p.Classification = c
	return p
}

func (p Params) WithImported(c bool) Params {
	p.Imported = c
	return p
}

func (p Params) WithImportSource(c string) Params {
	p.ImportSource = c
	return p
}

func (p Params) WithDctOnly() Params {
	p.OnlyWithDct = true
	return p
}

func (p Params) WithAllPages() Params {
	p.Skip = 0
	p.Limit = 9999
	return p
}

func (p Params) WithConsiderStartDate() Params {
	p.ConsiderStartDate = true
	return p
}

func (p Params) WithConsiderEndDate() Params {
	p.ConsiderEndDate = true
	return p
}

func (p Params) WithConsiderUpcomingDate() Params {
	p.ConsiderUpcomingDate = true
	return p
}

func (p Params) WithConsiderExpirationDate() Params {
	p.ExpirationState = NotExpired
	return p
}

func (p Params) WithAlertType(alertType string) Params {
	p.AlertType = alertType
	p.ContentType = []Type{Alert}
	return p
}

func (p Params) WithLastVisited(lastVisited time.Time) Params {
	p.LastVisited = lastVisited
	return p
}

func (p Params) WithPrivacyLevel(pl int) Params {
	p.PrivacyLevel = pl
	return p
}

func (p Params) WithContentTypes(tt ...Type) Params {
	ct := []Type{}
	for _, t := range tt {
		ct = append(ct, t)
	}
	p.ContentType = ct
	return p
}

func (p Params) WithSort(ss ...Sort) Params {
	for _, t := range ss {
		p.Sortings = append(p.Sortings, t)
	}
	return p
}

func (p Params) WithPublishedOnly() Params {
	p.PublicationState = Published
	return p
}

func (p Params) WithAdminData() Params {
	p.IncludeAdminData = true
	return p
}

//func (p Params) With() Params {}

func (p Params) HasAnyTypeOf(tt []Type) bool {
	if len(tt) == 0 {
		return false
	}
	if len(p.ContentType) == 0 { //query will return all content types
		return true
	}

	for _, pt := range p.ContentType {
		for _, t := range tt {
			if pt == t {
				return true
			}
		}
	}

	return false
}

func NewContentParams(options ...Option) *Params {
	p := &Params{}

	p.Limit = 10

	for _, o := range options {
		o(p)
	}

	return p
}

func ParamsFromMap(m map[string][]string) Params {
	contentType := getAll(m, "contentType")
	tags := getAll(m, "tags[]")
	filters := getAllFilters(m, "filters[]")
	sortings := getAllSortings(m, "sortings[]")
	authors := getAll(m, "editors[]")
	template := getFirst(m, "template")
	classification := getFirst(m, "classification")
	page := toInt(getFirst(m, "page"), 0)
	pageSize := toInt(getFirst(m, "limit"), 10)
	search := strings.ToLower(getFirst(m, "searchTerm"))
	alertType := strings.ToLower(getFirst(m, "alertType"))
	isPublished := toBool(getFirst(m, "isPublished"), false)
	ignorePublished := toBool(getFirst(m, "ignorePublished"), true)
	usePriority := toBool(getFirst(m, "usePriority"), false)
	onlyGetDct := toBool(getFirst(m, "onlyGetDct"), false)
	expiration := getFirst(m, "expiration")
	includeMedia := toBool(getFirst(m, "includeMedia"), false)
	includeTags := toBool(getFirst(m, "includeTags"), false)
	considerStartDate := toBool(getFirst(m, "considerStartDate"), false)
	considerEndDate := toBool(getFirst(m, "considerEndDate"), false)
	considerUpcomingDate := toBool(getFirst(m, "considerUpcomingDate"), false)
	onlyTagged := toBool(getFirst(m, "onlyTagged"), false)
	siteId := uuid.FromStringOrNil(getFirst(m, "siteId"))
	deleted := toDate(getFirst(m, "deleted"))
	siteOnly := toBool(getFirst(m, "siteOnly"), false)

	return Params{
		Skip:                 page,
		Limit:                pageSize,
		OnlyEditable:         toBool(getFirst(m, "editable"), false),
		ContentType:          ToContentType(contentType),
		PublicationState:     publicationStateFromBool(ignorePublished, isPublished),
		ExpirationState:      ToExpirationState(expiration),
		Search:               search,
		UsePriority:          usePriority,
		OnlyWithDct:          onlyGetDct,
		Classification:       classification,
		SiteId:               siteId,
		ConsiderStartDate:    considerStartDate,
		ConsiderEndDate:      considerEndDate,
		ConsiderUpcomingDate: considerUpcomingDate,
		IncludeMedia:         includeMedia,
		IncludeTags:          includeTags,
		AlertType:            alertType,
		OnlyTagged:           onlyTagged,
		Sortings:             sortings,
		Filters:              filters,
		Tags:                 toUUIDArr(tags),
		Editors:              toUUIDArr(authors),
		Template:             template,
		DeletedAfter:         deleted,
		IncludeAdminData:     toBool(getFirst(m, "includeAdminData"), false),
		SiteOnly:             siteOnly,
		// IncludeNavigationRelatedResults: !ignoreNavigation,
		// PrivacyLevel:                    privacyLevel,
		// LastVisited:                   time.Time{},

	}
}

func getFirst(m map[string][]string, key string) string {
	if v, ok := m[key]; ok {
		return v[0]
	}
	return ""
}

func getAll(m map[string][]string, key string) []string {
	if v, ok := m[key]; ok {
		return v
	}
	return []string{}
}

type Filter struct {
	ColumnField   string `json:"field"`
	OperatorValue string `json:"operatorValue"`
	Value         string `json:"value"`
}

func getAllFilters(m map[string][]string, key string) []Filter {
	res := []Filter{}

	if ss, ok := m[key]; ok {
		for _, s := range ss {
			var f Filter
			e := json.Unmarshal([]byte(s), &f)
			if e != nil {
				continue
			}
			if len(f.Value) == 0 {
				continue
			}
			res = append(res, f)
		}
	}
	return res
}

type Sort struct {
	Field string `json:"field"`
	Order string `json:"sort"`
}

func getAllSortings(m map[string][]string, key string) []Sort {
	res := []Sort{}
	if ss, ok := m[key]; ok {
		for _, s := range ss {
			var sort Sort
			e := json.Unmarshal([]byte(s), &sort)
			if e != nil {
				continue
			}
			res = append(res, sort)
		}
	}
	return res
}

func toBool(s string, def bool) bool {
	switch strings.ToUpper(s) {
	case "1", "T", "TRUE":
		return true
	case "0", "F", "FALSE":
		return false
	}
	return def
}

func toUUIDArr(ss []string) []uuid.UUID {
	res := []uuid.UUID{}
	for _, s := range ss {
		u := uuid.FromStringOrNil(s)
		if u == uuid.Nil {
			continue
		}
		res = append(res, u)
	}
	return res
}

func toInt(s string, def int) int {
	if i, e := strconv.Atoi(s); e == nil {
		return i
	}
	return def
}

func toDate(s string) time.Time {
	if len(s) == 0 {
		return time.Time{}
	}

	t, e := time.Parse("2006-01-02", s)
	if e != nil {
		return time.Time{}
	}
	return t
}

func publicationStateFromBool(ignorePublished bool, isPublished bool) PublicationState {
	var publicationState PublicationState
	if ignorePublished {
		publicationState = IgnorePublication
	} else if isPublished {
		publicationState = Published
	} else {
		publicationState = NotPublished
	}
	return publicationState
}

func ToExpirationState(s string) ExpirationState {
	switch strings.ToUpper(s) {
	case "ALL":
		return IgnoreExpiration
	case "EXPIRED":
		return ExpiredOnly
	case "ACTIVE":
		return NotExpired
	}

	return IgnoreExpiration
}

func ToContentType(ss []string) []Type {
	var tt []Type
	for _, s := range ss {
		t := Type(s)
		if _, ok := allTypes[t]; ok {
			tt = append(tt, t)
		}
	}
	return tt
}

// Initialize required params initialization
// page parameter starts with 1
func Initialize(siteId uuid.UUID, page int, pageSize int) func(p *Params) {
	return func(p *Params) {
		p.SiteId = siteId
		p.Limit = pageSize
		p.Skip = (page - 1) * p.Limit
	}
}

func WithPrivacyLevel(level int) func(p *Params) {
	return func(p *Params) {
		p.PrivacyLevel = level
	}
}

func WithType(ct Type) func(p *Params) {
	return func(p *Params) {
		p.ContentType = []Type{ct}
	}
}

func ForSite(siteId uuid.UUID) func(p *Params) {
	return func(p *Params) {
		p.SiteId = siteId
	}
}

func ForNews(ps PublicationState, es ExpirationState, usePriority bool) func(p *Params) {
	return func(p *Params) {
		p.ContentType = []Type{News}
		p.PublicationState = ps
		p.ExpirationState = es
		p.UsePriority = usePriority
	}
}

func ForEvents(ps PublicationState, es ExpirationState) func(p *Params) {
	return func(p *Params) {
		p.ContentType = []Type{Event}
		p.PublicationState = ps
		p.ExpirationState = es
	}
}

//func WithNavigationRelatedResults(p *Params) {
//	p.IncludeNavigationRelatedResults = true
//}

func WithTags(p *Params) {
	p.IncludeTags = true
}

func WithMedia(p *Params) {
	p.IncludeMedia = true
}
