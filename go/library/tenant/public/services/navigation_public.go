package publicServices

import (
	"contentmanager/infrastructure/database/pgxx"
	publicModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils"
	"contentmanager/library/utils/ltree"
	"contentmanager/library/utils/slicexx"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"sort"
	"time"
)

type NavigationContext struct {
	Context publicModels.ContentForHandlebarsChain
	Parent  publicModels.ContentForHandlebars
	Self    *publicModels.ContentForHandlebars
}

func NewNavigationContext() NavigationContext {
	return NavigationContext{
		Context: publicModels.ContentForHandlebarsChain{},
		Parent:  publicModels.ContentForHandlebars{Title: "Home", Route: "/"},
		Self:    nil,
	}
}

func findSelf(tree []publicModels.ContentForHandlebars, self publicModels.ContentForHandlebars) NavigationContext {
	context := NewNavigationContext()
	context, _ = findChild(context.Parent, tree, self.ID)
	return context
}

// TODO => parent will be incorrect when traversing upwards and hitting a 'department wrapper' type navigation node
//
//	Could do what we do with children in findSelf to replace the node with real children
func findChild(origin publicModels.ContentForHandlebars, searchContext []publicModels.ContentForHandlebars, selfId uuid.UUID) (NavigationContext, bool) {
	context := NewNavigationContext()
	for i, search := range searchContext {
		if search.ID == selfId {
			context.Context = searchContext
			context.Self = &searchContext[i]
			if origin.IsValid() {
				context.Parent = origin
			}
			return context, true
		}
		if len(search.Children) == 0 {
			continue
		}
		if found, ok := findChild(search, search.Children, selfId); ok {
			_, isValid := utils.ValidateExternalURL(found.Parent.Route)
			if found.Parent.IsNavigationGroup || found.Parent.DepartmentId.UUID != uuid.Nil || !isValid {
				found.Parent = origin
			}
			return found, ok
		}
	}
	return context, false
}

// GetSiteNavigation
// TODO => Potentially implement the preview / allow-drafts functionality
func GetSiteNavigation(dbCon *gorm.DB, siteId uuid.UUID, privacyLevel int /*, allowDrafts bool */) []publicModels.ContentForHandlebars {

	//if cached := cache.ICacheAdapter().GetObject("nav-" + siteId.String()); cached != nil {
	//	if ts, ok := cached.([]publicModels.ContentForHandlebars); ok {
	//		return ts, map[string]*publicModels.ContentForHandlebars{}
	//	}
	//}
	publishedSubquery := pgxx.PublishPeriod(time.Now().UTC(), "c.", false)

	consistentSelect := "select n.path as navigation_path, n.department_id, n.tree_index, n.inline_children, nlevel(n.path) as level, c.id, c.type, c.title, c.route, c.media_id, c.settings, c.meta, c.publish_at, c.expire_at, (c.settings->>'isNavigationGroup')::bool as is_navigation_group "
	var contentChain = make([]publicModels.ContentForHandlebars, 0)
	dbCon.Raw(consistentSelect+", (c.settings->>'isPrimary')::bool as is_primary "+
		"from navigation n "+
		"join content c on c.id = n.content_id "+
		"where n.site_id = ? "+
		"and ? = any(c.sites) "+
		"and n.active = true "+
		"and c.active = true "+
		"and "+publishedSubquery+" "+
		"and ((c.privacy_level & ?) = c.privacy_level OR c.privacy_level = 0) "+
		"order by nlevel(n.path), tree_index desc "+
		"", siteId, siteId, privacyLevel).
		Scan(&contentChain)

	for i, ref := range contentChain {
		if ref.DepartmentId.Valid && ref.DepartmentId.UUID != uuid.Nil {
			var departmentContent = make([]publicModels.ContentForHandlebars, 0)
			dbCon.Raw(consistentSelect+
				"from navigation n "+
				"join content c on c.id = n.content_id "+
				"where n.site_id = ? "+
				"and n.active = true "+
				"and ? = any(c.sites) "+
				"and c.active = true "+
				"and "+publishedSubquery+" "+
				"and ((c.privacy_level & ?) = c.privacy_level OR c.privacy_level = 0) "+
				"order by nlevel(n.path), tree_index desc "+
				"", ref.DepartmentId.UUID, siteId, privacyLevel).
				Scan(&departmentContent)

			chain := nestNavigation(departmentContent)
			contentChain[i].Children = chain
			if len(chain) > 0 {
				contentChain[i].Route = chain[0].Route
			}
		}
	}
	contentChain = nestNavigation(contentChain)
	contentChain = traverse(contentChain)

	// TODO => Short term cache
	//  CM-2097
	// cache.ICacheAdapter().CacheObjectForDuration("nav-"+siteId.String(), contentChain, 1*time.Minute)
	return contentChain
}

func traverse(nn []publicModels.ContentForHandlebars) []publicModels.ContentForHandlebars {
	for i, n := range nn {
		if len(n.Children) > 0 {
			nn[i].Children = traverse(n.Children)
		}
	}
	nn = processInline(nn)
	return nn
}

func processInline(nn []publicModels.ContentForHandlebars) []publicModels.ContentForHandlebars {
	res := make([]publicModels.ContentForHandlebars, 0, len(nn))
	for _, n := range nn {
		if n.InlineChildren {
			if n.IsPrimary {
				for i := 0; i < len(n.Children); i++ {
					n.Children[i].IsPrimary = n.IsPrimary
				}
			}
			res = append(res, n.Children...)
		} else {
			res = append(res, n)
		}
	}
	return res
}

// nestNavigation
// expects contentChain to be sorted by nlevel asc and tree_index desc
// iterates through the chain in reverse order to build its hierarchy structure
func nestNavigation(contentChain []publicModels.ContentForHandlebars) []publicModels.ContentForHandlebars {
	var contentMap = make(map[string]publicModels.ContentForHandlebars, 0)
	if len(contentChain) == 0 {
		return contentChain
	}
	for _, v := range contentChain {
		contentMap[v.NavigationPath] = v
	}
	for i := len(contentChain) - 1; i != 0; i-- {
		obj := contentMap[contentChain[i].NavigationPath]
		ancestor := ltree.MustAncestor(obj.NavigationPath)
		if parent, ok := contentMap[ancestor]; ok && ltree.Nlevel(obj.NavigationPath) > 1 {
			parent.Children = append(parent.Children, obj)
			contentMap[ancestor] = parent
			delete(contentMap, obj.NavigationPath)
		}
	}
	contentChain = []publicModels.ContentForHandlebars{}
	for _, ref := range contentMap {
		if ltree.Nlevel(ref.NavigationPath) != 1 {
			continue
		}
		contentChain = append(contentChain, ref)
	}
	sort.Slice(contentChain, func(i, j int) bool {
		return contentChain[i].TreeIndex < contentChain[j].TreeIndex
	})
	return contentChain
}

func findPrimaryNavigation(navigationContent []publicModels.ContentForHandlebars) publicModels.HandlebarsPrimaryNavigationChain {
	var primaryNavigation = make(publicModels.HandlebarsPrimaryNavigationChain, 0)
	primaryContent := slicexx.Filter[publicModels.ContentForHandlebars](navigationContent, func(c publicModels.ContentForHandlebars) bool {
		return c.IsPrimary
	})
	for _, ref := range primaryContent {
		primaryNavigation = append(primaryNavigation, publicModels.HandlebarsPrimaryNavigation{ContentForHandlebars: ref})
	}
	return primaryNavigation
}
