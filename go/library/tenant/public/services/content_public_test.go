package publicServices

//func BenchmarkGetContentResults(b *testing.B) {
//	tenancyServiceConfig := shared.IParseConfigAdapter().ParseConfig("Public Website Server")
//	cache.ICacheAdapter().CacheObject(conf.TenancySiteUrlCachekey, tenancyServiceConfig.AdminSiteUrl, false)
//	cache.ICacheAdapter().CacheObject(conf.TenancyConfigCacheKey, tenancyServiceConfig, false)
//	tenancyDatabase.ITenancyDBAdapter().CacheTenancyDatabaseConnection(tenancyServiceConfig)
//
//	tenantID := uuid.FromStringOrNil("eab04800-b8c8-460e-a002-ea27b7263406")
//	dbQuery := database.ITenantDBAdapter().GetTenantDatabaseByTenantID(tenantID)
//	requestURL := url.URL{
//		Path: "/",
//	}
//	request := http.Request{
//		URL:  &requestURL,
//		Host: "cm-iesd.imagineeverything.com",
//	}
//	siteID := uuid.FromStringOrNil("eb526f14-e71f-48e8-8933-d3de9f93aeab")
//	account, err := adminServices.GetAccountById(dbQuery, uuid.FromStringOrNil("79678a43-9fda-4450-8ff0-7ee648f33e75"), true)
//	if err != nil {
//		log.Println("Failed to get account necessary for benchmark test")
//		return
//	}
//	req := &shared.Request{
//		Request: &request,
//		Session: &shared.TenantInfo{
//			Id:         tenantID,
//			Name:       "",
//			Host:       request.Host,
//			Port:       "",
//			Server:     "",
//			Dbuser:     "",
//			Dbpassword: "",
//			Settings:   nil,
//			SiteId: uuid.NullUUID{
//				UUID:  siteID,
//				Valid: true,
//			},
//			DomainId: uuid.NullUUID{},
//		},
//		Database: dbQuery,
//		Account:  &account,
//	}
//	for i := 0; i < b.N; i++ {
//		IPublicContentAdapter().GetContentResults(req)
//	}
//}
