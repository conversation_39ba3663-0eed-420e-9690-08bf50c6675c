<section id="cm-documents-view" class="cm-documents">
	<div class="container">
        <div class="parent-folder">
        {{#if Parent.Active}}
			<a href="/folder/{{#uuidToString Parent.ID}}{{/uuidToString}}">{{Parent.Filename}}</a>
        {{else}}
            <a href="/documents">Documents</a>
        {{/if}}
        </div>

		<h1>{{FolderFilename}}</h1>

        <div class="cm-documents-directory">
            <div class="row">
		        {{#each FolderContents}}
					{{#equal Type "folder"}}
					<div class="folder col-xs-6 col-sm-4 col-lg-3">
						<a href="/folder/{{#uuidToString ID}}{{/uuidToString}}">
                            <span class="material-icons">folder</span>
							<span class="folder-title">{{Filename}}</span>
						</a>
					</div>
					{{/equal}}

					{{#equal Type "document"}}
					<div class="file col-xs-6 col-sm-4 col-lg-3">
						<a href="/documents/{{#uuidToString ID}}{{/uuidToString}}" target="_blank">
						    <span class="material-icons">picture_as_pdf</span>
							<span class="document-title">{{Filename}}</span>
						</a>
					</div>
					{{/equal}}
		        {{/each}}
		    </div>
		</div>
	</div>
</section>