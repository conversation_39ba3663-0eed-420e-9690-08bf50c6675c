package utils

import (
	"net/url"
	"strings"
)

var (
	validSchemes = map[string]bool{
		"http":   true,
		"https":  true,
		"mailto": true,
		"tel":    true,
		"webcal": true,
	}
)

// ValidateExternalURL validates if provided urlString conforms to External Link URL format.
// if a non-relative urlString is provided without a URI scheme, `https` is automatically added to the returned *url.URL
func ValidateExternalURL(urlString string) (*url.URL, bool) {
	lenientParse, err := url.Parse(urlString)
	if err != nil {
		return nil, false
	}
	if strings.HasPrefix(urlString, "/") {
		return lenientParse, true
	}
	if lenientParse.Host == "" && lenientParse.Path == "" && lenientParse.Opaque == "" {
		return nil, false
	}
	if len(lenientParse.Scheme) > 0 {
		if _, ok := validSchemes[lenientParse.Scheme]; !ok {
			return nil, false
		}
	} else {
		urlString = "https://" + urlString
	}
	value, err := url.ParseRequestURI(urlString)
	return value, err == nil
}
