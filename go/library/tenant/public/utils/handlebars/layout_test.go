package handlebars

import "testing"

func Test_Layout(t *testing.T) {
	RegisterLayout("layout", `<h1>{{templateContent}}</h1>`)
	RegisterLayout("layout2", `{{!Layout=layout}}<div>{{templateContent}}</div>`)
	template := `{{!Layout=layout2}} Hello World!`
	applied := applyLayout(template)
	if applied != `<h1><div> Hello World!</div></h1>` {
		t.<PERSON><PERSON><PERSON>("Expected: %s, got: %s", `<h1><div> Hello World!</div></h1>`, template)
	}
}

func Test_PanicsIfLayoutCircular(t *testing.T) {
	defer func() { _ = recover() }()
	// layout2 uses layout, layout uses layout2
	RegisterLayout("layout", `{{!Layout=layout2}}<h1>{{templateContent}}</h1>`)
	RegisterLayout("layout2", `{{!Layout=layout}}<div>{{templateContent}}</div>`)
	template := `{{!Layout=layout2}} Hello World!`
	_ = applyLayout(template)

	t.<PERSON><PERSON><PERSON>("did not panic")
}

func Test_PanicsIfNoPlaceholder(t *testing.T) {
	defer func() { _ = recover() }()
	// layout does not contain {{templateContent}}
	RegisterLayout("layout", `<h1> </h1>`)
	RegisterLayout("layout2", `{{!Layout=layout}}<div>{{templateContent}}</div>`)
	template := `{{!Layout=layout2}} Hello World!`
	_ = applyLayout(template)

	t.Errorf("did not panic")
}
