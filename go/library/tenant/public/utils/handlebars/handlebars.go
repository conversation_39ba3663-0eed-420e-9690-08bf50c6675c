package handlebars

// <PERSON><PERSON> parses a template and evaluates it with given context
//
// Note that this function call is not optimal as your template is parsed everytime you call it. You should use Parse() function instead.
//func <PERSON>der(source string, ctx interface{}) (string, error) {
//	// parse template
//	tpl, err := Parse(source)
//	if err != nil {
//		return "", err
//	}
//
//	// renders template
//	str, err := tpl.Exec(ctx)
//	if err != nil {
//		return "", err
//	}
//
//	return str, nil
//}

// <PERSON><PERSON><PERSON> parses a template and evaluates it with given context. It panics on error.
//
// Note that this function call is not optimal as your template is parsed everytime you call it. You should use Parse() function instead.
//func MustRender(source string, ctx interface{}) string {
//	return MustParse(source).MustExec(ctx)
//}
