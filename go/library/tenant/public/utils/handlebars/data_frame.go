package handlebars

import (
	"contentmanager/library/shared"
	"contentmanager/library/templates/hbs_helpers"
	shared2 "contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/logging"
	"github.com/rs/zerolog"
	"log"
	"reflect"
)

// Handlebar variables from : http://handlebarsjs.com/block_helpers.html
type DataFrame struct {
	parent  *DataFrame
	data    map[string]interface{}
	helpers shared2.IHbsHelpers
}

func NewDataFrameWithHelpersFromRequest(request *shared.AppContext) *DataFrame {
	helpers := hbsHelpers.NewHbsHelpers(request)
	return NewDataFrameWithHelpers(helpers)
}

func NewDataFrameWithHelpers(helpers shared2.IHbsHelpers) *DataFrame {
	data := make(map[string]interface{})
	if req, err := helpers.RequestContext(); err == nil {
		form := req.Request().Form
		for k, v := range form {
			data["$"+k] = v
		}
	}

	return &DataFrame{
		data:    data,
		helpers: helpers,
	}
}

func NewDataFrame() *DataFrame {
	return &DataFrame{
		data: make(map[string]interface{}),
	}
}

func (p *DataFrame) Copy() *DataFrame {
	result := NewDataFrame()

	for k, v := range p.data {
		result.data[k] = v
	}

	result.parent = p

	return result
}

func (p *DataFrame) newIterDataFrame(length int, i int, key interface{}) *DataFrame {
	result := p.Copy()

	result.Set("index", i)
	result.Set("key", key)
	result.Set("first", i == 0)
	result.Set("last", i == length-1)

	return result
}

func (p *DataFrame) GetLogger() *zerolog.Logger {
	root := p.GetRoot()
	if root.helpers == nil {
		return logging.RootLogger()
	}
	if r, err := root.helpers.RequestContext(); err != nil {
		return logging.RootLogger()
	} else {
		if r.Request() == nil {
			return logging.RootLogger()
		}
		return logging.FromContext(r.Request().Context())
	}
}

func (p *DataFrame) GetHelpers() shared2.IHbsHelpers {
	root := p.GetRoot()
	if root.helpers == nil {
		log.Print("No helpers provided")
	}
	return root.helpers
}

func (p *DataFrame) Set(key string, val interface{}) {
	p.data[key] = val
}

func (p *DataFrame) GetRoot() *DataFrame {
	root := p
	for {
		if root.parent == nil {
			return root
		}
		root = root.parent
	}
}

func (p *DataFrame) SetInRoot(key string, val interface{}) {
	root := p.GetRoot()
	root.data[key] = val
}

// Get gets a data value.
func (p *DataFrame) Get(key string) interface{} {
	return p.find([]string{key})
}

func (p *DataFrame) find(parts []string) interface{} {
	data := p.data

	for i, part := range parts {
		val := data[part]
		if val == nil {
			return nil
		}

		if i == len(parts)-1 {
			// found
			return val
		}

		valValue := reflect.ValueOf(val)
		if valValue.Kind() != reflect.Map {
			// not found
			return nil
		}

		// continue
		data = mapStringInterface(valValue)
	}

	// not found
	return nil
}

func mapStringInterface(value reflect.Value) map[string]interface{} {
	result := make(map[string]interface{})

	for _, key := range value.MapKeys() {
		result[strValue(key)] = value.MapIndex(key).Interface()
	}

	return result
}
