package publicDataaccess

import (
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/tenant/public/models"
	"github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// TODO: Convert Raw SQL to GORM => Current implementation omits certain results due to innerSelect
func GetPublicCombinedBusStatusData(dbCon *gorm.DB) ([]publicModels.BusRouteAndStatus, error) {
	var statusChain = make([]publicModels.BusRouteAndStatus, 0)
	dbQuery := dbCon

	if err := dbQuery.
		Raw("SELECT bus_route.*, " +
			"bus_status.type, bus_status.description, bus_status.created " +
			"FROM bus_route " +
			"INNER JOIN bus_status " +
			"on bus_route.id = ANY(bus_status.routes) " +
			"WHERE bus_route.active = true " +
			"AND bus_status.active = true " +
			"AND enddate >= now() OR enddate IS NULL " +
			"AND bus_status.startdate < now() " +
			"AND bus_status.startdate=(select max(startdate) from bus_status where bus_route.id = any(bus_status.routes))").
		Scan(&statusChain).Error; err != nil {
		return []publicModels.BusRouteAndStatus{}, err
	}

	//dbQuery.
	//	Table("bus_route").
	//	Select("bus_route.*, bus_status.type, bus_status.description, bus_status.created").
	//	Joins(" INNER JOIN bus_status on bus_route.id = ANY(bus_status.routes)").
	//	Where(" bus_route.active = true").
	//	Where(" bus_status.active = true").
	//	Where(" enddate >= now() ").
	//	Or(" enddate IS NULL ").
	//	Where(" bus_status.startdate = (select max(startdate) from bus_status where bus_route.id = any(bus_status.routes)) ").
	//	Scan(&statusChain)

	return statusChain, nil
}

func GetPublicCombinedBusStatusDataBySite(dbCon *gorm.DB, siteId uuid.NullUUID) ([]publicModels.BusRouteAndStatus, error) {
	var statusChain = make([]publicModels.BusRouteAndStatus, 0)
	dbQuery := dbCon

	//dbQuery.
	//	Table("bus_route").
	//	Select("bus_route.*, bus_status.type, bus_status.description, bus_status.created").
	//	Joins(" INNER JOIN bus_status on bus_route.id = ANY(bus_status.routes) AND ? = any (bus_route.sites)", siteId).
	//	Where(" bus_route.active = true").
	//	Where(" bus_status.active = true").
	//	Where(" bus_status.startdate = (select max(startdate) from bus_status where bus_route.id = any(bus_status.routes)) ").
	//	Where(" enddate >= now() ").
	//	Or(" enddate IS NULL ").
	//	Scan(&statusChain)

	if err := dbQuery.
		Raw("SELECT bus_route.*, "+
			"bus_status.type, bus_status.description, bus_status.created "+
			"FROM bus_route "+
			"INNER JOIN bus_status "+
			"on bus_route.id = ANY(bus_status.routes) AND ? = any(bus_route.sites) "+
			"WHERE bus_route.active = true "+
			"AND bus_status.active = true "+
			"AND enddate >= now() OR enddate IS NULL "+
			"AND bus_status.startdate=(select max(startdate) from bus_status where bus_route.id = any(bus_status.routes))", siteId).
		Scan(&statusChain).Error; err != nil {
		return []publicModels.BusRouteAndStatus{}, err
	}

	return statusChain, nil
}

func GetPublicBusRouteData(dbCon *gorm.DB) ([]commonModels.BusRoute, error) {
	var routeChain = make([]commonModels.BusRoute, 0)
	tenantDB := dbCon

	if err := tenantDB.
		Where(" active = true ").
		Where(" array_length(areas, 1) > 0 ").
		Order(" Name asc ").
		Find(&routeChain).
		Error; err != nil {
		return []commonModels.BusRoute{}, err
	}

	return routeChain, nil
}

func GetPublicBusAreaDataBySite(dbCon *gorm.DB, siteId uuid.NullUUID) ([]commonModels.BusArea, error) {
	var areaChain = make([]commonModels.BusArea, 0)
	dbQuery := dbCon

	if err := dbQuery.
		Raw("SELECT DISTINCT bus_area.id, bus_area.name, bus_area.active "+
			"FROM bus_area "+
			"INNER JOIN bus_route on bus_area.id = any(bus_route.areas) "+
			"WHERE ? = any(bus_route.sites) "+
			"AND bus_area.active = true ", siteId).
		Scan(&areaChain).Error; err != nil {
		return []commonModels.BusArea{}, err
	}

	return areaChain, nil
}
