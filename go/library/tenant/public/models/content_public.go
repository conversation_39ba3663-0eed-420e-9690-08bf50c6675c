package publicModels

import (
	"contentmanager/library/shared"
)

type HandlebarsPrimaryNavigation struct {
	ContentForHandlebars
	//ID             uuid.UUID                        `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	//Title          string                           `json:"title"`
	//Route          string                           `json:"route"`
	//Status         string                           `json:"status" gorm:"->"`
	//MediaID        uuid.NullUUID                    `json:"mediaId" gorm:"column:media_id;type:uuid"`
	//NavigationPath string                           `json:"navigationPath" gorm:"navigation_path"`
	//DepartmentID   uuid.NullUUID                    `json:"departmentId" gorm:"column:department_id"`
	//Children       HandlebarsPrimaryNavigationChain `json:"children" gorm:"-"`
}
type HandlebarsPrimaryNavigationChain []HandlebarsPrimaryNavigation

func (hpn HandlebarsPrimaryNavigation) TableName() string {
	return "content"
}
func (hpn HandlebarsPrimaryNavigation) GetKey() string {
	return hpn.ID.String()
}

var _ shared.IHaveKey = (*HandlebarsPrimaryNavigation)(nil)
