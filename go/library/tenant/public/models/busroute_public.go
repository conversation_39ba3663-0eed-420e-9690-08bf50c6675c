package publicModels

import (
	"contentmanager/library/tenant/common/models"
	"time"
)

type BusRouteAndStatus struct {
	commonModels.BusRoute
	Type        commonModels.StatusType `json:"type" gorm:"column:type;type:status_type;DEFAULT:'on_time';NOT NULL"`
	Description string                  `json:"description" gorm:"column:description;type:character varying(64)"`
	Created     time.Time               `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
}
