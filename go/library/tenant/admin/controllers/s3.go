package adminControllers

import (
	"contentmanager/etc/conf"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/services"
	"contentmanager/library/utils"
	"contentmanager/pkgs/config"
	"encoding/json"
	"fmt"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	_ "github.com/joho/godotenv/autoload"
	uuid "github.com/satori/go.uuid"
	"io/ioutil"
	"net/http"
	"strings"
)

type (
	S3Controller struct{}
	S3Media      struct {
		ID        uuid.UUID
		Thumbnail string
		Data      string
	}
)

func (sThree S3Controller) GetAdminS3(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	tenantDB := r.TenantDatabase()
	paramsID := r.Request().URL.Query().Get("id")
	paramsSize := r.Request().URL.Query().Get("size")
	if len(paramsSize) == 0 {
		paramsSize = "thumbnail"
	}
	if _, err := adminServices.GetMediaById(tenantDB, uuid.FromStringOrNil(paramsID), false); err != nil {
		if _, err := adminServices.GetDocumentById(tenantDB, uuid.FromStringOrNil(paramsID), false); err != nil {
			utils.ResponseJson(w, utils.Message("not found"), http.StatusNotFound)
			return
		}
	}

	bytes, err := commonServices.Download(r.TenantID(), paramsID, paramsSize)
	if err != nil {
		utils.ResponseJson(w, utils.Message("error downloading resource"), http.StatusBadRequest)
		return
	}

	w.Header().Set("Content-Type", "image/png; charset=utf-8")
	w.Write(bytes)
}

// Replace a media item's AWS S3 image, and replace it with a user provided image
func (sThree S3Controller) UpdateAdminS3Media(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	serviceConfig := config.GetAppConfig()
	w.Header().Set("Content-Type", "image/png; charset=utf-8")

	// Read Body and unmarshal into S3Media struct
	body, err := ioutil.ReadAll(r.Request().Body)
	if err != nil {

	}

	var s3Media S3Media
	err = json.Unmarshal(body, &s3Media)
	if err != nil {

	}

	// Create the S3 session for deletion and upload
	sess := conf.AwsS3Session
	uploader := s3manager.NewUploader(sess)
	svc := s3.New(sess)

	// Create the DeleteObjects for full-size and thumbnail sized images & delete
	fullInputForDelete := &s3.DeleteObjectInput{
		Bucket: aws.String(serviceConfig.AwsBucket),
		Key:    aws.String(r.TenantID().String() + commonServices.FullBucketPath + s3Media.ID.String() + "_full"),
	}
	thumbnailInputForDelete := &s3.DeleteObjectInput{
		Bucket: aws.String(serviceConfig.AwsBucket),
		Key:    aws.String(r.TenantID().String() + commonServices.ThumbnailBucketPath + s3Media.ID.String() + "_thumbnail"),
	}

	// Delete previous S3 objects
	fullDeleteResult, err := svc.DeleteObject(fullInputForDelete)
	if err != nil {
		//log.Println(time.Now()," - Error:S3:111 - Delete Full : ", err)
	}
	thumbnailDeleteResult, err := svc.DeleteObject(thumbnailInputForDelete)
	if err != nil {
		//log.Println(time.Now()," - Error:S3:115 - Delete Thumbnail : ", err)
	}
	fmt.Printf("file deleted from, %s\n , %s\n", fullDeleteResult, s3Media.ID.String())
	fmt.Printf("file deleted from, %s\n", thumbnailDeleteResult)

	// Create type reader for input into S3 as well as the upload objects
	fullReader := strings.NewReader(s3Media.Data)
	thumbnailReader := strings.NewReader(s3Media.Thumbnail)

	fullInputForSave := &s3manager.UploadInput{
		Bucket: aws.String(serviceConfig.AwsBucket),
		Key:    aws.String(r.TenantID().String() + commonServices.FullBucketPath + s3Media.ID.String() + "_full"),
		Body:   fullReader,
	}
	thumbnailInputForSave := &s3manager.UploadInput{
		Bucket: aws.String(serviceConfig.AwsBucket),
		Key:    aws.String(r.TenantID().String() + commonServices.ThumbnailBucketPath + s3Media.ID.String() + "_thumbnail"),
		Body:   thumbnailReader,
	}

	// UploadMedia new user provided image to S3
	_, err = uploader.Upload(fullInputForSave)
	if err != nil {
		//log.Println(time.Now()," - Error:S3:138 - UploadMedia Full : ", err)
	}
	_, err = uploader.Upload(thumbnailInputForSave)
	if err != nil {
		//log.Println(time.Now()," - Error:S3:142 - UploadMedia Thumbnail : ", err)
	}
}
