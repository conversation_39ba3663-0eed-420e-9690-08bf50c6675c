package adminControllers

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/tenant/admin/services"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/library/utils/converters"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/permissions"
	"encoding/json"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"io/ioutil"
	"log"
	"time"

	"net/http"
)

type (
	BusStatusController struct{}
)

func setBusStatusDates(statusMap map[string]interface{}, status *commonModels.BusStatus) {
	var startDateErr error
	var endDateErr error

	if statusMap["startDate"] != nil && len(statusMap["startDate"].(string)) > 0 {
		if status.StartDate.Time, startDateErr = time.Parse(time.RFC3339Nano, statusMap["startDate"].(string)); startDateErr != nil {
			status.StartDate.Valid = false
		} else {
			status.StartDate.Valid = true
		}
	}

	if statusMap["endDate"] != nil && len(statusMap["endDate"].(string)) > 0 {
		if status.EndDate.Time, endDateErr = time.Parse(time.RFC3339Nano, statusMap["endDate"].(string)); endDateErr != nil {
			status.EndDate.Valid = false
		} else {
			status.EndDate.Valid = true
		}
	}
}

func (bsc BusStatusController) GetAdminBusStatus(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication

	var inactive = converters.ToBool(r.Request().Form.Get("inactive"), false)
	var useSiteId = converters.ToBool(r.Request().Form.Get("useSiteId"), false)

	var statuses []commonModels.BusStatus
	var err error

	if inactive {
		statuses, err = adminServices.GetAllBusStatusResults(r.TenantDatabase(), r.CurrentSiteIDNullable())
		if err != nil {
			//log.Println(time.Now()," - Error:BusStatus:54 - GetAllBusStatusResults : ", err)
			http.NotFound(w, r.Request())
			return
		}
	} else {
		statuses, err = adminServices.GetBusStatusResults(r.TenantDatabase(), r.CurrentSiteIDNullable(), useSiteId, false)
		if err != nil {
			//log.Println(time.Now()," - Error:BusStatus:61 - GetBusStatusResults : ", err)
			http.NotFound(w, r.Request())
			return
		}
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	json.NewEncoder(w).Encode(statuses)
}

func (bsc BusStatusController) PostAdminBusStatus(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	if err := permissions.Evaluate(r.Account(), commonModels.BusStatus{}, "create"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	var status commonModels.BusStatus

	body, err := ioutil.ReadAll(r.Request().Body)
	if err != nil {

		http.Error(w, "Couldn't read posted data", http.StatusBadRequest)
		return
	}

	_ = json.Unmarshal(body, &status)
	status.Publisher = r.Account().ID

	statusMap := map[string]interface{}{}
	json.Unmarshal(body, &statusMap)

	status.Created = time.Now()
	setBusStatusDates(statusMap, &status)

	log.Println("BUSSTATUS: Starting CreateBusStatus")
	if _, err = adminServices.CreateBusStatus(r, status); err != nil {
		//log.Println(time.Now()," - Error:BusStatus:122 - CreateBusStatus : ", err)
		log.Println(fmt.Sprintf("ERROR: not able to create provided bus status \n err: [%s]", err))
		utils.ResponseJson(w, utils.Message(fmt.Sprintf("ERROR: not able to create provided bus status \n err: [%s]", err)), http.StatusBadRequest)
	}
	log.Println("BUSSTATUS: Finished CreateBusStatus Successfully")

}

func (bsc BusStatusController) PutAdminBusStatus(w http.ResponseWriter, r *shared.AppContext, p httpService.Params) {
	if err := permissions.Evaluate(r.Account(), commonModels.BusStatus{}, "update"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	id := uuid.FromStringOrNil(p["id"])
	if id == uuid.Nil {
		http.Error(w, "invalid id", http.StatusBadRequest)
		return
	}
	logger := logging.FromRequest(r.Request())

	var status commonModels.BusStatus

	body, err := ioutil.ReadAll(r.Request().Body)
	if err != nil {
		logger.Err(err).Msg("failed reading from request.body")
		http.Error(w, "Couldn't read posted data a", http.StatusBadRequest)
		return
	}

	err = json.Unmarshal(body, &status)
	if err != nil {
		logger.Err(err).Msg("failed to marshal bytes to bus status")
		//unmarshal error caused by sql.NullTime, TODO: change to time.Time
		//http.Error(w, "Couldn't read posted data b", http.StatusBadRequest)
		//return
	}
	status.Publisher = r.Account().ID

	statusMap := map[string]interface{}{}
	json.Unmarshal(body, &statusMap)

	status.Created = time.Now()
	setBusStatusDates(statusMap, &status)

	log.Println("BUSSTATUS: Starting UpdateBusStatus")
	if _, err = adminServices.UpdateBusStatus(r.TenantDatabase(), status); err != nil {
		//log.Println(time.Now()," - Error:BusStatus:122 - CreateBusStatus : ", err)
		log.Println(fmt.Sprintf("ERROR: not able to UPDATE provided bus status: [%s] \n err: [%s]", id, err))
		utils.ResponseJson(w, utils.Message(fmt.Sprintf("ERROR: not able to create provided bus status \n err: [%s]", err)), http.StatusBadRequest)
	}
	log.Println("BUSSTATUS: Finished UpdateBusStatus Successfully")

}

func (bsc BusStatusController) DeleteAdminBusStatus(w http.ResponseWriter, r *shared.AppContext) {
	//Requires Authentication
	if err := permissions.Evaluate(r.Account(), commonModels.BusStatus{}, "delete"); err != nil {
		utils.WriteResponseJSON(w, nil, err)
		return
	}

	tenantDB := r.TenantDatabase()
	body, err := ioutil.ReadAll(r.Request().Body)
	if err != nil {

		http.Error(w, "Couldn't parse data", http.StatusBadRequest)
		return
	}

	var status commonModels.BusStatus
	err = json.Unmarshal(body, &status)
	if err != nil {
		//log.Println(time.Now()," - Error:BusStatus:152 - Unmarshalling body into busStatus model : ", err)
	}

	status.Active = false
	status.Deleted = time.Now()
	if !status.EndDate.Valid {
		status.EndDate.Time = time.Now()
		status.EndDate.Valid = true
	}

	if _, err = adminServices.DeleteBusStatus(tenantDB, status); err != nil {
		//log.Println(time.Now()," - Error:BusStatus:165 - DeleteBusStatus : ", err)
		utils.ResponseJson(w, utils.Message("not able to delete specified status"), http.StatusBadRequest)
	}
}
