package adminControllers

import (
	"contentmanager/library/binding"
	"contentmanager/library/shared"
	adminDataaccess "contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"encoding/json"
	"io"
	"net/http"
)

type (
	RoleController struct{}
)

func (rc RoleController) GetTenantRole(w http.ResponseWriter, r *shared.AppContext) {
	roleChain, err := adminDataaccess.GetRoles(r.TenantDatabase())
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	w.WriteHeader(http.StatusOK)
	json.NewEncoder(w).Encode(roleChain)
}

func (rc RoleController) PostTenantRole(w http.ResponseWriter, r *shared.AppContext) {
	body, err := io.ReadAll(r.Request().Body)
	if err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusExpectationFailed)
		return
	}

	var role commonModels.Role

	if err = json.Unmarshal(body, &role); err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusBadRequest)
		return
	}

	contentMap := map[string]interface{}{}
	if err = json.Unmarshal(body, &contentMap); err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusBadRequest)
		return
	}

	if _, err = adminDataaccess.CreateRole(r.TenantDatabase(), role); err != nil {
		utils.ResponseJson(w, utils.Message(err.Error()), http.StatusBadRequest)
		return
	}
}

func (rc RoleController) PutTenantRole(w http.ResponseWriter, r *shared.AppContext) {
	var role commonModels.Role
	if err := binding.JSON.Bind(r.Request(), &role); err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	_, err := adminDataaccess.UpdateRole(r.TenantDatabase(), role)
	utils.WriteResponseJSON(w, nil, err)
}
