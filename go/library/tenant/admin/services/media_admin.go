package adminServices

import (
	tenantDataAccess "contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"time"
)

func GetMedia(dbCon *gorm.DB, siteId uuid.UUID) ([]commonModels.Media, error) {
	mediaChain, err := tenantDataAccess.GetAdminMediaData(dbCon, siteId)
	if err != nil {
		return mediaChain, err
	}
	return mediaChain, nil
}

func GetMediaById(dbCon *gorm.DB, mediaId uuid.UUID, ignoreActive bool) (commonModels.Media, error) {
	return tenantDataAccess.GetMediaById(dbCon, mediaId, ignoreActive)
}

func DeleteMedia(dbCon *gorm.DB, queryModel commonModels.Media) (commonModels.Media, error) {
	if media, err := tenantDataAccess.DeleteMedia(dbCon, queryModel); err != nil {
		return media, err
	} else {
		return media, nil
	}
}

func UpdateMedia(dbCon *gorm.DB, queryModel commonModels.Media) (commonModels.Media, error) {
	queryModel.Updated = time.Now()
	if media, err := tenantDataAccess.SaveMedia(dbCon, queryModel); err != nil {
		return media, err
	} else {
		return media, nil
	}
}

func CreateMedia(dbCon *gorm.DB, queryModel commonModels.Media) (commonModels.Media, error) {
	tenantDB := dbCon
	queryModel.Type = commonModels.Image
	image, err := tenantDataAccess.CreateMedia(tenantDB, queryModel)
	if err != nil {
		return image, err
	}
	return image, nil
}
