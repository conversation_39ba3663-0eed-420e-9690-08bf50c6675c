package adminServices

import (
	"contentmanager/library/tenant/admin/dataaccess"
	adminModels "contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
)

func CreateBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	if route, err := adminDataaccess.CreateBusRoute(dbCon, queryModel); err == nil {
		return route, nil
	} else {
		return route, err
	}
}
func UpdateBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	if route, err := adminDataaccess.UpdateBusRoute(dbCon, queryModel); err == nil {
		return route, nil
	} else {
		return route, err
	}
}
func DeleteBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	if route, err := adminDataaccess.DeleteBusRoute(dbCon, queryModel); err == nil {
		return route, nil
	} else {
		return route, err
	}
}
