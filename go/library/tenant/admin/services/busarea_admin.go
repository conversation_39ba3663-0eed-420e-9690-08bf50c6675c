package adminServices

import (
	"contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
)

func CreateBusArea(dbCon *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	if area, err := adminDataaccess.CreateBusArea(dbCon, queryModel); err == nil {
		return area, nil
	} else {
		return area, err
	}
}
func UpdateBusArea(dbCon *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	if area, err := adminDataaccess.UpdateBusArea(dbCon, queryModel); err == nil {
		return area, nil
	} else {
		return area, err
	}
}
func DeleteBusArea(dbCon *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	if area, err := adminDataaccess.DeleteBusArea(dbCon, queryModel); err == nil {
		return area, nil
	} else {
		return area, err
	}
}
