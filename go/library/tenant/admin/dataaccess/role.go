package adminDataaccess

import (
	models "contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
)

func GetRoles(dbCon *gorm.DB) ([]models.Role, error) {
	var roleChain = make([]models.Role, 0)
	return roleChain, dbCon.Where(" active = true ").Find(&roleChain).Error
}

func CreateRole(dbCon *gorm.DB, queryModel models.Role) (models.Role, error) {
	queryModel.Active = true
	if err := dbCon.Create(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}

func UpdateRole(dbCon *gorm.DB, queryModel models.Role) (models.Role, error) {
	if err := dbCon.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}
