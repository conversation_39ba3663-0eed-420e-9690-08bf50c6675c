package adminDataaccess

import (
	adminModels "contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
)

func CreateBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	dbQuery := dbCon
	if err := dbQuery.Create(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}

func UpdateBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	dbQuery := dbCon
	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}

func DeleteBusRoute(dbCon *gorm.DB, queryModel adminModels.BusRoute) (adminModels.BusRoute, error) {
	dbQuery := dbCon
	queryModel.Active = false

	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}
