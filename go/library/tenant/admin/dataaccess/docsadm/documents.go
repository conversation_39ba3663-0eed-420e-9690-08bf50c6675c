package docsadm

import (
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils/slicexx"
	"github.com/satori/go.uuid"
)

type Params struct {
	pagx.Query
	sortx.SortingQuery
	Search     string
	SiteID     *uuid.UUID
	Tags       []uuid.UUID
	Type       string
	Path       string
	IgnorePath bool
}

func Search(rq *shared.AppContext, q Params) result.Result[pagx.Paginated[commonModels.Document]] {
	var pag pagx.Paginated[commonModels.Document]

	tx := rq.TenantDatabase()
	tx = tx.Where("active = true").
		Where(" @siteID = any(sites) OR department_id = @siteID", map[string]interface{}{"siteID": q.SiteID})
	if !q.IgnorePath {
		tx = tx.Where(pgxx.LTreeChildren("path", q.Path))
	}

	if len(q.Tags) > 0 {
		tx = tx.Where(pgxx.ArrayHasAll("tags", q.Tags))
	}
	if len(q.Search) > 0 {
		tx = tx.Where(commonModels.Document{}.SearchQuery(), "%"+q.Search+"%")
	}
	if len(q.Type) > 0 && slicexx.Contains([]string{string(commonModels.Folder), string(commonModels.File)}, q.Type) {
		tx = tx.Where("type = @type", map[string]interface{}{"type": q.Type})
	}
	tx = tx.Order(q.GetSortingSQL("created desc"))

	return result.Check(pag, pagination.Paginate(tx, q.Query, &pag))
}
