package adminDataaccess

import (
	"contentmanager/library/tenant/common/models"
	"gorm.io/gorm"
)

func CreateBusArea(dbQuery *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	queryModel.Active = true
	return queryModel, dbQuery.Create(&queryModel).Error
}

func UpdateBusArea(dbQuery *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}

func DeleteBusArea(dbQuery *gorm.DB, queryModel commonModels.BusArea) (commonModels.BusArea, error) {
	queryModel.Active = false
	if err := dbQuery.Save(&queryModel).Error; err != nil {
		return queryModel, err
	}
	return queryModel, nil
}
