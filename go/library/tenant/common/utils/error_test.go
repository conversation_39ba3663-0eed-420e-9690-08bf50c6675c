package commonUtils

import (
	"contentmanager/library/tenant/common/models"
	"errors"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"net/url"
	"testing"
	"time"
)

func Test_ErrorFmt(t *testing.T) {

	tests := []struct {
		name      string
		arguments []interface{}
	}{
		{
			name:      "Time, Url, and String",
			arguments: append([]interface{}{}, time.Now(), &url.URL{Host: "www.host.com", Path: "/test"}, "some string"),
		},
		{
			name:      "Url, Error, Nil, ContentType, ID",
			arguments: append([]interface{}{}, &url.URL{Host: "www.host.com", Path: "/test"}, errors.New("invalid base64"), nil, commonModels.ContentType("template"), uuid.NewV4()),
		},
		{
			name:      "Nil, String, Nil, StatusCode",
			arguments: append([]interface{}{}, nil, "some string", nil, http.StatusBadRequest),
		},
		{
			name:      "Unknown, Byte, Nil, Integer, nil",
			arguments: append([]interface{}{}, commonModels.Content{Title: "Test", ID: uuid.NewV4()}, []byte("This is a byte array"), nil, 10329, nil),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ErrorFmt(tt.name, tt.arguments...)
		})
	}
}
