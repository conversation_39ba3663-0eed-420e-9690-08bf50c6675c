package commonDataaccess

import (
	commonInterfaces "contentmanager/library/tenant/common/interfaces"
	tenantModels "contentmanager/library/tenant/common/models"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
)

type legacyUrlInt struct{}

func (l *legacyUrlInt) GetByNorm(dbCon *gorm.DB, urlNorm string) (tenantModels.LegacyURL, error) {
	var legacyURL tenantModels.LegacyURL
	err := dbCon.Preload("ContentStatus").Where("active = true and url_norm = ?", urlNorm).First(&legacyURL).Error
	return legacyURL, err
}

func (l *legacyUrlInt) GetAllByContentID(dbCon *gorm.DB, contentID uuid.UUID) ([]tenantModels.LegacyURL, error) {
	legacyURLs := []tenantModels.LegacyURL{}
	err := dbCon.Preload("ContentStatus").Where("content_id = ?", contentID).Find(&legacyURLs).Error
	return legacyURLs, err
}

func (l *legacyUrlInt) ReplaceAllForContentID(db *gorm.DB, contentID uuid.UUID, urls []tenantModels.LegacyURL) error {
	if len(urls) > 0 {
		for _, u := range urls {
			if contentID != u.ContentID {
				return errors.New("DataAccess Legacy URL error: wrong ContentID for Legacy URL. ")
			}
		}
	}

	tx := db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	err := tx.Where("content_id = ?", contentID).Delete(&tenantModels.LegacyURL{}).Error
	if err != nil {
		tx.Rollback()
		return err
	}

	for _, u := range urls {
		e := tx.Create(&u).Error
		if e != nil {
			tx.Rollback()
			if strings.Contains(e.Error(), "uq_url_norm") {
				return errors.New("Legacy URL already exists for another content: " + u.Url)
			}
			return e
		}
	}

	return tx.Commit().Error
}

func ILegacyUrlDataAccessAdapter() commonInterfaces.ILegacyUrlDataAccess {
	return &legacyUrlInt{}
}
