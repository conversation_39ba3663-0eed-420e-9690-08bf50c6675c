package commonServices

import (
	"contentmanager/etc/conf"
	"contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/config"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/service/s3"
	"github.com/aws/aws-sdk-go/service/s3/s3manager"
	uuid "github.com/satori/go.uuid"
	"log"
	"strings"
	"time"
)

const (
	DocumentBucketPath  = "/documents/"
	FullBucketPath      = "/full/"
	ThumbnailBucketPath = "/thumbnail/"
)

func Copy(awsOriginBucket string, awsOriginPath string, awsTargetBucket string, awsTargetPath string, paramId string) error {
	sess := conf.AwsS3Session
	svc := s3.New(sess)
	_, err := svc.CopyObject(&s3.CopyObjectInput{
		Bucket:     aws.String(awsTargetBucket),
		CopySource: aws.String(awsOriginBucket + awsOriginPath + paramId),
		Key:        aws.String(awsTargetPath + paramId),
	})
	return err
}

// TODO => Bring in the ctx from the request
func Download(tenantId uuid.UUID, paramID string, paramsSize string) ([]byte, error) {
	appConfig := config.GetAppConfig()
	sess := conf.AwsS3Session
	downloader := s3manager.NewDownloader(sess)
	awsBuffer := aws.NewWriteAtBuffer([]byte{})
	var params *s3.GetObjectInput
	var err error

	// TODO => Replace objectType specific Context Timeout when we improve EC2 -> S3 download speeds
	timeout := map[bool]time.Duration{true: appConfig.GetDocumentTimeout(), false: appConfig.GetImageTimeout()}[paramsSize == commonModels.UploadDocument]

	switch paramsSize {
	case commonModels.UploadFullSize:
		params = &s3.GetObjectInput{
			Bucket: aws.String(appConfig.AwsBucket),
			Key:    aws.String(tenantId.String() + FullBucketPath + paramID + "_full"),
		}
	case commonModels.UploadDocument:
		params = &s3.GetObjectInput{
			Bucket: aws.String(appConfig.AwsBucket),
			Key:    aws.String(tenantId.String() + DocumentBucketPath + paramID),
		}
	case commonModels.UploadThumbnail:
		fallthrough
	default:
		params = &s3.GetObjectInput{
			Bucket: aws.String(appConfig.AwsBucket),
			Key:    aws.String(tenantId.String() + ThumbnailBucketPath + paramID + "_thumbnail"),
		}
	}

	var retries = 0
	var results []byte
	for retries < appConfig.GetMaxRetries() {
		ctx, cancel := context.WithTimeout(context.Background(), timeout)
		defer cancel()

		_, err = downloader.DownloadWithContext(ctx, awsBuffer, params)
		if err == nil || utils.IsErrorS3Timeout(err) || utils.IsErrorS3NotFound(err) {
			break
		}
		awsBuffer = aws.NewWriteAtBuffer([]byte{})
		retries += 1
	}
	if awsBuffer != nil && err == nil {
		results = awsBuffer.Bytes()
	}
	return results, err
}

func Exists(bucket string, path string, paramId string) (bool, error) {
	sess := conf.AwsS3Session
	svc := s3.New(sess)
	if _, err := svc.HeadObject(&s3.HeadObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(path + paramId),
	}); err != nil {
		return false, err
	} else {
		return true, nil
	}
}

func UploadCrop(tenantID uuid.UUID, originalImageID uuid.UUID, cropSizeID uuid.UUID, data string) error {
	if len(data) <= 0 {
		return errors.New("Failed to upload crop without data")
	}

	appConfig := config.GetAppConfig()
	b := appConfig.AwsBucket
	sess := conf.AwsS3Session
	uploader := s3manager.NewUploader(sess)

	reader := strings.NewReader(data)
	cropUploadInput := &s3manager.UploadInput{
		Bucket: aws.String(b),
		Key:    aws.String(tenantID.String() + FullBucketPath + originalImageID.String() + "_" + cropSizeID.String()),
		Body:   reader,
	}
	fullResult, err := uploader.Upload(cropUploadInput)
	if err != nil {
		log.Println(time.Now(), " - Error:Media:120 - uploadCrop : ", err)
		return err
	}
	_, err = json.Marshal(fullResult)
	if err != nil {
		log.Println(time.Now(), " - Error:Media:125 - Marshalling FullResult : ", err)
		return err
	}

	return nil
}

func UploadMedia(UploadMedia commonModels.UploadMedia, tenantId uuid.UUID) error {
	appConfig := config.GetAppConfig()
	b := appConfig.AwsBucket
	sess := conf.AwsS3Session
	uploader := s3manager.NewUploader(sess)

	if len(UploadMedia.Data) > 0 {
		fullReader := strings.NewReader(UploadMedia.Data)
		//TODO = (implement) => Retry on failure
		fullInput := &s3manager.UploadInput{
			Bucket: aws.String(b),
			Key:    aws.String(tenantId.String() + FullBucketPath + UploadMedia.ID.String() + "_full"),
			Body:   fullReader,
		}
		fullResult, err := uploader.Upload(fullInput)
		if err != nil {
			log.Println(time.Now(), " - Error:Media:147 - UploadMedia : ", err)
			return err
		}
		_, err = json.Marshal(fullResult)
		if err != nil {
			log.Println(time.Now(), " - Error:Media:152 - Marshalling FullResult : ", err)
		}
	}

	if len(UploadMedia.Thumbnail) > 0 {
		thumbnailReader := strings.NewReader(UploadMedia.Thumbnail)
		thumbnailInput := &s3manager.UploadInput{
			Bucket: aws.String(b),
			Key:    aws.String(tenantId.String() + ThumbnailBucketPath + UploadMedia.ID.String() + "_thumbnail"),
			Body:   thumbnailReader,
		}
		_, err := uploader.Upload(thumbnailInput)
		if err != nil {
			log.Println(time.Now(), " - Error:Media:157 - UploadMedia : ", err)
			return err
		}
	}

	return nil
}

func UploadDocument(UploadMedia commonModels.UploadMedia, tenantId uuid.UUID) error {
	if len(UploadMedia.Data) == 0 {
		return errors.New("no data")
	}
	appConfig := config.GetAppConfig()
	b := appConfig.AwsBucket
	documentReader := strings.NewReader(UploadMedia.Data)

	sess := conf.AwsS3Session
	uploader := s3manager.NewUploader(sess)

	documentInput := &s3manager.UploadInput{
		Bucket: aws.String(b),
		Key:    aws.String(tenantId.String() + DocumentBucketPath + UploadMedia.ID.String()),
		Body:   documentReader,
	}

	_, err := uploader.Upload(documentInput)
	if err != nil {
		return err
	}
	//json.Marshal(result)

	return nil
}

func ToBase64(b []byte) string {
	return base64.StdEncoding.EncodeToString(b)
}
