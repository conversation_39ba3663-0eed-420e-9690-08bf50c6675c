package commonInterfaces

import (
	tenantModels "contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/url"
)

type (
	ILegacyUrl interface {
		GetNewRoute(dbCon *gorm.DB, url *url.URL) (*url.URL, bool)
		GetAllByContentID(dbCon *gorm.DB, contentID uuid.UUID) ([]tenantModels.LegacyURL, error)
		ReplaceAllByContentID(dbCon *gorm.DB, contentID uuid.UUID, vm []tenantModels.LegacyURLViewModel) error
	}
)
