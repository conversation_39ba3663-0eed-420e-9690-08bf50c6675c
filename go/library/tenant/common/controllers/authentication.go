package commonControllers

import (
	conf "contentmanager/etc/conf"
	"contentmanager/library/binding"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth/claims"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/login"
	"contentmanager/pkgs/auth/login/native"
	"contentmanager/pkgs/auth/token"
	"net/http"
)

type (
	AuthenticationController struct{}
)

func (ac AuthenticationController) Authenticate(w http.ResponseWriter, r *shared.AppContext, manager token.TokenManager[identity.PublicAccount]) {
	r.Request().ParseForm()
	var form struct {
		Email    string
		Password string
	}
	if err := binding.JSON.Bind(r.Request(), &form); err != nil {
		utils.ResponseJson(w, utils.Message("Invalid request"), http.StatusBadRequest)
		return
	}
	if form.Email == "" || form.Password == "" {
		utils.ResponseJson(w, utils.Message("Invalid request"), http.StatusBadRequest)
		return
	}
	jwt, err := native.Authenticate(r.TenantDatabase(), form.Email, form.Password, manager)
	if err != nil {
		r.Logger().Err(err).Msg("Error authenticating: can't create cookie")
		utils.ResponseJson(w, utils.Message("Invalid email/password combination"), http.StatusBadRequest)
		return
	}
	claims.SetCookies(w, jwt, r.Request().Host)
	if !login.IsAdminApplicationURL(r.Request().Host) {
		if r.Request().URL.Path == conf.RouterLogin {
			http.Redirect(w, r.Request(), "/", http.StatusSeeOther)
		} else {
			http.Redirect(w, r.Request(), r.Request().Referer(), http.StatusTemporaryRedirect)
		}
	}
}
func (ac AuthenticationController) Logout(w http.ResponseWriter, r *shared.AppContext) {
	//Allow Anonymous
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	claims.ExpireCookies(w, r.Request().Host)
	http.Redirect(w, r.Request(), "/", http.StatusSeeOther)
}
