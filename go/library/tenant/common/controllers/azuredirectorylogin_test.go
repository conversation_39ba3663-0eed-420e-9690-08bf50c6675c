package commonControllers

import (
	"contentmanager/library/binding"
	"contentmanager/pkgs/auth/login/azure"
	"testing"
)

func Test_Deserialize(t *testing.T) {
	values := map[string][]string{
		"code":             {"AXYAhxMW71old0"},
		"state":            {"aHR0cHM6Ly9jb250ZW50bWFuYWdlci5pbWFnaW5lZXZlcnl0aGluZy5jb20"},
		"session_state":    {"5845d65b-39c2-4e36-95fd-c9cae86a2591"},
		"device_tenant_id": {"ef161387-255a-4777-af36-80b8fce4ee73"},
	}

	var azureResponse azure.ResponseForm

	if err := binding.MapFromMaps(&azureResponse, values); err != nil {
		t.Error(err)
	}

	if azureResponse.Code != "AXYAhxMW71old0" {
		t.Error("Code not deserialized")
	}
	if azureResponse.State != "aHR0cHM6Ly9jb250ZW50bWFuYWdlci5pbWFnaW5lZXZlcnl0aGluZy5jb20" {
		t.Error("State not deserialized")
	}
	if azureResponse.SessionState != "5845d65b-39c2-4e36-95fd-c9cae86a2591" {
		t.Error("SessionState not deserialized")
	}
}
