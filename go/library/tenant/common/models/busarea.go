package commonModels

import (
	"contentmanager/pkgs/auth"
	uuid "github.com/satori/go.uuid"
)

type BusArea struct {
	auth.TenantWideBase
	ID     uuid.UUID `json:"id" gorm:"column:id;type:uuid;primary_key;not null;default:uuid_generate_v4()"`
	Name   string    `json:"name" gorm:"column:name;type:character varying(64);not null"`
	Active bool      `json:"active" gorm:"column:active;type:boolean; not null"`
}

func (BusArea) TableName() string {
	return "bus_area"
}

func (b BusArea) GetScopeEntity() string {
	return "cm.transportation.bus_area"
}
