package commonModels

import (
	uuid "github.com/satori/go.uuid"
)

var _ IContent = (*Media)(nil)
var _ IContent = (*Document)(nil)
var _ IContent = (*Navigation)(nil)
var _ IEntity = (*Document)(nil)
var _ IEntity = (*Media)(nil)

// Navigation
func (c Navigation) GetSites() []uuid.UUID {
	return []uuid.UUID{c.SiteId}
}

func (c Navigation) GetDepartmentID() *uuid.UUID {
	// TODO => Leave comment @nick
	return nil
	//return c.DepartmentId
}

func (c Navigation) GetType() string {
	return "navigation"
}

func (c Navigation) GetScopeEntity() string {
	return "cm.navigation." + string(c.Type)
}

// Media
func (c Media) GetID() uuid.UUID {
	return c.ID
}
func (c Media) GetSites() []uuid.UUID {
	return c.Sites
}

func (c Media) GetDepartmentID() *uuid.UUID {
	return c.DepartmentID
}

func (c Media) GetType() string {
	return string(c.Type)
}

func (c Media) GetScopeEntity() string {
	return "cm.image"
}

// Document
func (c Document) GetSites() []uuid.UUID {
	return c.Sites
}

func (c Document) GetDepartmentID() *uuid.UUID {
	return c.DepartmentID
}

func (c Document) GetScopeEntity() string {
	return "cm.document." + string(c.Type)
}

func (c Document) GetType() string {
	return string(c.Type)
}

func (c Document) GetID() uuid.UUID {
	return c.ID
}
