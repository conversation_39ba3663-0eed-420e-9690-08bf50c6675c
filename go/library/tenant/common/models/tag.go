package commonModels

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/pkgs/auth"
	uuid "github.com/satori/go.uuid"
	_ "gorm.io/gorm"
)

var ContentTagTypes = []string{"page", "news", "event", "alert", "fragment"}

type Tag struct {
	auth.TenantWideBase
	ID     uuid.UUID              `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Name   string                 `json:"name" gorm:"column:name;type:character varying(256)"`
	Active bool                   `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
	Types  dbDriver.PgStringArray `gorm:"column:types;type:text[]"`
}

func (t Tag) GetScopeEntity() string {
	return "cm.tag"
}

func (Tag) TableName() string {
	return "tag"
}
func (t Tag) IsValid() bool {
	return t.Active
}
