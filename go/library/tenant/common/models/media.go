package commonModels

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	uuid "github.com/satori/go.uuid"
	"strings"
	"time"
)

type MediaType string

const (
	Image           MediaType = "image"
	Video           MediaType = "video"
	UploadThumbnail           = "thumbnail"
	UploadFullSize            = "full"
	UploadDocument            = "document"
)

type UploadMedia struct {
	ID        uuid.UUID `json:"id"`
	Data      string    `json:"data"`
	Thumbnail string    `json:"thumbnail"`
}

type Media struct {
	ID                    uuid.UUID            `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Filename              string               `json:"filename" gorm:"column:filename;character varying(64)"`
	Sites                 dbDriver.PgUUIDArray `json:"sites" gorm:"column:sites;type:uuid[]"`
	DepartmentID          *uuid.UUID           `json:"department_id" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
	Path                  string               `json:"path" gorm:"column:path;type:ltree"`
	ContentId             uuid.NullUUID        `json:"content_id" gorm:"column:content_id;type:uuid;"`
	Type                  MediaType            `json:"type" gorm:"column:type;type:media_type"`
	Tags                  dbDriver.PgUUIDArray `json:"tags" gorm:"column:tags;type:uuid[]"`
	Alt                   string               `json:"alt" gorm:"column:alt;type:CHARACTER VARYING(125);NOT NULL; DEFAULT:''"`
	Created               time.Time            `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
	Updated               time.Time            `json:"updated" gorm:"column:updated;type:timestamp with time zone;DEFAULT:now()"`
	Deleted               time.Time            `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
	Active                bool                 `json:"active" gorm:"column:active;type:bool;NOT NULL;DEFAULT:true"`
	Origin                *string              `json:"origin" gorm:"column:origin;type:CHARACTER VARYING(256);"`
	ImageCropSizeIDs      dbDriver.PgUUIDArray `json:"image_crop_size_ids" gorm:"column:image_crop_size_ids;type:uuid[]"`
	DirtyImageCropSizeIds dbDriver.PgUUIDArray `json:"dirty_image_crop_size_ids" gorm:"column:dirty_image_crop_size_ids;type:uuid[]"`
}

func (Media) TableName() string {
	return "media"
}
func NewMedia(id uuid.UUID, filename string, sites []uuid.UUID) Media {
	return Media{
		ID:       id,
		Filename: filename,
		Sites:    sites,
		Type:     Image,
	}
}
func (m *Media) IsValid() bool {
	hasPossibleExtension := strings.Index(m.Filename, ".") > -1
	return m.ID != uuid.Nil && len(m.Filename) > 0 && hasPossibleExtension
}

func (m Media) SearchQuery() string {
	return "(id || ' ' || filename || ' ' || coalesce(alt, ' ')) ilike ( ? )"
}
