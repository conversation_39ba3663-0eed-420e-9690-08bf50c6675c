package commonModels

import (
	"github.com/satori/go.uuid"
)

type NavigationType string

const (
	PageRoute    NavigationType = "page_route"
	ExternalLink NavigationType = "external_link"
	Email        NavigationType = "email"
)

type Navigation struct {
	ID             uuid.UUID      `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	ContentId      uuid.UUID      `gorm:"column:content_id;type:uuid;"`
	SiteId         uuid.UUID      `gorm:"column:site_id;type:uuid;"`
	DepartmentId   *uuid.UUID     `gorm:"column:department_id;type:uuid;"`
	Path           string         `json:"path" gorm:"column:path;type:ltree"`
	Type           NavigationType `json:"type" gorm:"column:type;type:navigation_type"`
	Visible        bool           `json:"visible" gorm:"column:visible;type:boolean;"`
	Active         bool           `json:"active" gorm:"column:active;type:boolean"`
	InlineChildren bool           `json:"inlineChildren" gorm:"column:inline_children;type:boolean;"`
	TreeIndex      int            `json:"treeIndex" gorm:"column:tree_index;type:int;DEFAULT:0"`
}

var _ IContent = (*Navigation)(nil)

func (Navigation) TableName() string {
	return "navigation"
}
