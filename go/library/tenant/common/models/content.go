package commonModels

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/pkgs/workspaces/shared"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/satori/go.uuid"
	"gorm.io/gorm"
	"strings"
	"time"
)

const ISOTimeFormat = "2006-01-02T15:04:05.000Z"
const PsqlTimeFormat = "2006-01-02T15:04:05.000000Z"

type Content struct {
	ID           uuid.UUID            `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Workspace    string               `gorm:"primaryKey;type:text"`
	EffectiveIn  driver.PgStringArray `gorm:"type:text[]"`
	Type         ContentType          `json:"type" gorm:"column:type;type:content_type"`
	Owner        uuid.UUID            `json:"owner" gorm:"column:owner;type:uuid" `
	Publisher    uuid.UUID            `json:"publisher" gorm:"column:publisher;type:uuid"`
	Title        string               `json:"title" gorm:"column:title;type:character varying(256)"`
	Content      string               `json:"content" gorm:"column:content;type:text"`
	Data         json.RawMessage      `json:"data" gorm:"column:data;type:jsonb;NOT NULL"`
	Structure    json.RawMessage      `json:"structure" gorm:"column:structure;type:jsonb;DEFAULT:'{}'::jsonb;NOT NULL"`
	Route        string               `json:"route" gorm:"column:route;type:character varying(256)"`
	Path         string               `json:"path" gorm:"column:path;type:ltree"`
	PageLayout   PageType             `json:"pagelayout" gorm:"column:pagelayout;type:page_style;DEFAULT:'HTML';NOT NULL"`
	Created      time.Time            `json:"created" gorm:"column:created;type:timestamp with time zone;DEFAULT:now()"`
	Updated      time.Time            `json:"updated" gorm:"column:updated;type:timestamp with time zone"`
	Deleted      time.Time            `json:"deleted" gorm:"column:deleted;type:timestamp with time zone"`
	PrivacyLevel int                  `json:"privacyLevel" gorm:"column:privacy_level;type:integer"`
	Approved     bool                 `json:"approved" gorm:"column:approved;type:boolean;DEFAULT:true"`
	Active       bool                 `json:"active" gorm:"column:active;type:boolean;"`
	Sites        driver.PgUUIDArray   `json:"sites" gorm:"column:sites;type:uuid[]"`
	MediaID      uuid.NullUUID        `json:"mediaId" gorm:"column:media_id;type:uuid"`
	Settings     json.RawMessage      `json:"settings" gorm:"column:settings;type:jsonb;NOT NULL;DEFAULT '{}'::jsonb"`
	DepartmentId uuid.NullUUID        `json:"departmentId" gorm:"column:department_id;type:uuid; DEFAULT: NULL"`
	StructureID  *uuid.UUID           `json:"structureId" gorm:"column:structure_id;type:uuid"`
	PublishAt    *time.Time           `json:"publish_at" gorm:"column:publish_at;type:timestamp with time zone"`
	ExpireAt     *time.Time           `json:"expire_at" gorm:"column:expire_at;type:timestamp with time zone"`

	Status     string             `json:"status" gorm:"-"`
	Media      Media              `json:"media" gorm:"foreignKey:media_id"`
	Meta       json.RawMessage    `json:"meta" gorm:"column:meta;type:jsonb"`
	Structures driver.PgUUIDArray `json:"structures" gorm:"column:structures;type:uuid[]"`

	Tags   []Tag              `json:"tags" gorm:"-"`
	TagIds driver.PgUUIDArray `gorm:"column:tags;type:uuid[]"`
}

func (c *Content) SetEffectiveIn(effectiveIn []string) {
	c.EffectiveIn = effectiveIn
}

func (c *Content) SetWorkspace(workspace string) {
	c.Workspace = workspace
}

var _ shared.Workspaced = (*Content)(nil)

func (c Content) GetWorkspace() string {
	return c.Workspace
}

func (c Content) GetEntityID() uuid.UUID {
	return c.ID
}

func (c Content) GetEffectiveIn() []string {
	return c.EffectiveIn
}

func (c Content) GetRoute() string {
	return c.Route
}

func (c Content) GetPublished() bool {
	return c.PublishAt != nil
}

type ContentSettings struct {
	IsPrimary      bool            `json:"isPrimary"`
	IsDistrictPage bool            `json:"isDistrictPage"`
	Priority       sql.NullFloat64 `json:"priority"`
	Classification []ContentType   `json:"classification"`
	StartDate      sql.NullTime    `json:"startdate"`
	ReleaseDate    sql.NullTime    `json:"releaseDate"`
	ExpirationDate sql.NullTime    `json:"expirationDate"`
	EndDate        sql.NullTime    `json:"enddate"`
	Navigation     bool            `json:"navigation"`
	ImportInfo     ImportInfo      `json:"importInfo"`
	Imported       bool            `json:"imported"`
	SeoTitle       string          `json:"seoTitle"`
	SeoDescription string          `json:"seoDescription"`
	AlertType      string          `json:"alertType"`
}
type ImportInfo struct {
	Source    string   `json:"source"`
	Calendar  string   `json:"calendar"`
	SchoolIds []string `json:"schoolIds"`
}

func (Content) TableName() string {
	return "content"
}

func (c Content) ReservationKey() string {
	return fmt.Sprintf("content::%s::%s", c.ID.String(), c.Workspace)
}

func (c *Content) AssociationColumns() []string {
	return []string{"Tags", "Tags.*"}
}

func (c *Content) ReplaceAssociations() map[string]interface{} {
	return map[string]interface{}{
		"Tags": &c.Tags,
	}
}

func (c *Content) OmitColumns() []string {
	return c.AssociationColumns()
}

func (c Content) GetData() map[string]interface{} {
	var result map[string]interface{}
	json.Unmarshal(c.Data, &result)
	return result
}

func (c Content) GetSettingsMap() map[string]interface{} {
	var result map[string]interface{}
	json.Unmarshal(c.Settings, &result)
	return result
}

func (c Content) GetSettings() ContentSettings {
	var cs ContentSettings
	json.Unmarshal(c.Settings, &cs)
	return cs
}

func (c Content) IsValid() bool {
	return c.ID != uuid.Nil
}

func (c Content) IsDistrictPage() bool {
	return c.GetSettings().IsDistrictPage
}

func (c Content) IsPrimary() bool {
	return c.GetSettings().IsPrimary
}

func (c Content) GetID() uuid.UUID {
	return c.ID
}

func (c Content) GetSites() []uuid.UUID {
	return c.Sites
}

func (c Content) GetDepartmentID() *uuid.UUID {
	var id *uuid.UUID
	if c.DepartmentId.Valid && c.DepartmentId.UUID != uuid.Nil {
		id = &c.DepartmentId.UUID
	}
	return id
}

func (c Content) GetType() string {
	return c.Type.String()
}

func (c Content) GetScopeEntity() string {
	switch c.Type {
	case Template, CSS, JS:
		return "cm.resource." + string(c.Type)
	case Page, DistributedPage, ExternalLinkContentType, Fragment:
		return "cm.content.page"
	default:
		return "cm.content." + string(c.Type)
	}
}

func MapTagsFromIDs(db *gorm.DB, tagIds []uuid.UUID) ([]Tag, error) {
	var tags = []Tag{}
	return tags, db.
		Where("active").
		Where(pgxx.FieldInArray("id", tagIds)).
		Find(&tags).Error
}

// ContentStatus //
type ContentStatus struct {
	ID          uuid.UUID            `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	Workspace   string               `gorm:"primaryKey;type:text"`
	EffectiveIn driver.PgStringArray `gorm:"type:text[]"`
	Route       string               `json:"route" gorm:"column:route;type:character varying(256)"`
	Active      bool                 `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
	PublishAt   *time.Time           `json:"publish_at" gorm:"column:publish_at"`
	ExpireAt    *time.Time           `json:"expire_at" gorm:"column:expire_at"`
}

func (s *ContentStatus) SetWorkspace(workspace string) {
	s.Workspace = workspace
}

func (s *ContentStatus) SetEffectiveIn(effectiveIn []string) {
	s.EffectiveIn = effectiveIn
}

var _ shared.Workspaced = (*ContentStatus)(nil)

func (s ContentStatus) GetWorkspace() string {
	return s.Workspace
}

func (s ContentStatus) GetEntityID() uuid.UUID {
	return s.ID
}

func (s ContentStatus) GetEffectiveIn() []string {
	return s.EffectiveIn
}

func (ContentStatus) TableName() string {
	return "content"
}

// PageType //
type (
	PageType string
)

const (
	Dct     PageType = "DCT"
	Wysiwyg PageType = "WYSIWYG"
	Html    PageType = "HTML"
)

func (pt PageType) String() string {
	return string(pt)
}

func (pt PageType) IsValid() bool {
	switch pt {
	case Dct, Wysiwyg, Html:
		return true
	}
	return false
}

func NewPageType(convert string) PageType {
	if try := PageType(strings.ToUpper(strings.TrimSpace(convert))); try.IsValid() {
		return try
	}
	return ""
}

// ContentType //
type (
	ContentType string
)

const (
	Template                ContentType = "template"
	Page                    ContentType = "page"
	CSS                     ContentType = "css"
	JS                      ContentType = "js"
	News                    ContentType = "news"
	Event                   ContentType = "event"
	Alert                   ContentType = "alert"
	NavigationContentType   ContentType = "navigation"
	ExternalLinkContentType ContentType = "external_link"
	DistributedPage         ContentType = "distributed_page"
	Fragment                ContentType = "fragment"
)

func (ct ContentType) String() string {
	return string(ct)
}

func (ct ContentType) IsValid() bool {
	switch ct {
	case Template, Page, CSS, JS, News, Event, Alert, NavigationContentType, ExternalLinkContentType, DistributedPage, Fragment:
		return true
	}
	return false
}

func NewContentType(convert string) ContentType {
	if try := ContentType(strings.ToLower(strings.TrimSpace(convert))); try.IsValid() {
		return try
	}
	return ""
}
