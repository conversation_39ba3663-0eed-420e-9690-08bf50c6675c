package commonModels

import (
	"bytes"
	"contentmanager/library/cache"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

type (
	Credentials struct {
		Scope        string `json:"scope"`
		ClientID     string `json:"client_id"`
		ClientSecret string `json:"client_secret"`
		BearerURL    string `json:"bearer_url"`
		Token        Token
	}
	Token struct {
		TokenType    string    `json:"token_type"`
		ExpiresIn    int       `json:"expires_in"`
		ExtExpiresIn int       `json:"ext_expires_in"`
		ExpiresOn    time.Time `json:"expires_on"`
		AccessToken  string    `json:"access_token"`
	}
)

func (t Token) IsValid() bool {
	return time.Now().Before(t.ExpiresOn) && len(t.AccessToken) > 0
}

func (t Token) WantsToBeRefreshed() bool {
	return !t.Is<PERSON>alid() || time.Now().After(t.ExpiresOn.Add(-10*time.Second))
}

func (t Token) GetAuthorizationHeader() string {
	return "Bearer " + t.AccessToken
}

func (t Token) String() string {
	return fmt.Sprintf("type: %s - tokenLen: %v - expiresOn: %s", t.TokenType, len(t.AccessToken), t.ExpiresOn)
}

func (c *Credentials) RefreshToken(cacheKey string) error {
	if c.Token.IsValid() && !c.Token.WantsToBeRefreshed() {
		return nil
	}
	token := cache.ICacheAdapter().GetObject(cacheKey)
	if token != nil && token.(Token).IsValid() && !token.(Token).WantsToBeRefreshed() {
		c.Token = token.(Token)
		return nil
	}
	data := url.Values{}
	data.Add("grant_type", "client_credentials")
	data.Add("client_id", c.ClientID)
	data.Add("client_secret", c.ClientSecret)
	if len(c.Scope) > 0 {
		data.Add("scope", c.Scope)
	}

	u, _ := url.ParseRequestURI(c.BearerURL)
	req, _ := http.NewRequest("POST", u.String(), bytes.NewBufferString(data.Encode()))

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("Content-Length", strconv.Itoa(len(data.Encode())))

	br, err := http.DefaultClient.Do(req)
	if err != nil {
		return err
	}
	defer br.Body.Close()
	body, err := ioutil.ReadAll(br.Body)
	if err != nil {
		return err
	}
	json.Unmarshal(body, &c.Token)
	c.Token.ExpiresOn = time.Now().Add(time.Duration(c.Token.ExpiresIn) * time.Second)

	if c.Token.IsValid() {
		cache.ICacheAdapter().CacheObject(cacheKey, c.Token, false)
		return nil
	}
	return errors.New("invalid or expired token")
}
