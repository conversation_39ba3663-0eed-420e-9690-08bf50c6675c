package binding

import (
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
	"net/url"
	"testing"
)

func Test_AdvancedURLBinding(t *testing.T) {
	type IDName struct {
		ID   uuid.UUID
		Name string
	}
	type TQ struct {
		PageSize   int
		Page       int
		Events     []string `json:"Events[]"`
		SomeObject []IDName `json:"SomeObject[]"`
	}

	u, _ := url.Parse("http://localhost:10000/api/v1/notifications/audit-records?page=1&pageSize=10&Events[]=send_issue_stopped&Events[]=send_issue_started&SomeObject[]=%7B%22ID%22:%225f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c%22,%22Name%22:%22issue%22%7D&SomeObject[]=%7B%22ID%22:%225f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c%22,%22Name%22:%22issue%22%7D&siteId=00ffecc6-d8fb-46c7-b357-462bd4d7ac6d")

	var tq TQ
	err := MapFromMaps(&tq, u.Query())
	expected := TQ{
		PageSize: 10,
		Page:     1,
		Events:   []string{"send_issue_stopped", "send_issue_started"},
		SomeObject: []IDName{
			{ID: uuid.FromStringOrNil("5f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c"), Name: "issue"},
			{ID: uuid.FromStringOrNil("5f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c"), Name: "issue"},
		},
	}

	assert.NoError(t, err)
	assert.Equal(t, tq, expected)
}

func TestMappingCustomFieldTypeWithURI(t *testing.T) {
	var s struct {
		Foo []uuid.UUID `uri:"foo"`
	}
	err := mappingByPtr(&s, formSource{"foo": {`df67786a-a543-4958-87ca-426954c626d5`, `df67786a-a543-4958-87ca-426954c626d5`}}, "uri")
	assert.NoError(t, err)

	assert.EqualValues(t, uuid.FromStringOrNil("df67786a-a543-4958-87ca-426954c626d5"), s.Foo[0])
}

func Test_tryAssignUUID(t *testing.T) {
	mm := map[string][]string{
		"id":  []string{"df67786a-a543-4958-87ca-426954c626d5"},
		"ids": []string{"df67786a-a543-4958-87ca-426954c626d5", "68dc3815-6ab5-4883-81a1-96eff25b659f"},
	}
	type UUIDs struct {
		Id  uuid.UUID   `uri:"id"`
		Ids []uuid.UUID `uri:"ids"`
	}

	var received UUIDs
	if err := mapFormByTag(&received, mm, "uri"); err != nil {
		t.Error(err)
	}

	expected := UUIDs{
		Id: uuid.FromStringOrNil("df67786a-a543-4958-87ca-426954c626d5"),
		Ids: []uuid.UUID{
			uuid.FromStringOrNil("df67786a-a543-4958-87ca-426954c626d5"),
			uuid.FromStringOrNil("68dc3815-6ab5-4883-81a1-96eff25b659f"),
		},
	}

	assert.Equal(t, received, expected)
}

func Test_ErrorSerialization(t *testing.T) {
	var m map[string]interface{}
	e := json.Unmarshal([]byte(`'`), &m)
	if b, err := json.Marshal(e); err != nil {
		t.Error(err)
	} else {
		t.Log(string(b))
	}
}
