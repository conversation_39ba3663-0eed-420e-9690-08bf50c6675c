package cache

import (
	"testing"
	"time"
)

func Test_GetOrSet2(t *testing.T) {
	// Test case 1a: Value not in cache
	v1, v2 := GetOrSet2[int, error]("key1", time.Second, func() (int, error) {
		return 42, nil
	})

	// Test case 1b: Value in cache
	v1, v2 = GetOrSet2[int, error]("key1", time.Second, func() (int, error) {
		panic("This should not be called")
	})

	if v1 != 42 || v2 != nil {
		t.<PERSON><PERSON><PERSON>("Expected (42, nil), got (%d, %s)", v1, v2)
	}

	// Test case 2: Different types
	v3, v4 := GetOrSet2[float64, bool]("key2", time.Second, func() (float64, bool) {
		return 3.14, true
	})

	if v3 != 3.14 || !v4 {
		t.<PERSON><PERSON><PERSON>("Expected (3.14, true), got (%f, %t)", v3, v4)
	}
}
