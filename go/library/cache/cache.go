package cache

import (
	"contentmanager/library/interfaces"
	"errors"
	"github.com/patrickmn/go-cache"
	"log"
	"strings"
	"time"
)

type cacheInt struct{}

var cc *cache.Cache

func init() {
	cc = cache.New(60*time.Minute, 90*time.Minute)
}

func GetOrSet[T any](key string, duration time.Duration, getValue func() T) T {
	if obj, found := cc.Get(key); found {
		if v, ok := obj.(T); ok {
			return v
		} else if obj == nil {
			var zero T
			return zero
		} else {
			log.Printf("[GetOrSet] assertion failures for: %v\n", obj)
		}
	}

	value := getValue()
	cc.Set(key, value, duration)
	return value
}

func GetOrSet2[T1 any, T2 any](key string, duration time.Duration, getValue func() (T1, T2)) (T1, T2) {
	if obj, found := cc.Get(key); found {
		if m, mok := obj.(map[string]interface{}); mok {
			var v1 T1
			var v2 T2
			v1, ok1 := m["v1"].(T1)
			v2, ok2 := m["v2"].(T2)
			if (ok1 || m["v1"] == nil) && (ok2 || m["v2"] == nil) {
				return v1, v2
			} else {
				log.Printf("[GetOrSet2] assertion failures for: %v\n", obj)
			}
		} else {
			log.Printf("[GetOrSet2] assertion failures for: %v\n", obj)
		}
	}

	v1, v2 := getValue()
	cc.Set(key, map[string]interface{}{
		"v1": v1,
		"v2": v2,
	}, duration)
	return v1, v2
}

func GetOrSet3[T1 any, T2 any, T3 any](key string, duration time.Duration, getValue func() (T1, T2, T3)) (T1, T2, T3) {
	if obj, found := cc.Get(key); found {
		if m, mok := obj.(map[string]interface{}); mok {
			var v1 T1
			var v2 T2
			var v3 T3
			v1, ok1 := m["v1"].(T1)
			v2, ok2 := m["v2"].(T2)
			v3, ok3 := m["v3"].(T3)
			if (ok1 || m["v1"] == nil) && (ok2 || m["v2"] == nil) && (ok3 || m["v3"] == nil) {
				return v1, v2, v3
			} else {
				log.Printf("[GetOrSet2] assertion failures for: %v\n", obj)
			}
		} else {
			log.Printf("[GetOrSet2] assertion failures for: %v\n", obj)
		}
	}

	v1, v2, v3 := getValue()
	cc.Set(key, map[string]interface{}{
		"v1": v1,
		"v2": v2,
		"v3": v3,
	}, duration)
	return v1, v2, v3
}

func (cacheInt) CacheObject(key string, value interface{}, expires bool) {
	if !expires {
		cc.Set(key, value, cache.NoExpiration)
	} else {
		cc.Set(key, value, cache.DefaultExpiration)
	}
}
func (cacheInt) CacheObjectForDuration(key string, value interface{}, duration time.Duration) {
	if duration == 0 {
		return
	}
	cc.Set(key, value, duration)
}
func (cacheInt) GetObject(key string) interface{} {
	if obj, found := cc.Get(key); found {
		return obj
	} else {
		return nil
	}
}

func (cacheInt) ExistsObject(key string) bool {
	if _, found := cc.Get(key); found {
		return true
	} else {
		return false
	}
}

func (cacheInt) SearchObject(k string) ([]interface{}, error) {
	var obj = cc.Items()
	var results []interface{}
	for key, value := range obj {
		if strings.Contains(key, k) {
			results = append(results, value)
		}
	}
	if len(results) > 0 {
		return results, nil
	} else {
		return nil, errors.New("no match error")
	}

}

func ICacheAdapter() interfaces.ICache {
	return &cacheInt{}
}
