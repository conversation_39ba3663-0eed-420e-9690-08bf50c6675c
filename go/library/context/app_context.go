package context

import (
	"contentmanager/pkgs/auth/identity"
	"github.com/rs/zerolog"
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// AppContextProvider defines the interface that provides application context
// information needed by GORM plugins and other components that need to avoid
// circular dependencies with the full shared.AppContext.
type AppContextProvider interface {
	TenantID() uuid.UUID
	TenantDatabase() *gorm.DB
	TimezoneLocation() *time.Location
	Logger() *zerolog.Logger
	Account() *identity.Account
	EditingSession() (*time.Time, error)
}
