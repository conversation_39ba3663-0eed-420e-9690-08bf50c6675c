package httpService

import (
	"contentmanager/library/shared"
	"contentmanager/logging"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/pkgs/auth/identity/accessor"
	"contentmanager/pkgs/auth/token"
	"contentmanager/pkgs/multitenancy"
	"context"
	"encoding/base64"
	"fmt"
	"github.com/rs/xid"
	"github.com/rs/zerolog"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"net/url"
	"strings"
	"time"
)

// SharedAppContext returns a middleware handler that logs the request as it goes in and the response as it goes out.
func SharedAppContext() Handler {
	return func(res http.ResponseWriter, req *http.Request, c Context,
		factory multitenancy.AccessorFactory, manager token.TokenManager[identity.PublicAccount]) {

		req.ParseForm()

		// Skip logging for healthcheck, also fixes Site Not Found error for healthcheck
		if req.URL.Path == "/api/v1/status" {
			res.WriteHeader(http.StatusOK)
			res.Write([]byte("OK"))
			return
		}

		start := time.Now()
		reqID := getReqId(req)
		method := req.Method
		u := getUrl(req).String()
		ip := getIp(req)
		amzTrace := getAmzId(req)
		ua := req.UserAgent()
		res.Header().Set("x-ie-request-id", reqID)
		log := logging.FromRequest(req)

		// init multitenancy accessor
		mta := factory.Accessor(req.Context())
		c.Map(mta)

		// tenantID is required for all requests
		tenantID, err := getTenantID(req, mta)
		if err != nil {
			log.Error().Err(err).Str("method", method).
				Str("url", u).
				Str("ip", ip).
				Str("req_id", reqID).Msg("TenantID not found")
			http.Error(res, fmt.Sprintf("The site %s is not found. ", req.Host), http.StatusNotFound)
			return
		}

		// init identity accessor
		identityAccessor := accessor.NewIdentityAccessor(req, tenantID, mta, manager)
		c.Map(identityAccessor)

		log.UpdateContext(func(c zerolog.Context) zerolog.Context {
			c = c.Str("method", method).
				Str("url", u).
				Str("ip", ip).
				Str("req_id", reqID)
			if len(amzTrace) > 0 {
				c = c.Str("amz_trace_id", amzTrace)
			}
			if identityAccessor.Authenticated() {
				c = c.Str("account", identityAccessor.PublicAccount().Email)
			}
			return c
		})

		// init shared app context
		sharedContext := shared.NewAppContext(req, mta, identityAccessor, tenantID)
		c.Map(sharedContext)

		log.Info().
			Msg("Request Started")

		ctx := req.Context()
		ctx = context.WithValue(ctx, "req_id", reqID)
		ctx = context.WithValue(ctx, "method", method)
		ctx = context.WithValue(ctx, "url", u)
		ctx = context.WithValue(ctx, "ip", ip)
		ctx = context.WithValue(ctx, "amz_trace_id", amzTrace)
		ctx = context.WithValue(ctx, "ua", ua)

		req = req.WithContext(ctx)
		c.Map(req)

		rw := res.(ResponseWriter)
		c.Next()

		var lastLog *zerolog.Event
		if status := rw.Status(); status >= 400 {
			lastLog = log.Error()
		} else {
			lastLog = log.Info()
		}
		lastLog.Int("status", rw.Status()).
			Int("size", rw.Size()).
			TimeDiff("duration", time.Now(), start).
			Str("ua", req.UserAgent()).
			Msg("Request Completed")
	}
}

func getIp(req *http.Request) string {
	addr := req.Header.Get("X-Real-IP")
	if addr == "" {
		addr = req.Header.Get("X-Forwarded-For")
		if addr == "" {
			addr = req.RemoteAddr
		}
	}
	return addr
}

func getAmzId(req *http.Request) string {
	return req.Header.Get("X-Amzn-Trace-Id")
}

func getReqId(req *http.Request) string {
	p := req.Header.Get("x-ie-request-id")
	if len(p) > 0 {
		return p
	}
	return xid.New().String()
}

func getUrl(req *http.Request) *url.URL {
	if req.URL.IsAbs() {
		return req.URL
	}

	return &url.URL{
		Scheme:   "https",
		Host:     req.Host,
		Path:     req.URL.Path,
		RawQuery: req.URL.RawQuery,
	}
}

func getTenantID(req *http.Request, mta multitenancy.Accessor) (tenantID uuid.UUID, err error) {
	tenantID, err = mta.TenantIDByHost(req.Host)
	if err == nil {
		return
	}

	state := req.FormValue("state")
	if state != "" {
		b, bErr := base64.RawURLEncoding.DecodeString(state)
		if bErr != nil {
			return tenantID, bErr
		}
		parts := strings.Split(string(b), "|")
		tenantID, err = mta.TenantIDByHost(parts[0])
	}

	return
}
