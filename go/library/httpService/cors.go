package httpService

import (
	"net/http"
	"strings"
)

func CorsMiddleware(commaSeparatedOrigins string) Handler {
	oo := map[string]interface{}{}
	for _, o := range strings.Split(commaSeparatedOrigins, ",") {
		o = strings.TrimSpace(o)
		if len(o) == 0 {
			continue
		}
		oo[o] = struct{}{}
	}
	return func(rw http.ResponseWriter, r *http.Request, c Context) {
		origin := r.Header.Get("Origin")
		if _, ok := oo[origin]; !ok {
			c.Next()
			return
		}

		rw.Header().Add("Access-Control-Allow-Origin", origin)
		rw.Header().Add("Access-Control-Allow-Methods", "DELETE, PATCH, PUT, POST, GET, OPTIONS")
		rw.Header().Add("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With, Set-Cookie")
		rw.Header().Add("Access-Control-Allow-Credentials", "true")
		if r.Method == "OPTIONS" {
			rw.WriteHeader(http.StatusOK)
			return
		}

	}
}
