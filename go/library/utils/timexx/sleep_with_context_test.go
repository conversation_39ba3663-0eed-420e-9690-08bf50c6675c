package timexx

import (
	"context"
	"testing"
	"time"
)

func Test_SleepWithContextCancelledBeforeSleepStarts(t *testing.T) {
	t.<PERSON>llel()

	// Test case 2: context is cancelled before sleep starts
	ctx2, cancel2 := context.WithCancel(context.Background())
	go func() {
		cancel2()
	}()
	startTime2 := time.Now()
	SleepWithContext(ctx2, 2*time.Second)
	duration2 := time.Since(startTime2)
	if duration2 > 10*time.Millisecond {
		t.Errorf("Expected duration to be very short, but got %s", duration2)
	}
}

func Test_SleepWithContextCancelled(t *testing.T) {
	t.Parallel()
	// Test case 1: context is cancelled before sleep duration expires
	ctx1, cancel1 := context.WithCancel(context.Background())
	go func() {
		time.Sleep(time.Second) // wait a bit before cancelling
		cancel1()
	}()
	startTime1 := time.Now()
	SleepWithContext(ctx1, 2*time.Second)
	duration1 := time.Since(startTime1)
	if duration1 < time.Second || duration1 > 3*time.Second {
		t.<PERSON><PERSON><PERSON>("Expected duration between 2 and 3 seconds, but got %s", duration1)
	}
}

func Test_SleepWithContext(t *testing.T) {
	t.Parallel()
	// Test case 3: sleep duration is zero
	ctx3, cancel3 := context.WithCancel(context.Background())
	defer cancel3()
	startTime3 := time.Now()
	SleepWithContext(ctx3, time.Second)
	duration3 := time.Since(startTime3)
	if duration3 < time.Second || duration3 > time.Second+100*time.Millisecond {
		t.Errorf("Expected duration to be 1 sec, but got %s < %s < %s", time.Second, duration3, time.Second+100*time.Millisecond)
	}
}

func Test_SleepWithContextZero(t *testing.T) {
	t.Parallel()
	// Test case 3: sleep duration is zero
	ctx3, cancel3 := context.WithCancel(context.Background())
	defer cancel3()
	startTime3 := time.Now()
	SleepWithContext(ctx3, 0)
	duration3 := time.Since(startTime3)
	if duration3 > 10*time.Millisecond {
		t.Errorf("Expected duration to be very short, but got %s", duration3)
	}
}
