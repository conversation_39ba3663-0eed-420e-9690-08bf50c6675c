package refxx

import (
	"errors"
	"reflect"
	"strings"
	"unsafe"
)

func InterfaceByPath(data interface{}, path string) (interface{}, error) {
	segments := strings.Split(path, ".")
	val := reflect.ValueOf(data)

	for _, segment := range segments {
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		if val.Kind() != reflect.Struct {
			return nil, errors.New("path leads to a non-struct type")
		}

		val = val.FieldByName(segment)
		if !val.IsValid() {
			return nil, errors.New("invalid field path")
		}
	}

	if !val.CanInterface() {
		return nil, errors.New("cannot interface value")
	}

	return val.Interface(), nil
}

func ValueByPath[T any](data interface{}, path string) (T, error) {
	var zero T

	segments := strings.Split(path, ".")
	val := reflect.ValueOf(data)

	for _, segment := range segments {
		if val.Kind() == reflect.Ptr {
			val = val.Elem()
		}

		if val.Kind() != reflect.Struct {
			return zero, errors.New("path leads to a non-struct type")
		}

		val = val.FieldByName(segment)
		if !val.IsValid() {
			return zero, errors.New("invalid field path")
		}
	}

	var result T
	if !val.CanInterface() {
		return zero, errors.New("cannot interface value")
	}

	result, ok := val.Interface().(T)
	if !ok {
		return zero, errors.New("type assertion failed")
	}

	return result, nil
}

func SetPrivateFieldByName[T any](target *T, fieldName string, value interface{}) error {
	val := reflect.ValueOf(target).Elem()
	field := val.FieldByName(fieldName)

	if !field.IsValid() {
		return errors.New("field sites not found in AppContext")
	}

	// Bypassing the unexported field restriction using unsafe
	field = reflect.NewAt(field.Type(), unsafe.Pointer(field.UnsafeAddr())).Elem()
	field.Set(reflect.ValueOf(value))
	return nil
}

func IterateIfSliceReturnFirstNonZero[T any](val reflect.Value, fn func(val reflect.Value) T) T {
	var zero T
	if !val.IsValid() {
		return zero
	}

	// Check if the value is nil for kinds that support the IsNil method
	if (val.Kind() == reflect.Ptr || val.Kind() == reflect.Slice || val.Kind() == reflect.Map ||
		val.Kind() == reflect.Interface || val.Kind() == reflect.Chan || val.Kind() == reflect.Func) && val.IsNil() {
		return zero
	}

	if val.Kind() == reflect.Slice || val.Kind() == reflect.Array {
		for i := 0; i < val.Len(); i++ {
			elem := val.Index(i)
			if res := fn(elem); !reflect.DeepEqual(res, zero) {
				return res
			}
		}
		return zero
	} else {
		return fn(val)
	}
}
