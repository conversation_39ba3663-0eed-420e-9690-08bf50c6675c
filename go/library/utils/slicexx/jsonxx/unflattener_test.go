package jsonxx

import (
	"encoding/json"
	"reflect"
	"testing"
)

func Test_UnflattenJSON(t *testing.T) {
	flattenedMap := map[string]interface{}{
		"imageList.0.id":           "cb305d6b-cbac-434f-926d-ff42d4daf276",
		"imageList.0.image.alt":    "News Image",
		"imageList.0.image.src":    "/images/ddb973ed-2a7d-51d5-8c45-ed6d8bf9c2e7",
		"imageList.0.caption":      "News Image",
		"imageList.1.id":           "8fdcb272-da69-506a-977a-90a683d579ec",
		"imageList.1.image.alt":    "Image 2",
		"imageList.1.image.src":    "/images/f4ed0d94-91fd-5ec6-ae37-7d79044d2622",
		"imageList.1.caption":      "Caption",
		"mainContent.id":           "2f52cc3a-f589-4acf-bc00-aaaa32d75546",
		"mainContent.lexical.html": "<section>\n...</section>",
	}

	result, err := UnflattenJSON(flattenedMap)
	if err != nil {
		t.Errorf("UnflattenJSON returned an error: %v", err)
	}

	var unflattenedJSON map[string]interface{}
	err = json.Unmarshal(result, &unflattenedJSON)
	if err != nil {
		t.Errorf("Failed to unmarshal unflattened JSON: %v", err)
	}

	// Check structure and values
	imageList, ok := unflattenedJSON["imageList"].([]interface{})
	if !ok || len(imageList) != 2 {
		t.Errorf("Expected imageList to be an array with 2 elements")
	}

	mainContent, ok := unflattenedJSON["mainContent"].(map[string]interface{})
	if !ok {
		t.Errorf("Expected mainContent to be an object")
	}

	if mainContent["id"] != "2f52cc3a-f589-4acf-bc00-aaaa32d75546" {
		t.Errorf("Unexpected value for mainContent.id")
	}

	lexical, ok := mainContent["lexical"].(map[string]interface{})
	if !ok {
		t.Errorf("Expected mainContent.lexical to be an object")
	}

	if lexical["html"] != "<section>\n...</section>" {
		t.Errorf("Unexpected value for mainContent.lexical.html")
	}
}

func Test_UnflattenJSONWithNestedArrays(t *testing.T) {
	flattenedMap := map[string]interface{}{
		"nested.array.0.0": float64(1),
		"nested.array.0.1": float64(2),
		"nested.array.0.2": float64(3),
		"nested.array.1.0": float64(4),
		"nested.array.1.1": float64(5),
		"nested.array.1.2": float64(6),
	}

	result, err := UnflattenJSON(flattenedMap)
	// t.Log(string(result))
	if err != nil {
		t.Errorf("UnflattenJSON returned an error: %v", err)
	}

	var unflattenedJSON map[string]interface{}
	err = json.Unmarshal(result, &unflattenedJSON)
	if err != nil {
		t.Errorf("Failed to unmarshal unflattened JSON: %v", err)
	}

	nested, ok := unflattenedJSON["nested"].(map[string]interface{})
	if !ok {
		t.Errorf("Expected nested to be an object")
	}

	array, ok := nested["array"].([]interface{})
	if !ok || len(array) != 2 {
		t.Errorf("Expected nested.array to be an array with 2 elements")
	}

	for i, subArray := range array {
		subArr, ok := subArray.([]interface{})
		if !ok || len(subArr) != 3 {
			t.Errorf("Expected nested.array[%d] to be an array with 3 elements", i)
		}
		for j, value := range subArr {
			expected := float64(i*3 + j + 1)
			if value != expected {
				t.Errorf("Expected nested.array[%d][%d] to be %v, but got %v", i, j, expected, value)
			}
		}
	}
}

func Test_UnflattenJSONWithEmptyInput(t *testing.T) {
	flattenedMap := map[string]interface{}{}

	result, err := UnflattenJSON(flattenedMap)
	if err != nil {
		t.Errorf("UnflattenJSON returned an error: %v", err)
	}

	if string(result) != "{}" {
		t.Errorf("Expected empty JSON object, but got: %s", string(result))
	}
}

func Test_FlattenUnflattenRoundTrip(t *testing.T) {
	originalJSON := []byte(`
	{
		"a": {
			"b": [
				{"c": 1, "d": 2},
				{"c": 3, "d": 4}
			],
			"e": "f"
		},
		"g": [1, 2, 3]
	}
	`)

	flattened, err := FlattenJSON(originalJSON)
	if err != nil {
		t.Errorf("FlattenJSON returned an error: %v", err)
	}

	unflattened, err := UnflattenJSON(flattened)
	if err != nil {
		t.Errorf("UnflattenJSON returned an error: %v", err)
	}

	var original, result map[string]interface{}
	json.Unmarshal(originalJSON, &original)
	json.Unmarshal(unflattened, &result)

	if !reflect.DeepEqual(original, result) {
		t.Errorf("Round trip failed. Original: %v, Result: %v", original, result)
	}
}
