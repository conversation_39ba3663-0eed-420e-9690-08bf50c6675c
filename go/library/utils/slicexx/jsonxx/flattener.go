package jsonxx

import (
	"fmt"
	"github.com/tidwall/gjson"
)

func FlattenJSON(data []byte) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	if !gjson.Valid(string(data)) {
		return result, fmt.<PERSON>rf("invalid JSON")
	}
	gjson.Parse(string(data)).ForEach(func(key, value gjson.Result) bool {
		flatten(key.String(), value, result)
		return true
	})
	return result, nil
}

func flatten(prefix string, value gjson.Result, result map[string]interface{}) {
	switch value.Type {
	case gjson.JSON:
		if value.IsArray() {
			for i, v := range value.Array() {
				flatten(fmt.Sprintf("%s.%d", prefix, i), v, result)
			}
		} else if value.IsObject() {
			value.ForEach(func(key, value gjson.Result) bool {
				flatten(fmt.Sprintf("%s.%s", prefix, key.String()), value, result)
				return true
			})
		}
	default:
		result[prefix] = value.Value()
	}
}
