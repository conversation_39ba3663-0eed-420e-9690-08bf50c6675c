package locker

import "sync"

type locker[T comparable] struct {
	m  map[T]interface{}
	mu sync.Mutex
}

func NewKeyLocker[T comparable]() *locker[T] {
	return &locker[T]{
		m: make(map[T]interface{}),
	}
}

// Unlock unlocks the `key`
func (l *locker[T]) Unlock(key T) {
	l.mu.Lock()
	defer l.mu.Unlock()

	delete(l.m, key)
}

// CheckAndLock check if `key` is already locked and if so, then returns false and does nothing,
// if `key` if free, then locks the `key` and returns true
func (l *locker[T]) CheckAndLock(key T) bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	if _, ok := l.m[key]; ok {
		return false
	}
	l.m[key] = struct{}{}
	return true
}
