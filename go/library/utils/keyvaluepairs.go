package utils

import (
	"github.com/BurntSushi/ty"
	"reflect"
)

func Any(slice interface{}, value interface{}) bool {
	chk := ty.Check(
		new(func([]ty.A)),
		slice)
	sliceValue := chk.Args[0]

	sliceLength := sliceValue.Len()

	for i := 0; i < sliceLength; i++ {
		if reflect.DeepEqual(sliceValue.Index(i).Interface(), value) {
			return true
		}

	}

	return false
}

// All(func(s string) bool {return s == value}, []<T>)
// All is provided a function which is the value provided from slice
// The function provided must return a boolean
// .All returns a boolean true if all items iterated over pass the function provided
func All(function, slice interface{}) bool {
	chk := ty.Check(
		new(func(func(ty.A) ty.B, []ty.A) []ty.A),
		function, slice)
	funcValue, sliceValue := chk.Args[0], chk.Args[1]

	sliceLength := sliceValue.Len()
	for i := 0; i < sliceLength; i++ {
		if !funcValue.Call([]reflect.Value{sliceValue.Index(i)})[0].Bool() {
			return false
		}
	}
	return true
}

// Filter(func(s string) bool {return s == value}, []<T>).([]<T>)
func Filter(function, slice interface{}) interface{} {
	chk := ty.Check(
		new(func(func(ty.A) ty.B, []ty.A) []ty.A),
		function, slice)
	funcValue, sliceValue, sliceType := chk.Args[0], chk.Args[1], chk.Returns[0]

	sliceLength := sliceValue.Len()
	resultSlice := reflect.MakeSlice(sliceType, 0, 0)
	var counter = 0
	for i := 0; i < sliceLength; i++ {
		if funcValue.Call([]reflect.Value{sliceValue.Index(i)})[0].Bool() {
			resultSlice = reflect.Append(resultSlice, sliceValue.Index(i))
			counter += 1
		}
	}
	return resultSlice.Interface()
}

// Map(func(value <TA>) <TB> { return <TB>{ } }, slice).([]<TB>)
func Map(function, slice interface{}) interface{} {
	chk := ty.Check(
		new(func(func(ty.A) ty.B, []ty.A) []ty.B),
		function, slice)
	funcValue, sliceValue, sliceType := chk.Args[0], chk.Args[1], chk.Returns[0]

	sliceLength := sliceValue.Len()
	resultSlice := reflect.MakeSlice(sliceType, sliceLength, sliceLength)
	for i := 0; i < sliceLength; i++ {
		valueOfFunction := funcValue.Call([]reflect.Value{sliceValue.Index(i)})[0]
		resultSlice.Index(i).Set(valueOfFunction)
	}
	return resultSlice.Interface()
}
