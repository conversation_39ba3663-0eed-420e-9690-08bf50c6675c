package xlsxx

import (
	"github.com/xuri/excelize/v2"
	"log"
)

func SaveXLSXFile(fileName string, data [][]interface{}) error {
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			log.Println(err)
		}
	}()

	for idx, row := range data {
		cell, err := excelize.CoordinatesToCellName(1, idx+1)
		if err != nil {
			return err
		}
		if err := f.SetSheetRow("Sheet1", cell, &row); err != nil {
			return err
		}
	}

	if err := f.SaveAs(fileName); err != nil {
		return err
	}

	return nil
}
