package utils

import (
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type customQueryBuilder struct {
	db *gorm.DB
}

func GetStatement(db *gorm.DB, i interface{}) map[string]interface{} {
	stmt := db.Session(&gorm.Session{DryRun: true}).Find(&i).Statement
	return map[string]interface{}{
		"query": stmt.SQL.String(),
		"vars":  stmt.Vars,
	}
}

func NewCustomClauseBuilder(db *gorm.DB) *customQueryBuilder {
	return &customQueryBuilder{db: db}
}
func newExpr(statement string, arguments []interface{}, withoutParentheses bool) clause.Expr {
	return clause.Expr{
		SQL:                statement,
		Vars:               arguments,
		WithoutParentheses: withoutParentheses,
	}
}
func (ccb *customQueryBuilder) UnwrappedWhere(statement string, arguments ...interface{}) *gorm.DB {
	var expressions = append(make([]clause.Expression, 0), newExpr(statement, arguments, true))
	if len(expressions) == 0 {
		return ccb.db
	}
	return ccb.db.Clauses(clause.Where{Exprs: expressions})
}
func (ccb *customQueryBuilder) WithOnConflictUpdateClause() *gorm.DB {
	ccb.db = ccb.db.Clauses(clause.OnConflict{UpdateAll: true, Columns: []clause.Column{{Name: "id"}}})
	return ccb.db
}

type UpsertAssociationModel interface {
	// OmitColumns returns an array of string that is spread onto the gorm.Omit function, ignoring the field on Upsert.
	// This should be used when deliberately ignoring a field or when using looking to Replace any associations using ReplaceAssociations
	OmitColumns() []string
	TableName() string
	// ReplaceAssociations returns a map of AssociationName(string) to Replace value(&interface).
	// Replace value should be a value address (&)
	Associations() map[string]interface{}
}

/*	TODO => Validate functionality of replace using interface as opposed to typed struct (ReplaceAssociations & for loop)
	  	Gorm seems to be unable to reconcile what table its looking at based on an interface value (UpsertAssociationModel) even if its
  		underlying type is a valid struct
*/
//func (ccb *customQueryBuilder) Upsert(model UpsertAssociationModel) error {
//	err := ccb.db.Transaction(func(dbQuery *gorm.DB) error {
//		dbQuery = ccb.WithOnConflictUpdateClause()
//		if err := dbQuery.
//			//Omit(model.OmitColumns()...).
//			Create(&model).Error; err != nil {
//			return err
//		}
//		dbQuery = dbQuery.Model(&model).Omit(model.OmitColumns()...)
//		for k, v := range model.ReplaceAssociations() {
//			if len(k) == 0 {
//				return fmt.Errorf("invalid replacement associations provided \n key: [%s]", k)
//			}
//			if err := dbQuery.Association(k).Replace(&v); err != nil {
//				return err
//			}
//		}
//		return nil
//	})
//	return err
//}
