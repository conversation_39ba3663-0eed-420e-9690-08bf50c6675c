package utils

import (
	"context"
	"fmt"
	"math/rand"
	"time"
)

// RandomSleep pauses execution for a random duration between min and max milliseconds
// It respects context cancellation and returns early if context is canceled
func RandomSleep(ctx context.Context, min, max int) error {
	if min < 0 || max < 0 {
		return fmt.Errorf("min and max must be positive")
	}
	if min > max {
		return fmt.Errorf("min cannot be greater than max")
	}

	duration := time.Duration(rand.Intn(max-min+1)+min) * time.Millisecond

	timer := time.NewTimer(duration)
	defer timer.Stop()

	select {
	case <-timer.C:
		return nil
	case <-ctx.Done():
		return ctx.Err()
	}
}
