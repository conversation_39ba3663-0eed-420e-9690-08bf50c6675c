package utils

import (
	"encoding/base64"
)

var stdPrefixLength = 2

func IsBase64Equal(hash, expected, prefix string) bool {
	prefixLen := len(prefix)
	if len(hash) < prefixLen {
		return false
	}
	withoutPrefix := hash[prefixLen:]
	b, e := base64.StdEncoding.DecodeString(withoutPrefix)
	if e != nil || string(b) != expected {
		return false
	}
	return true
}

func StandardPrefix(str string) string {
	if len(str) < stdPrefixLength {
		return ""
	}
	return str[:stdPrefixLength]
}
