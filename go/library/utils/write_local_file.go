package utils

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path"
)

type WriteLocalFileParams struct {
	FilePath    string
	ContentType string // "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
}

func WriteLocalFile(w http.ResponseWriter, params WriteLocalFileParams) error {
	file, err := os.Open(params.FilePath)
	if err != nil {
		return fmt.Errorf("failed to open file: %w", err)
	}
	defer file.Close()

	// Get file info
	fileInfo, err := file.Stat()
	if err != nil {
		return fmt.Errorf("failed to get file info: %w", err)
	}

	// Set headers
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", path.Base(params.FilePath)))
	w.Header().Set("Content-Type", params.ContentType)
	w.Header().Set("Content-Length", fmt.Sprintf("%d", fileInfo.Size()))

	// Stream the file to the client
	_, err = io.Copy(w, file)
	if err != nil {
		return fmt.Errorf("failed to copy file to response: %w", err)
	}

	return nil
}
