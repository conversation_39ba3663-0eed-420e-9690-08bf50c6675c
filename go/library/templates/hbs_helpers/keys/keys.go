package keys

import "contentmanager/library/utils/mapxx"

type Keys struct {
	cm map[string]interface{}
}

func NewKeys() *Keys {
	return &Keys{cm: map[string]interface{}{}}
}

func (k *Keys) Display(key string) {
	k.cm[key] = struct{}{}
}

func (k *Keys) Displayed(key string) bool {
	_, ok := k.cm[key]
	return ok
}

func (k *Keys) Clear() {
	k.cm = map[string]interface{}{}
}

func (k *Keys) DisplayedKeys() []string {
	return mapxx.Keys(k.cm)
}
