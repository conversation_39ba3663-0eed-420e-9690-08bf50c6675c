package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
)

func init() {
	handlebars.RegisterHelper("ifCounter", ifCounterHelper)
	handlebars.RegisterHelper("counterInc", counterIncHelper)
	handlebars.RegisterHelper("counterVal", counterValHelper)
}

func ifCounterHelper(name string, sign string, num int, options *handlebars.Options) interface{} {
	helpers := options.DataFrame().GetHelpers()
	if helpers == nil {
		return ""
	}

	if helpers.Counters().Compare(name, sign, num) {
		return options.Fn()
	}

	return options.Inverse()
}

func counterIncHelper(name string, options *handlebars.Options) interface{} {
	helpers := options.DataFrame().GetHelpers()
	if helpers == nil {
		return ""
	}
	helpers.Counters().Inc(name)

	return ""
}

func counterValHelper(name string, options *handlebars.Options) interface{} {
	helpers := options.DataFrame().GetHelpers()
	if helpers == nil {
		return ""
	}
	return helpers.Counters().Get(name)
}
