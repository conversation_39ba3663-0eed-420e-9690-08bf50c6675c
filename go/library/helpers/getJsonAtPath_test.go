package helpers

import (
	"reflect"
	"testing"
)

// TestDCTData and tests.ExpectedValue / Error must be kept in sync.
var TestDCTData = []byte("{\n  \"tabs\": [\n    {\"field1\": null, \"field2\": null, \"field3\": \"valid text\"}\n  ], \n  \"boxes\": [], \n  \"intro\": {\n    \"introText\": \"test\", \"introTitle\": null\n  }, \n  \"gallery\": [{}], \n  \"sections\": [], \n  \"tabIntro\": {\n    \"test1\": null, \"test2\": null\n  }, \n  \"documents\": [], \n  \"spotlight3\": {}, \n  \"simpleLinks\": [], \n  \"usefulLinks\": [], \n  \"boxesSection\": {}, \n  \"gallerySection\": {}, \n  \"documentsSection\": {}, \n  \"usefulLinksSection\": {}\n}")

var tests = []struct {
	Name          string
	ExpectedValue interface{}
	ExpectedError error
}{
	{
		Name:          "intro.introText",
		ExpectedValue: "test",
		ExpectedError: nil,
	},
	{
		Name:          "intro.introTitle",
		ExpectedValue: nil,
		ExpectedError: InvalidJsonAtPath,
	},
	{
		Name:          "gallerySection.FieldThatDoesntExist",
		ExpectedValue: nil,
		ExpectedError: InvalidJsonAtPath,
	},
	{
		Name:          "gallerySection",
		ExpectedValue: nil,
		ExpectedError: InvalidJsonAtPath,
	},
	{
		Name:          "boxes",
		ExpectedValue: nil,
		ExpectedError: InvalidJsonAtPath,
	},
	{
		Name: "tabs",
		ExpectedValue: append([]interface{}{}, map[string]interface{}{
			"field1": nil,
			"field2": nil,
			"field3": "valid text",
		}),
		ExpectedError: nil,
	},
	{
		Name:          "sectionThatDoesntExist",
		ExpectedValue: nil,
		ExpectedError: InvalidJsonAtPath,
	},
}

func Test_GetJsonAtPath(t *testing.T) {
	for _, tCase := range tests {
		valueAtPath, err := GetJsonAtPath(TestDCTData, tCase.Name)
		if !reflect.DeepEqual(valueAtPath, tCase.ExpectedValue) {
			t.Errorf("GetJsonAtPath - Received Unexpected Value: [%s], wanted: [%s]", valueAtPath, tCase.ExpectedValue)
		}
		if err != tCase.ExpectedError {
			t.Errorf("GetJsonAtPath - Received Unexpected Error: [%s], wanted: [%s]", err, tCase.ExpectedError)
		}
	}
}
