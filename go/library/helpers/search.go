package helpers

import (
	"contentmanager/etc/conf"
	"contentmanager/library/tenant/public/utils/handlebars"
)

func init() {
	handlebars.RegisterHelper("renderSearchBar", renderSearchBar)
}

func renderSearchBar() string {
	var search string
	search = `
		<div class="optionbox-padding">
		    <form id="search-form" action="/search">
				<div class="input-field">
		        	<input id="` + conf.SearchHelper + `" name="` + conf.SearchHelper + `" class="search-box" type="text" placeholder="Search..">
					<label for="` + conf.SearchHelper + `">Search</label>
				</div>
				<div class="input-buttons">
		        	<input id="search-button" class="back-2 rounded" type="submit" value="Find">
				</div>
		    </form>
		</div>`
	return search
}
