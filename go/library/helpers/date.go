package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"strconv"
	"time"
)

func init() {
	handlebars.RegisterHelper("dateGetDay", dateGetDay)
	handlebars.RegisterHelper("dateGetMonthStr", dateGetMonthStr)
	handlebars.RegisterHelper("dateGetMonthDayYear", dateGetMonthDayYear)
	handlebars.RegisterHelper("dateToRFC2822", dateToRFC2822)
}

func dateGetDay(date time.Time) string {
	return strconv.Itoa(date.Day())
}

func dateGetMonthStr(date time.Time) string {
	return date.Month().String()[:3]
}

func dateGetMonthDayYear(date time.Time) string {
	return date.Format("Jan 02, 2006")
}

func dateToRFC2822(date time.Time) string {
	return date.Format(time.RFC822Z)
}
