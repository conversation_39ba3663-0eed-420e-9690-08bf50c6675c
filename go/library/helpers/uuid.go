package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"github.com/satori/go.uuid"
	"log"
	"reflect"
)

func init() {
	handlebars.RegisterHelper("uuidToString", uuidToString)
	handlebars.RegisterHelper("uuidIsNull", uuidIsNull)
	handlebars.RegisterHelper("uuidArrayContains", uuidArrayContains)
}

func uuidToString(value uuid.UUID) string {
	return value.String()
}

func uuidIsNull(value uuid.UUID, options *handlebars.Options) interface{} {
	if reflect.DeepEqual(uuid.UUID{}, value) {
		return options.Inverse()
	}

	return options.Fn()
}

func uuidArrayContains(context interface{}, f func(interface{}) interface{}) bool {
	val := reflect.ValueOf(context)

	switch val.Kind() {
	case reflect.Array, reflect.Slice:
		for i := 0; i < val.Len(); i++ {
			t := f(val.Index(i).Interface())
			log.Println(t)
		}
	}
	return true

}
