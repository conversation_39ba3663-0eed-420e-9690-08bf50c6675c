package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"strings"
)

func init() {
	handlebars.RegisterHelper("ifContains", ifContains)
}

// #if block helper
func ifContains(mainValue string, containsValue string, options *handlebars.Options) interface{} {

	if strings.Contains(strings.ToLower(mainValue), strings.ToLower(containsValue)) {
		return options.Fn()
	}
	return options.Inverse()
}
