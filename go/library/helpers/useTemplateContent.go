package helpers

import (
	handlebarsModels "contentmanager/library/tenant/public/models"
	"contentmanager/library/tenant/public/utils/handlebars"
	"reflect"
	"strings"
)

func init() {
	handlebars.RegisterHelper("useTemplateContent", useTemplateContent)
}

func useTemplateContent(templates []handlebarsModels.ContentForHandlebars, template string, options *handlebars.Options) interface{} {
	if reflect.ValueOf(template).Len() == 0 {
		return ""
	}
	if i := FindItemInSlice(templates, template); i.Active == true {
		return i.Content
	}
	return ""
}
func FindItemInSlice(contentChain []handlebarsModels.ContentForHandlebars, itemName string) handlebarsModels.ContentForHandlebars {
	for _, i := range contentChain {
		if strings.ToLower(i.Title) == strings.ToLower(itemName) {
			return i
		}
	}
	return handlebarsModels.ContentForHandlebars{}
}
