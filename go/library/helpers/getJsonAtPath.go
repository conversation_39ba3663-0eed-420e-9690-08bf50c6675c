package helpers

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"encoding/json"
	"errors"
	"reflect"
	"strings"
)

var (
	InvalidJsonAtPath = errors.New("invalid json at path")
)

// GetJsonAtPath returns the value found at path in the provided json.RawMessage, it will safely return (Nil, Nil) on invalid paths.
// Invalid value or path results in a return of (Nil, Nil)
// Errors are only != Nil when Value is empty or if an error is presented in marshalling (currently commented out pending discussion)
func GetJsonAtPath(value json.RawMessage, path string) (interface{}, error) {
	if value == nil {
		return nil, errors.New("empty json value provided")
	}

	var ok bool
	var result map[string]interface{}

	if err := json.Unmarshal(value, &result); err != nil {
		// Could log the whole value here, though I'm a bit wary of massive data objects.
		// Not returning Inverse here, as we could have a partial success and the desired path could be valid.
		// *Edit: How should we be handling these errors? We don't want to stop the function execution (due to the above)
		// but we don't want to provide a logger to the func,

		//options.DataFrame().GetLogger().Err(err).
		//	Str("hbsError", "error unmarshalling json").
		//	Str("path", path).
		//	Msg("")
	}
	if !handlebars.IsTrue(result) {
		return nil, InvalidJsonAtPath
	}

	pathArray := strings.Split(path, ".")
	if len(pathArray) == 0 {
		return nil, InvalidJsonAtPath
	}
	for i, v := range pathArray {
		iValue := result[v]
		if !handlebars.IsTrue(iValue) {
			return nil, InvalidJsonAtPath
		}

		if i != len(pathArray)-1 {
			result, ok = iValue.(map[string]interface{})
			if ok {
				continue
			} else {
				break
			}

		}

		if i == len(pathArray)-1 {
			switch reflect.ValueOf(iValue).Kind() {
			case reflect.String:
				return iValue, nil
			case reflect.Map:
				if doesInterfaceMapHaveAnyValidValues(iValue) {
					return iValue, nil
				}
			case reflect.Slice:
				if sl, ok := iValue.([]interface{}); ok {
					for _, item := range sl {
						if doesInterfaceMapHaveAnyValidValues(item) {
							return iValue, nil
						}
					}
				}
			}
		}
	}

	return nil, InvalidJsonAtPath
}

// doesInterfaceMapHaveAnyValidValues checks if a Map or Interface{} | Map contain any fields with Truthy values
func doesInterfaceMapHaveAnyValidValues(iValue interface{}) bool {
	if iMap, ok := iValue.(map[string]interface{}); ok {
		for _, fieldValue := range iMap {
			if handlebars.IsTrue(fieldValue) {
				return true
			}
		}
	}
	return false
}
