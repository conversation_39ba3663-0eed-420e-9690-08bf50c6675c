package hbsQuery

import (
	"contentmanager/library/helpers/ietags"
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/pkgs/content/public"
)

func init() {
	handlebars.RegisterHelper("useFragment", useFragment)
	handlebars.RegisterHelper("withFragment", withFragment)
}

/* Note: issue with testability:
   Code as currently written might not be abstractable to run in test cases - might need something like
   we do in Query where the real function performs a DB lookup, but the abstracted method returns a pre-defined object.
   Look at getFragmentById, and by extension, getContentById to see if we can abstract this to make it testable.
*/

/*
useFragment is for injecting the compiled fragment content without the use of a block helper
options.Inverse() is not available - It returns a safeString, meaning triple brackets aren't required for HTML escape.
{{useFragment "id"}}
*/
func useFragment(fragmentId string, options *handlebars.Options) handlebars.SafeString {
	appContext, err := handlebars.GetAppContextFromOptions(options)
	if err != nil {
		return ""
	}
	if ff, err := ietags.CompileAll(appContext, public.By{
		IDsOrNames:   []string{fragmentId},
		ContentTypes: []string{"fragment"},
	}); err == nil {
		if len(ff) == 0 {
			return ""
		} else if len(ff) > 1 {
			options.DataFrame().GetHelpers().Logger().Warn().
				Str("fragmentId", fragmentId).
				Str("helper", "useFragment").
				Msg("More than one fragment found with provided ID. Using first fragment for: " + fragmentId)
		}
		return handlebars.SafeString(ff[0].Content)
	} else {
		options.DataFrame().GetLogger().
			Err(err).
			Str("fragmentId", fragmentId).
			Str("helper", "useFragment").
			Msg("error getting fragment by id")
	}
	return ""
}

/*
withFragment is the block helper for utilizing fragments - the content field contains the compiled html.
{{#withFragment "id"}}

	{{title}}
	{{{content}}}

{{else}}

	An error has occurred, or no content exists with provided ID

{{/withFragment}}
*/
func withFragment(fragmentId string, options *handlebars.Options) string {
	appContext, err := handlebars.GetAppContextFromOptions(options)
	if err != nil {
		return ""
	}
	if ff, err := ietags.CompileAll(appContext, public.By{
		IDsOrNames:   []string{fragmentId},
		ContentTypes: []string{"fragment"},
	}); err == nil {
		if len(ff) == 0 {
			return options.Inverse()
		} else if len(ff) > 1 {
			options.DataFrame().GetHelpers().Logger().Warn().
				Str("fragmentId", fragmentId).
				Str("helper", "useFragment").
				Msg("More than one fragment found with provided ID. Using first fragment for: " + fragmentId)
		}
		return options.FnWith(ff[0])
	} else {
		options.DataFrame().GetLogger().
			Err(err).
			Str("fragmentId", fragmentId).
			Str("helper", "useFragment").
			Msg("error getting fragment by id")
	}
	return options.Inverse()
}
