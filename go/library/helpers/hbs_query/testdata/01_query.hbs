-------- Query list --------
{{#query '{hasTags: ["tag1", "tag2"], excludeTags: ["tag3"],}'}}
    -- loop index: {{@index}}
    -- total pages: {{@total}}
    <h1>{{Title}}</h1>
    {{#if @last}}
        {{#pager "page"}}
            -> Page: {{@page}} current: {{@current}} url: {{{@url}}}
        {{/pager}}
    {{/if}}
{{/query}}

{{#query '{     excludeDisplayed:true, hasTags:     ["tag1", "tag2"], excludeTags:["tag3"],}'}}
    -- loop index: {{@index}}
    -- total pages: {{@total}}
    <h1>{{Title}}</h1>
{{else}}
    Nothing to show
{{/query}}

-------- Query single item --------
{{#queryFirst '{hasTags: ["tag1", "tag2"], excludeTags: ["tag3"],}'}}
    {{Title}}
{{/queryFirst}}

{{#queryFirst '{     excludeDisplayed:true, hasTags:     ["tag1", "tag2"], excludeTags:["tag3"],}'}}
    {{Title}}
{{else}}
    Nothing to show
{{/queryFirst}}