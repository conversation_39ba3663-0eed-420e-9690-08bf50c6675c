package hbsQuery

import (
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	shared2 "contentmanager/library/templates/hbs_helpers/shared"
	"contentmanager/library/tenant/public/models"
	"errors"
)

type hbsRepositoryMock struct {
	shared2.BaseHandleBarRepository
}

func (h *hbsRepositoryMock) Query(params shared2.HandlebarsQueryParams) result.Result[pagx.Paginated[publicModels.ContentForHandlebars]] {
	var res pagx.Paginated[publicModels.ContentForHandlebars]
	if params.ExcludeDisplayed {
		return result.Success(res)
	}

	rows := []publicModels.ContentForHandlebars{
		{
			Title: params.GetKey([]string{}),
		},
		{
			Title: "Title",
		},
	}

	return result.Success(pagx.Paginated[publicModels.ContentForHandlebars]{
		TotalRecords: int64(len(rows)),
		TotalPages:   5,
		Offset:       0,
		PageSize:     10,
		Page:         1,
		Rows:         rows,
	})
}

func (h *hbsRepositoryMock) QueryFirst(params shared2.HandlebarsQueryParams) (publicModels.ContentForHandlebars, error) {
	if params.ExcludeDisplayed {
		return publicModels.ContentForHandlebars{}, errors.New("No record found for query: " + params.GetKey([]string{}))
	}

	return publicModels.ContentForHandlebars{
		Title: params.GetKey([]string{}),
	}, nil
}

func (h *hbsRepositoryMock) GetContentById(idOrName string) (publicModels.ContentForHandlebars, error) {
	return publicModels.ContentForHandlebars{
		Title: idOrName,
	}, nil
}

func NewHbsMockRepository() shared2.IHandlebarsRepository {
	return &hbsRepositoryMock{}
}
