package helpers

import (
	"contentmanager/library/templates/hbs_helpers"
	"contentmanager/library/templates/hbs_helpers/counters"
	"contentmanager/library/templates/hbs_helpers/keys"
	"contentmanager/library/templates/hbs_helpers/shared"
	"strconv"
	"sync"
	"testing"
)

func Test_Operators(t *testing.T) {
	RunHandlebarFilesTests(t, ctx, helpers, IgnoreWhiteSpaceComparer)
}

func helpers() shared.IHbsHelpers {
	return hbsHelpers.NewHbsHelpers(NewMockRequest("https://example.com/path?page=1&random=111"))
}

func ctx() interface{} {
	return &ViewModel{
		Users: []User{
			{
				Id:        1,
				Name:      "<PERSON>",
				displayed: false,
			},
			{
				Id:        2,
				Name:      "<PERSON>",
				displayed: false,
			},
			{
				Id:        3,
				Name:      "<PERSON>",
				displayed: false,
			},
			{
				Id:        4,
				Name:      "<PERSON>",
				displayed: false,
			},
		},
	}
}

type (
	User struct {
		Id        int
		Name      string
		displayed bool
	}

	ViewModel struct {
		Users             []User
		counters          *counters.Counters
		keys              *keys.Keys
		initForHandlebars sync.Once
	}
)

func (u User) GetKey() string {
	return strconv.Itoa(u.Id)
}
