package ietags

import (
	"os"
	"testing"
)

func Test_Regex(t *testing.T) {
	bb, err := os.ReadFile("testdata/snippet.html")
	if err != nil {
		t.Fatal(err)
	}

	stringsToReplace := GetFragmentSnippetsToReplace(string(bb))

	if len(stringsToReplace) != 4 {
		t.<PERSON><PERSON>("expected 4 snippets")
	}
	if stringsToReplace[`<ie-fragment title="Some title " data-lexical-content-fragment="c4b22dc7-13c3-4f4e-b3b5-63716aee4e99"></ie-fragment>`].String() != "c4b22dc7-13c3-4f4e-b3b5-63716aee4e99" {
		t.<PERSON>("expected first snippet to be snippet1")
	}
}
