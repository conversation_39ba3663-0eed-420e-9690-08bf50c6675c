package ietags

import (
	"contentmanager/library/shared"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/content/public"
	uuid "github.com/satori/go.uuid"
	"regexp"
	"strings"
)

// This function should handle all <ie-> html tags.
// it should apply alterations to <ie-fragment> and <ie-document>, and any future custom ie html tags.
func ReplaceIETags(r *shared.AppContext, content string) string {
	for i := 0; i < 5; i++ {
		snippets := GetFragmentSnippetsToReplace(content)
		if len(snippets) == 0 {
			//return content
			break
		}

		fragmentsToFind := make([]uuid.UUID, 0, len(snippets))
		for _, id := range snippets {
			fragmentsToFind = append(fragmentsToFind, id)
		}

		by := public.By{
			IDs:          fragmentsToFind,
			ContentTypes: []string{"fragment"},
		}

		compiledFragments, err := CompileAll(r, by)
		if err != nil {
			r.Logger().Err(err).Msgf("error compiling fragments: %v", by.IDs)
			content = removeFragments(content, snippets)
			break
			//return removeFragments(content, snippets)
		}
		compiledFragmentsMap := slicexx.AsMap(compiledFragments, func(fragment FragmentViewModel) uuid.UUID {
			return fragment.ID
		})
		toReplace := make(map[string]string)
		for key, value := range snippets {
			if fragment, ok := compiledFragmentsMap[value]; ok {
				toReplace[key] = fragment.Content
			} else {
				toReplace[key] = ""
			}
		}

		for key, value := range toReplace {
			content = strings.ReplaceAll(content, key, value)
		}
	}
	content = ReplaceLexicalDocumentLinkNodes(r, content)
	if strings.Contains(content, "<ie-query") {
		content = strings.Replace(content, "</body>", "<script src=\"/api/v1/queries/ie-query.js\"></script></body>", 1)
	}
	return content
}

func removeFragments(content string, snippets map[string]uuid.UUID) string {
	for key := range snippets {
		content = strings.ReplaceAll(content, key, "")
	}
	return content
}

var re = regexp.MustCompile(`(?s)<ie-fragment[^>]*data-lexical-content-fragment="([0-9a-fA-F-]+)"[^>]*>.*?</ie-fragment>`)

func GetFragmentSnippetsToReplace(content string) map[string]uuid.UUID {
	matches := re.FindAllStringSubmatch(content, -1)
	resultMap := make(map[string]uuid.UUID)

	for _, match := range matches {
		if len(match) == 2 {
			snippet := match[0]
			uuidStr := match[1]

			if parsedUUID, err := uuid.FromString(uuidStr); err == nil {
				resultMap[snippet] = parsedUUID
			}
		}
	}

	return resultMap
}
