package controllers

import (
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/tests"
	uuid "github.com/satori/go.uuid"
	"log"
	"testing"
	"time"
)

func Test_UpsertDepartment(t *testing.T) {
	t.Skip("skip while secure_settings change is in progress")
	// Arrange
	db, dispose := tests.InitMultitenancyDB()
	defer dispose()
	siteID := uuid.NewV4()
	tenant := tenancyModels.Tenant{
		ID:          uuid.NewV4(),
		Name:        "Tenant 1",
		Description: "Description 1",
		Created:     time.Now(),
		Active:      true,
	}
	site1 := tenancyModels.Site{
		BaseSite: tenancyModels.BaseSite{
			ID:            siteID,
			PrimaryDomain: siteID.String(),
			TenantID:      tenant.ID,
			Type:          tenancyModels.District,
			Active:        true,
		},
		Created: time.Now(),
	}
	site2 := tenancyModels.Site{
		BaseSite: tenancyModels.BaseSite{
			ID:       uuid.NewV4(),
			TenantID: tenant.ID,
			Type:     tenancyModels.District,
			Active:   true,
		},
		Created: time.Now(),
	}
	if err := db.Create(&tenant).Error; err != nil {
		log.Fatal(err)
	}
	if err := db.Create(&site1).Error; err != nil {
		log.Fatal(err)
	}
	if err := db.Create(&site2).Error; err != nil {
		log.Fatal(err)
	}
	department1 := tenancyModels.Site{
		BaseSite: tenancyModels.BaseSite{
			ID:       uuid.NewV4(),
			TenantID: tenant.ID,
			Type:     tenancyModels.District,
			Active:   true,
		},
		Created: time.Now(),
		Hosts:   []tenancyModels.Site{site1, site2},
	}
	// Act
	newSite, err := UpsertDepartment(db, department1)
	// Assert
	if err != nil {
		t.Errorf("UpsertDepartment() error = %v", err)
	}
	if len(newSite.Hosts) != 2 {
		t.Errorf("UpsertDepartment() error = %v", err)
	}
	modifiedSite := newSite
	modifiedSite.Hosts = []tenancyModels.Site{site1}
	updatedSite, err := UpsertDepartment(db, modifiedSite)
	if err != nil {
		t.Errorf("UpsertDepartment() error = %v", err)
	}
	if len(updatedSite.Hosts) != 1 {
		t.Errorf("UpsertDepartment() error = %v", err)
	}
}
