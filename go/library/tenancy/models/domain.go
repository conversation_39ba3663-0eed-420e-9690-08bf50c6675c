package models

import (
	uuid "github.com/satori/go.uuid"
	_ "gorm.io/gorm"
)

type Domain struct {
	ID            uuid.UUID `json:"id" gorm:"column:id;type:uuid;primary_key;NOT NULL;DEFAULT:uuid_generate_v4()"`
	TenantID      uuid.UUID `json:"tenant_id" gorm:"column:tenant_id;type:uuid;NOT NULL"`
	SiteID        uuid.UUID `json:"site_id" gorm:"column:site_id;type:uuid;NOT NULL"`
	Domain        string    `json:"domain" gorm:"column:domain;type:character varying(128);NOT NULL"`
	IsPrimary     bool      `json:"is_primary" gorm:"column:is_primary;type:boolean;NOT NULL;DEFAULT:false"`
	IsSiteDefault bool      `json:"is_site_default" gorm:"column:is_site_default;type:boolean;DEFAULT:false"`
	Active        bool      `json:"active" gorm:"column:active;type:boolean;DEFAULT:true"`
}

func (Domain) TableName() string {
	return "domain"
}
