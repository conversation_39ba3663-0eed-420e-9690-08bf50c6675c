package services

import (
	"contentmanager/library/shared"
	tenancyDataAccess "contentmanager/library/tenancy/dataaccess"
	tenancyModels "contentmanager/library/tenancy/models"
	"contentmanager/pkgs/auth/permissions"
	uuid "github.com/satori/go.uuid"
)

func GetSiteResults(r *shared.AppContext, siteType tenancyModels.SiteType, page string, limit string) ([]tenancyModels.Site, int, error) {
	var siteIds []uuid.UUID
	account := r.Account()
	if !account.IsAdmin {
		if ids, err := permissions.GetSitesForScopes(account, "cm.site", "cm.settings", "cm.settings.instagram"); err == nil {
			siteIds = ids
		} else {
			return []tenancyModels.Site{}, 0, err
		}
	}
	return tenancyDataAccess.GetSites(r.TenancyDB(), r.TenantID(), siteIds, siteType, page, limit)
}

func UpdateSite(r *shared.AppContext, updateModel tenancyModels.Site) (tenancyModels.Site, error) {
	siteModel, err := r.SiteByID(updateModel.ID)
	if err != nil {
		return siteModel, err
	}
	if updateModel.Settings != nil {
		siteModel.Settings = updateModel.Settings
	}
	if updateModel.Tags != nil {
		siteModel.Tags = updateModel.Tags
	}
	if updateModel.Description != "" {
		siteModel.Description = updateModel.Description
	}
	if updateModel.Type != "" && updateModel.Type != siteModel.Type {
		if siteType, ok := tenancyModels.NewSiteType(string(updateModel.Type)); ok {
			siteModel.Type = siteType
		}
	}
	if updateModel.PrimaryDomain != "" {
		siteModel.PrimaryDomain = updateModel.PrimaryDomain
	}
	return updateModel, r.TenancyDB().Model(&updateModel).Updates(updateModel).Error
}
