package db_seed

import (
	"contentmanager/servers/importer/common/configurable_import"
	"github.com/satori/go.uuid"
)

var (
	SiteID       = uuid.NewV4()
	SettingsID   = uuid.NewV4()
	TemplatePath = "1.2"
)

func GetImportConfig(configID string, templateID uuid.UUID) (ImportConfig configurable_import.ImportConfig) {
	var contentTypeConfig = configurable_import.ContentFieldConfig{
		TemplateID:   templateID,
		Sites:        []uuid.UUID{SiteID},
		ComputedPath: TemplatePath,
		Fields: configurable_import.Fields{
			"site": {
				UseDefault: true,
				InsertOnly: false,
			},
			"privacyLevel": {
				UseDefault: true,
				Default:    2,
				InsertOnly: false,
			},
			"published": {
				UseDefault: true,
				Default:    "",
				InsertOnly: false,
			},
			"tags": {
				UseDefault: true,
				Default:    "",
				InsertOnly: false,
			},
		},
	}
	return configurable_import.ImportConfig{
		SettingsID: SettingsID,
		Sites:      []uuid.UUID{SiteID},
		FromSettingsData: configurable_import.FromSettingsData{
			Id:     configID,
			Events: contentTypeConfig,
			News:   contentTypeConfig,
		},
	}

}
