package db_seed

import (
	"contentmanager/logging"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func SeedTenant2(db *gorm.DB) {
	seedAdminT2(db)
	seedTemplate(db, admin_t2.ID, []uuid.UUID{t2s1.ID})
}

func seedAdminT2(db *gorm.DB) {
	logging.RootLogger().Info().Msg("Seeding admin_t2")
	if err := db.Save(&admin_t2).Error; err != nil {
		panic("Failed to seed admin_t2: " + err.<PERSON>rror())
	}
}
