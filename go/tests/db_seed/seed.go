package db_seed

import (
	"contentmanager/etc/conf"
	sharedDatabase "contentmanager/library/shared/database"
	"contentmanager/library/tenancy/models"
	"contentmanager/logging"
	"fmt"
	"gorm.io/gorm"
	"os"
	"strings"
	"time"
)

func Seed() {
	if os.Getenv("CM_E2E") != "1" {
		logging.RootLogger().Info().Msg("Skipping seeding of databases")
		return
	}

	connStr := fmt.Sprintf(conf.DatabaseConnectionStringFormat, cnf.DbHost, "5432", cnf.DbServer, cnf.DbUser, cnf.DbPassword)
	tenancyDB := waitForDBColumn(connStr, "site", "primary_domain")
	waitForDBColumn(connStr, "tenant", "admin_url")

	migrateTenancy(tenancyDB)
	migrateTenants(tenancyDB)
}

func migrateTenancy(db *gorm.DB) {
	domainsCount := -1
	for i := 0; i < 20; i++ {
		var count int64
		if db.Table("domain").Where("id = ?", "********-0000-0000-0000-********0011").Count(&count).Error == nil {
			domainsCount = int(count)
			logging.RootLogger().Info().Int("domainsCount", domainsCount).Msg("Migrating cm_multitenancy database")
			break
		}
		time.Sleep(5 * time.Second)
	}

	if domainsCount == -1 {
		panic("Failed to migrate cm_multitenancy database")
	}

	if domainsCount == 0 {
		logging.RootLogger().Info().Msg("Seeding cm_multitenancy database")
		SeedMultitenancy(db)
	}
}

func migrateTenants(tenancyDB *gorm.DB) {
	var tenants []models.Tenant
	if tenancyDB.Where("id = ? OR id = ?", t1.ID, t2.ID).Find(&tenants).Error != nil {
		panic("Failed to migrate tenants")
	}

	if len(tenants) != 2 {
		panic("Failed to migrate tenants")
	}

	for _, tenant := range tenants {
		connStr := fmt.Sprintf(conf.DatabaseConnectionStringFormat, tenant.Host, "5432", tenant.Server, tenant.DBUser, tenant.DBPassword)
		db := waitForDBColumn(connStr, "content", "tags")
		waitForTableInDB(db, "content_history")
		waitForColumnInDB(db, "account", "created_at")

		TruncateTenantTables(db) // Comment this line for dev if needed

		if tenant.ID.String() == "********-0000-0000-0000-********0002" {
			SeedTenant2(db)
		} else if tenant.ID.String() == "********-0000-0000-0000-************" {
			SeedTenant1(db)
		}
	}
}

func TruncateTenantTables(db *gorm.DB) {
	tables := []string{
		"public.content",
		"public.content_history",
		"public.account",
		"public.document",
		"public.navigation",
		"public.legacy_url",
		"public.media",
		"public.security_group",
		"public.lists",
		"public.account_security_group",
		"public.structure",
		"public.redirect_rule",
		"public.document_history",
		"public.navigation_history",
		"public.role",
		"public.settings",
		"public.subscriber",
		"public.tag",
		"public.structure_history",
		"public.audit_record",
		"public.issue",
		"public.topic",
		"public.pa_tokens",
		"public.relay",
		"public.third_party_content",
		"public.security_group_history",
		"public.schedule",
		"public.media_history",
		"public.bus_route",
		"public.bus_status",
		"public.role_history",
		"public.submission",
		"public.subscription",
		"public.redirect_rule_history",
		"public.bus_area",
	}
	queryBuildsr := strings.Builder{}
	for _, table := range tables {
		queryBuildsr.WriteString("TRUNCATE TABLE " + table + " CASCADE; ")
	}
	if err := db.Exec(queryBuildsr.String()).Error; err != nil {
		logging.RootLogger().Error().Err(err).Msg("Failed to truncate tables")
		return
	}

	logging.RootLogger().Info().Msg("Truncated tables")
}

func waitForDBTable(connStr string, table string) *gorm.DB {
	var db *gorm.DB
	for i := 0; i < 36; i++ {
		d, err := sharedDatabase.CreateDatabaseConnectionByString(connStr)
		if err == nil {
			db = d
			break
		}
		logging.RootLogger().Warn().Err(err).Msg("Failed to connect to database, retrying...")
		time.Sleep(5 * time.Second)
	}

	waitForTableInDB(db, table)

	return db
}

func waitForTableInDB(db *gorm.DB, table string) {
	dbName := getDatabaseName(db)

	for i := 0; i < 36; i++ {
		var count int64
		result := db.Raw(`
			SELECT COUNT(*) 
			FROM information_schema.columns 
			WHERE table_name = ?`,
			table,
		).Scan(&count)

		if result.Error != nil {
			logging.RootLogger().Warn().Err(result.Error).Msg("Failed to check for table existence, retrying...")
			time.Sleep(5 * time.Second)
			continue
		}

		if count > 0 {
			logging.RootLogger().Info().Msg(fmt.Sprintf("Table %s exists in database %s", table, dbName))
			return
		}

		logging.RootLogger().Warn().Msg(fmt.Sprintf("Table %s does not exist in database %s, retrying...", table, dbName))
		time.Sleep(5 * time.Second)
	}

	panic(fmt.Sprintf("Table %s not found in database %s after multiple attempts", table, dbName))
}

func waitForDBColumn(connStr string, table string, column string) *gorm.DB {
	db := waitForDBTable(connStr, table)

	waitForColumnInDB(db, table, column)
	return db
}

func waitForColumnInDB(db *gorm.DB, table string, column string) {
	dbName := getDatabaseName(db)

	for i := 0; i < 36; i++ {
		var count int64
		result := db.Raw(`
			SELECT COUNT(*) 
			FROM information_schema.columns 
			WHERE table_name = ? 
			AND column_name = ?`,
			table, column,
		).Scan(&count)

		if result.Error != nil {
			logging.RootLogger().Warn().Err(result.Error).Msg("Failed to check for column existence, retrying...")
			time.Sleep(5 * time.Second)
			continue
		}

		if count > 0 {
			logging.RootLogger().Info().Msg(fmt.Sprintf("Column %s exists in table %s in database %s", column, table, dbName))
			return
		}

		logging.RootLogger().Warn().Msg(fmt.Sprintf("Column %s does not exist in table %s, retrying...", column, table))
		time.Sleep(5 * time.Second)
	}

	panic(fmt.Sprintf("Column %s not found in table %s in databse %s after multiple attempts", column, table, dbName))
}

func getDatabaseName(db *gorm.DB) string {
	var dbName string
	if err := db.Raw("SELECT current_database();").Scan(&dbName).Error; err != nil {
		panic(err)
	}
	return dbName
}
