package test_utils

import (
	"bufio"
	"encoding/json"
	"io"
	"os"
	"path/filepath"
	"strings"
)

func ReadFileFromTestData(filename string) string {
	path := filepath.Join("testdata", filename)
	file, err := os.Open(path)
	if err != nil {
		panic(err)
	}
	bb, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}
	return string(bb)
}

func MustReadFile(filename string) string {
	file, err := os.Open(filename)
	if err != nil {
		panic(err)
	}
	bb, err := io.ReadAll(file)
	if err != nil {
		panic(err)
	}
	return string(bb)
}

func MustReadLines(filename string) []string {
	file, err := os.Open(filename)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	var result []string
	scanner := bufio.NewScanner(file)

	// Set larger buffer if needed for long lines
	// Default buffer size is 64K
	// buffer := make([]byte, 0, 64*1024)
	// scanner.Buffer(buffer, 1024*1024)

	for scanner.Scan() {
		if line := strings.TrimSpace(scanner.Text()); line != "" {
			result = append(result, line)
		}
	}

	if err := scanner.Err(); err != nil {
		panic(err)
	}

	return result
}

func MustDeserializeJSON(filename string, out interface{}) {
	file, err := os.Open(filename)
	if err != nil {
		panic(err)
	}
	defer file.Close()
	if err := json.NewDecoder(file).Decode(out); err != nil {
		panic(err)
	}
}
