<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="API" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="contentmanager" />
    <working_directory value="$PROJECT_DIR$/go/servers/api" />
    <envs>
      <env name="DEBUG" value="1" />
      <env name="CM_E2E" value="1" />
      <env name="TIKA_SERVER_URL" value="http://localhost:9998/tika" />
    </envs>
    <kind value="DIRECTORY" />
    <package value="contentmanager/servers/api" />
    <directory value="$PROJECT_DIR$/go/servers/api" />
    <filePath value="$PROJECT_DIR$/go/servers/api/server.go|$PROJECT_DIR$/go/servers/api/server.go" />
    <method v="2" />
  </configuration>
</component>