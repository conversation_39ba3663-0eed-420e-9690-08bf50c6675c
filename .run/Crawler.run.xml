<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Crawler" type="GoApplicationRunConfiguration" factoryName="Go Application" singleton="false">
    <module name="contentmanager" />
    <working_directory value="$PROJECT_DIR$/go/servers/crawler" />
    <kind value="DIRECTORY" />
    <package value="contentmanager/servers/crawler" />
    <directory value="$PROJECT_DIR$/go/servers/crawler" />
    <filePath value="$PROJECT_DIR$/go/servers/crawler/server.go" />
    <method v="2" />
  </configuration>
</component>