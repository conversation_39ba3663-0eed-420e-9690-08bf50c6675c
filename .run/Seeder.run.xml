<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Seeder" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="contentmanager" />
    <working_directory value="$PROJECT_DIR$/go/servers/seeder" />
    <envs>
      <env name="DEBUG" value="1" />
      <env name="TIKA_SERVER_URL" value="http://127.0.0.1:9998/tika" />
    </envs>
    <kind value="DIRECTORY" />
    <package value="contentmanager/servers/seeder" />
    <directory value="$PROJECT_DIR$/go/servers/seeder" />
    <filePath value="$PROJECT_DIR$/go/servers/seeder/main.go" />
    <method v="2" />
  </configuration>
</component>