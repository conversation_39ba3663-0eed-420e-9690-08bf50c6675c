<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Indexer Content" type="GoApplicationRunConfiguration" factoryName="Go Application">
    <module name="contentmanager" />
    <working_directory value="$PROJECT_DIR$/go/servers/indexer-content" />
    <envs>
      <env name="DEBUG" value="1" />
      <env name="TIKA_SERVER_URL" value="http://127.0.0.1:9998/tika" />
    </envs>
    <kind value="DIRECTORY" />
    <package value="contentmanager/servers/indexer" />
    <directory value="$PROJECT_DIR$/go/servers/indexer-content" />
    <filePath value="$PROJECT_DIR$/go/servers/indexer/server.go" />
    <method v="2" />
  </configuration>
</component>