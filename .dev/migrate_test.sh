#!/bin/bash

# Set the environment variables if not already set
export PGHOST="************"
export PGUSER="postgres"
export PGPASSWORD=${PGPASSWORD}

WHITE_LIST=(
  "cm_blackgold"
  "cm_cbe"
  "cm_cssd"
  "cm_ctt"
  "cm_ecacs"
  "cm_ecsd"
  "cm_facebook"
  "cm_fmcs"
  "cm_grasslands"
  "cm_hfcrd"
  "cm_hpsd"
  "cm_hscsd"
  "cm_imagineeverything"
  "cm_limestone"
  "cm_livingsky"
  "cm_mhpsd"
  "cm_nwsd"
  "cm_peel"
  "cm_prairierose"
  "cm_rcsd"
  "cm_redeemer"
  "cm_sd22"
  "cm_sd23"
  "cm_sd27"
  "cm_sd91"
  "cm_yellowknifecatholic"
)

echo "Connecting to database: $PGHOST"

# Query the cm_multitenancy database to get the server and host column values from the tenant table
if ! psql_output=$(psql -h $PGHOST -t -A -F '|' -d cm_multitenancy -c "SELECT server, host FROM tenant;"); then
    echo "Failed to fetch server and host list from cm_multitenancy."
    exit 1
fi

# Loop through each server and host and perform operations
echo "$psql_output" | while IFS='|' read -r server host; do
  if [[ ! "${WHITE_LIST[*]}" == *"$server"* ]]; then
          # echo "Skipping database $server, as it does not match the required prefix."
          continue
      fi

  echo "----- $server at $host ----"

#  echo "----- REVERTING $server at $host ----"
#  sqitch --chdir "./database/src/tenant/Deploy" -v  revert -y --target "db:pg://${PGUSER}:${PGPASSWORD}@${host}/${server}" @HEAD^1

  echo "Deploying to database: $server"
  sqitch --chdir "./database/src/tenant/Deploy" deploy --verify --target "db:pg://${PGUSER}:${PGPASSWORD}@${host}/${server}"

  if [ $? -ne 0 ]; then
      echo "Deployment failed for database: $server"
      # Optional: stop deploying to other servers upon failure
      # break
  else
      echo "Deployment successful for database: $server"
  fi

done
